import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import models 1.0
import "../base"

/**
 * GridItemContextMenuCamera.qml - Context menu chuyên dụng cho camera
 *
 * CHỨC NĂNG CHÍNH:
 * - <PERSON><PERSON> thừa tất cả tính năng từ GridItemContextMenuBase
 * - Thêm menu "Bài toán AI" (Recognition, Protection, Frequency)
 * - Thêm menu "Luồng video" (Main Stream/Sub Stream)
 * - Thêm menu "Cài đặt"
 * - Dành riêng cho camera grid items
 */
GridItemContextMenuBase {
    id: root
    signal actionTriggered(var key,var direction)
    property var itemData: null
    // ✅ INHERITANCE: Kế thừa tất cả từ GridItemContextMenuBase
    // Tự động có: gridItem, gridModel, position, itemType, isDarkTheme
    // Tự động có: menu "Mở camera sang...", "Xóa khỏi view", "Toàn màn hình"


    
    // Bài toán AI menu
    Menu {
        id: aiMenu
        title: "Bài toán AI"
        icon.source: root.icon_ai_flow
        width: 200

        background: Rectangle {
            color: root.menuBackgroundColor
            border.color: root.menuBorderColor
            border.width: 1
            radius: 4
        }

        MenuItem {
            text: "Nhận diện"
            checkable: true

            background: Rectangle {
                color: root.menuBackgroundColor
                border.color: parent.hovered ? root.menuHoverColor : "transparent"
                border.width: 2
                radius: 2
            }

            contentItem: Text {
                text: parent.text
                color: root.menuTextColor
                font.pixelSize: 14
                leftPadding: 8
                rightPadding: 8
                verticalAlignment: Text.AlignVCenter
            }

            onTriggered: {
                console.log("[CAMERA_MENU] AI Recognition clicked - position:", root.position)
                // TODO: Implement AI recognition toggle
            }
        }

        MenuItem {
            text: "Bảo vệ"
            checkable: true

            background: Rectangle {
                color: root.menuBackgroundColor
                border.color: parent.hovered ? root.menuHoverColor : "transparent"
                border.width: 2
                radius: 2
            }

            contentItem: Text {
                text: parent.text
                color: root.menuTextColor
                font.pixelSize: 14
                leftPadding: 8
                rightPadding: 8
                verticalAlignment: Text.AlignVCenter
            }

            onTriggered: {
                console.log("[CAMERA_MENU] AI Protection clicked - position:", root.position)
                // TODO: Implement AI protection toggle
            }
        }

        MenuItem {
            text: "Tần suất"
            checkable: true

            background: Rectangle {
                color: root.menuBackgroundColor
                border.color: parent.hovered ? root.menuHoverColor : "transparent"
                border.width: 2
                radius: 2
            }

            contentItem: Text {
                text: parent.text
                color: root.menuTextColor
                font.pixelSize: 14
                leftPadding: 8
                rightPadding: 8
                verticalAlignment: Text.AlignVCenter
            }

            onTriggered: {
                console.log("[CAMERA_MENU] AI Frequency clicked - position:", root.position)
                // TODO: Implement AI frequency toggle
            }
        }
    }

    MenuSeparator {
        background: Rectangle {
            color: root.menuSeparatorColor
            height: 1
        }
    }

    // Luồng video menu
    Menu {
        id: streamMenu
        title: "Luồng video"
        icon.source: root.icon_video_stream
        width: 200

        background: Rectangle {
            color: root.menuBackgroundColor
            border.color: root.menuBorderColor
            border.width: 1
            radius: 4
        }

        // Radio Group for Stream Mode Selection
        property string currentStreamMode: itemData ? itemData.streamMode : "AUTO"

        MenuItem {
            text: "Tự động"
            checkable: true
            checked: streamMenu.currentStreamMode === "AUTO"
            indicator: null

            contentItem: Row {
                spacing: 8
                leftPadding: 8
                rightPadding: 8
                anchors.verticalCenter: parent.verticalCenter

                // Radio button circle
                Rectangle {
                    width: 16; height: 16
                    radius: 8
                    border.color: root.menuTextColor
                    border.width: 1
                    color: "transparent"
                    anchors.verticalCenter: parent.verticalCenter

                    Rectangle {
                        width: 8; height: 8
                        radius: 4
                        color: root.menuTextColor
                        anchors.centerIn: parent
                        visible: parent.parent.parent.checked
                    }
                }

                Text {
                    text: parent.parent.text
                    color: root.menuTextColor
                    font.pixelSize: 14
                    verticalAlignment: Text.AlignVCenter
                }
            }

            onTriggered: {
                if (itemData) {
                    itemData.setStreamModeFromQML("AUTO")
                    streamMenu.currentStreamMode = "AUTO"
                }
            }
        }

        MenuItem {
            text: "Cao (Main)"
            checkable: true
            checked: streamMenu.currentStreamMode === "MAIN_FIXED"
            indicator: null

            contentItem: Row {
                spacing: 8
                leftPadding: 8
                rightPadding: 8
                anchors.verticalCenter: parent.verticalCenter

                // Radio button circle
                Rectangle {
                    width: 16; height: 16
                    radius: 8
                    border.color: root.menuTextColor
                    border.width: 1
                    color: "transparent"
                    anchors.verticalCenter: parent.verticalCenter

                    Rectangle {
                        width: 8; height: 8
                        radius: 4
                        color: root.menuTextColor
                        anchors.centerIn: parent
                        visible: parent.parent.parent.checked
                    }
                }

                Text {
                    text: parent.parent.text
                    color: root.menuTextColor
                    font.pixelSize: 14
                    verticalAlignment: Text.AlignVCenter
                }
            }

            onTriggered: {
                if (itemData) {
                    itemData.setStreamModeFromQML("MAIN_FIXED")
                    streamMenu.currentStreamMode = "MAIN_FIXED"
                }
            }
        }

        MenuItem {
            text: "Thấp (Sub)"
            checkable: true
            checked: streamMenu.currentStreamMode === "SUB1_FIXED"
            indicator: null

            contentItem: Row {
                spacing: 8
                leftPadding: 8
                rightPadding: 8
                anchors.verticalCenter: parent.verticalCenter

                // Radio button circle
                Rectangle {
                    width: 16; height: 16
                    radius: 8
                    border.color: root.menuTextColor
                    border.width: 1
                    color: "transparent"
                    anchors.verticalCenter: parent.verticalCenter

                    Rectangle {
                        width: 8; height: 8
                        radius: 4
                        color: root.menuTextColor
                        anchors.centerIn: parent
                        visible: parent.parent.parent.checked
                    }
                }

                Text {
                    text: parent.parent.text
                    color: root.menuTextColor
                    font.pixelSize: 14
                    verticalAlignment: Text.AlignVCenter
                }
            }

            onTriggered: {
                if (itemData) {
                    itemData.setStreamModeFromQML("SUB1_FIXED")
                    streamMenu.currentStreamMode = "SUB1_FIXED"
                }
            }
        }

        // Update currentStreamMode when itemData changes
        Connections {
            target: itemData
            function onStreamModeChanged() {
                if (itemData) {
                    streamMenu.currentStreamMode = itemData.streamMode
                }
            }
        }
    }

    MenuSeparator {
        background: Rectangle {
            color: root.menuSeparatorColor
            height: 1
        }
    }

    // Cài đặt menu item
    MenuItem {
        id: settingsMenuItem
        text: "Cài đặt\tI"

        background: Rectangle {
            color: root.menuBackgroundColor
            border.color: settingsMenuItem.hovered ? root.menuHoverColor : "transparent"
            border.width: 2
            radius: 2
        }

        contentItem: Row {
            spacing: 8
            leftPadding: 8
            rightPadding: 8

            Image {
                source: root.icon_setting
                width: 16
                height: 16
                anchors.verticalCenter: parent.verticalCenter
                fillMode: Image.PreserveAspectFit
            }

            Text {
                text: settingsMenuItem.text
                color: root.menuTextColor
                font.pixelSize: 14
                anchors.verticalCenter: parent.verticalCenter
            }
        }

        onTriggered: {
            console.log("[CAMERA_MENU] Settings clicked - position:", root.position)
            if (gridModel) {
                gridModel.showCameraInfo(root.position)
            } else {
                console.log("[CAMERA_MENU] Error: gridModel not available")
            }
        }
    }

    MenuSeparator {
        background: Rectangle {
            color: root.menuSeparatorColor
            height: 1
        }
    }

    // Fullscreen action - ở cuối cùng
    MenuItem {
        id: fullscreenMenuItem
        text: "Toàn màn hình\tEnter"
        enabled: true

        background: Rectangle {
            color: root.menuBackgroundColor
            border.color: fullscreenMenuItem.hovered ? root.menuHoverColor : "transparent"
            border.width: 2
            radius: 2
        }

        contentItem: Row {
            spacing: 8
            leftPadding: 8
            rightPadding: 8

            Image {
                source: root.icon_full_screen
                width: 16
                height: 16
                anchors.verticalCenter: parent.verticalCenter
                fillMode: Image.PreserveAspectFit
            }

            Text {
                text: fullscreenMenuItem.text
                color: root.menuTextColor
                font.pixelSize: 14
                anchors.verticalCenter: parent.verticalCenter
            }
        }

        onTriggered: {
            // ✅ CENTRALIZED: Use reusable fullscreen handler
            if (gridItem && gridItem.itemData && gridItem.animationManager) {
                var targetState = !gridItem.itemData.fullscreen
                gridItem.animationManager.handleFullscreenTransition(
                    gridItem, targetState, "CONTEXT_MENU_FULLSCREEN"
                )
            } else {
                console.warn("[CONTEXT_MENU] AnimationManager or gridItem not available, using direct toggle")
                if (itemData) {
                    itemData.fullscreen = !itemData.fullscreen
                }
            }
        }
    }
}
