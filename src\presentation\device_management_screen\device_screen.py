import logging
from src.common.widget.custom_titlebar.custom_titlebar_with_tab import CustomTitleBarWithTab
import os

from typing import List
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QFileDialog, QLabel, QPushButton,QMenu, QDialog, \
    QStackedWidget, QTabWidget
from PySide6.QtQuickWidgets import QQuickWidget
from PySide6.QtGui import QIcon,QGuiApplication,QAction
from PySide6.QtCore import Qt, Signal, QCoreApplication,QUrl
from src.presentation.device_management_screen.widget.device_table import DeviceTable
from src.presentation.device_management_screen.widget.device_group_table import DeviceGroupTable
from src.common.model.group_model import Group,GroupModel,group_model_manager
from src.common.model.camera_model import Camera, filter_camera_model, camera_model_manager,CameraModel, AddCameraType
from .widget.list_custom_widgets import <PERSON><PERSON><PERSON><PERSON>on, SearchComboBox
from src.common.model.device_models import FilterType
from src.common.widget.dialogs.add_camera_dialog import AddCameraDialog
from src.common.widget.dialogs.warning_dialog import WarningDialog
# from src.common.widget.dialogs.add_integrated_device_dialog import AddIntegratedDeviceDialog
from src.utils.utils import Utils
import json
from src.styles.style import Style
from src.utils.file import SelectFile,SaveFile
from src.common.widget.notifications.notify import Notifications
from src.common.widget.search_widget.search_bar import SearchBar
from src.common.controller.main_controller import main_controller,connect_slot
from src.common.controller.controller_manager import Controller,controller_manager
from src.common.widget.dialogs.add_group_dialog import AddGroupDialog
from src.common.model.door_model import DoorModel,door_model_manager
from src.common.widget.notifications.listen_message_notifications import listen_show_notification

logger = logging.getLogger(__name__)
class DeviceScreen(QWidget):
    language_changed = Signal()

    def __init__(self, parent=None, window_parent=None):
        super(DeviceScreen, self).__init__(parent)
        self.icon_camera_group = None
        self.new_add_camera_dialog = None
        self.parent = parent
        self.window_parent = window_parent
        main_controller.list_parent['DeviceScreen'] = self

        self.health_check_camera_thread = None

        self.calculate_layout()

        self.main_layout = QVBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.main_layout.setContentsMargins(4, 0, 2, 4)
        self.main_layout.setSpacing(0)

        self.title_bar = CustomTitleBarWithTab(parent=self, window_parent=self.window_parent, is_show_tab_bar=True)
        self.title_bar.setFixedHeight(40)
        self.title_bar.signal_change_tab.connect(self.on_tab_change)
        self.main_layout.addWidget(self.title_bar)

        self.layout_content = QVBoxLayout()
        self.layout_content.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_content.setContentsMargins(0, 0, 0, 0)
        self.stacked_content = QStackedWidget()
        self.stacked_content.setContentsMargins(0, 0, 0, 0)
        self.stacked_content.setObjectName("stacked_content")

        self.layout_content.addWidget(self.stacked_content)
        self.quick_widget = QQuickWidget()
        # logger.debug(f'init qml')
        self.quick_widget.setSource(QUrl("qrc:src/common/qml/device_table/Init.qml"))
        self.quick_widget.setResizeMode(QQuickWidget.SizeRootObjectToView)
        self.layout_content.addWidget(self.quick_widget)

        self.main_layout.addLayout(self.layout_content)
        self.setLayout(self.main_layout)

        self.setObjectName("device_screen_widget")
        # self.setStyleSheet(
        #             f'''
        #             QWidget#device_screen_widget {{
        #                 background-color: {Style.PrimaryColor.on_background};
        #                 color: Style.PrimaryColor.white;
        #             }}
        #             QLabel {{
        #                 color: {Style.PrimaryColor.white_2};
        #             }}
        #             QStandardItem {{
        #                 color: #000000;
        #             }}
        #             '''
        #         )
        self.image_list ={}
        self.connect_slot()
        self.restyle_device_screen()

    def connect_slot(self):
        connect_slot(
            (main_controller.complete_fetching_data, self.complete_fetching_data),
            (controller_manager.exit_controller_signal,self.exit_controller_signal)
        )

    def complete_fetching_data(self,data):
        controller: Controller = data
        main_controller.current_controller = controller
        # add Widget truoc khi add tab vao tab bar de dam bao wigdet da duoc add thanh cong
        # khi do moi co widget de co the setCurrentIndex
        tab_device_widget = TabDeviceWidget(controller=controller)
        self.stacked_content.addWidget(tab_device_widget)
        self.title_bar.add_Tab(controller.server.data.server_ip)
        self.title_bar.tab_widget.tabBar().show()
        self.stacked_content.setCurrentIndex(self.title_bar.tab_widget.tabBar().currentIndex())
        self.tab_device_widget.update_data_to_table()

    def exit_controller_signal(self,data):
        # Xử lý dữ liêu và UI khi disconnect server 
        # Xóa UI stackwidget
        controller:Controller = data
        for idx in range(self.title_bar.tab_widget.count()):
            if self.title_bar.tab_widget.tabText(idx) == controller.server.data.server_ip:
                # logger.debug(f'controller.server.data.server_ip = {controller.server.data.server_ip}')
                widget = self.stacked_content.widget(idx)
                self.stacked_content.removeWidget(widget)
                widget.deleteLater()
                self.title_bar.tab_widget.tabBar().tabCloseRequested.emit(idx)
                break
        self.title_bar.tab_widget.tabBar().show()

    def on_tab_change(self, index):
        # logger.debug(f'on_tab_change = {index}')
        # Show the corresponding content widget when a tab is selected
        self.stacked_content.setCurrentIndex(index)
        self.tab_device_widget = self.stacked_content.currentWidget()
        if self.tab_device_widget is not None:
            main_controller.current_controller = self.tab_device_widget.controller

    def calculate_layout(self, desktop_screen_size=None):
        if desktop_screen_size is None:
            screen = QGuiApplication.primaryScreen()
            desktop_screen_size = screen.availableGeometry()
        screen_available_width = desktop_screen_size.width()
        screen_available_height = desktop_screen_size.height()
        self.control_width = 0.97 * screen_available_width
        self.control_height = 0.08 * screen_available_height
        self.table_height = 0.85 * screen_available_height
        self.table_width = 0.96 * screen_available_width

    def stop_app(self):
        if self.health_check_camera_thread is not None and self.health_check_camera_thread.isRunning():
            self.health_check_camera_thread.quit()
            # self.health_check_camera_thread.wait()

    def retranslate_device_screen(self):
        for i in range(self.stacked_content.count()):
            widget = self.stacked_content.widget(i)
            if isinstance(widget, TabDeviceWidget):
                widget.retranslate_device_widget()

    def restyle_device_screen(self):
        # self.stacked_content.setStyleSheet(f'''
        #     QWidget#stacked_content {{
        #         border-top: 1px solid {Style.PrimaryColor.border_line_edit_not_focus};
        #     }}
        #     ''')
        self.setStyleSheet(
                    f'''
                    QWidget#device_screen_widget {{
                        background-color: {main_controller.get_theme_attribute( "Color" , "main_background")};
                        color: {main_controller.get_theme_attribute( "Color" , "label_title_1")};
                    }}
                    QLabel {{
                        color: {main_controller.get_theme_attribute( "Color" , "label_title_1")};
                    }}
                    QStandardItem {{
                        color: #000000;
                    }}
                    '''
                )
        self.stacked_content.setStyleSheet(f'''
            QWidget#stacked_content {{
             background-color: {main_controller.get_theme_attribute( "Color" , "main_background")};
                border-top: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
            }}
            ''')

        if self.stacked_content.currentWidget() is not None:
            self.stacked_content.currentWidget().set_dynamic_stylesheet()

        self.title_bar.set_dynamic_stylesheet()

class TabDeviceWidget(QWidget):
    def __init__(self, parent=None, controller:Controller=None):
        super().__init__(parent)
        self.controller = controller
        self.control_height = None
        self.table_height = None
        self.table_width = None
        self.control_width = None
        self.calculate_layout()
        self.connect_slot()
        self.load_ui()
        self.set_dynamic_stylesheet()

    def calculate_layout(self, desktop_screen_size=None):
        current_screen = self.screen()
        if current_screen:
            screen_geometry = current_screen.geometry()
            screen_width = screen_geometry.width()
            screen_height = screen_geometry.height()
            self.control_width = 0.97 * screen_width
            self.control_height = 0.08 * screen_height
            self.table_height = 0.85 * screen_height
            self.table_width = 0.96 * screen_width

    def load_ui(self):

        self.widget_camera = QWidget()
        self.layout_camera = QVBoxLayout()
        self.layout_camera.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.setup_ui_cameras()

        self.widget_camera_group = QWidget()
        self.layout_camera_group = QVBoxLayout()
        self.layout_camera_group.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.setup_ui_groups()

        self.tab_content_widget = QTabWidget()
        self.tab_content_widget.addTab(self.widget_camera_group, self.tr('Device Group'))
        self.tab_content_widget.addTab(self.widget_camera, self.tr('Device'))

        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)

        self.main_layout.addWidget(self.tab_content_widget)

        self.setLayout(self.main_layout)

    def connect_slot(self):
        connect_slot(
            (main_controller.listen_ws_add_camera_signal,self.listen_ws_add_camera_signal),
            (group_model_manager.add_group_signal,self.add_group_signal),
            (group_model_manager.delete_group_model_signal,self.delete_group_model),
            (camera_model_manager.delete_camera_model_signal,self.delete_camera_model),
            (camera_model_manager.add_camera_signal,self.add_camera_signal),
            (camera_model_manager.add_cameras_signal,self.add_cameras_signal),
            (door_model_manager.add_door_signal,self.add_door_signal),
            (main_controller.delete_groups_signal,self.delete_groups),
        )

    def setup_ui_cameras(self):
        self.widget_camera.setObjectName("widget_camera")
        control_widget = QWidget()
        self.layout_btn = QHBoxLayout(control_widget)
        self.layout_btn.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_btn.setContentsMargins(10, 0, 0, 0)
        ################### Control Button ######################
        self.btn_add = QPushButton(self.tr("Add Camera"))
        self.btn_add.setStyleSheet(Style.PrimaryStyleSheet.get_add_button_style(theme_instance=main_controller))
        self.btn_add.setFixedHeight(40)
        self.btn_add.setIcon(QIcon(main_controller.get_theme_attribute("Image", "add_button_icon")))
        self.btn_add.clicked.connect(self.add_clicked)
        # self.btn_add.enterEvent = self.show_menu

        # self.btn_import_file = IconButton(icon=QIcon(main_controller.get_theme_attribute("Image", "upload_file")), style=Style.PrimaryStyleSheet.get_button_style4(theme_instance=main_controller),
        #                                   tool_tip='Import file')
        # self.btn_import_file.clicked.connect(self.import_file_clicked)

        # self.btn_export_file = IconButton(icon=QIcon(main_controller.get_theme_attribute("Image", "download_file")),style=Style.PrimaryStyleSheet.get_button_style4(theme_instance=main_controller),
        #                                   tool_tip='Export file')
        # self.btn_export_file.clicked.connect(self.export_file_clicked)

        # self.btn_delete_camera = IconButton(icon=QIcon(Style.PrimaryImage.trash_mini), style=Style.StyleSheet.button_style4, tool_tip='Delete')
        # self.btn_delete_camera.clicked.connect(self.delete_camera_clicked)
        # self.btn_delete_camera.hide()

        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(10, 10, 10, 10)
        button_layout.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignBottom)
        button_layout.setSpacing(10)

        # button_layout.addWidget(self.btn_delete_camera)
        # button_layout.addWidget(self.btn_export_file)
        # button_layout.addWidget(self.btn_import_file)
        button_layout.addWidget(self.btn_add)

        self.search_camera_widget = SearchBar(parent=self, title=self.tr("Search"))
        self.search_camera_widget.search_bar.setPlaceholderText(self.tr("Enter Camera Name"))
        self.search_camera_widget.search_bar.setFixedHeight(40)
        # self.search_camera_widget.label_title.setStyleSheet(f"color: {Style.PrimaryColor.white_2};")
        self.search_camera_widget.search_items_signal.connect(self.search_device_name)

        self.search_group = SearchComboBox(title=self.tr('Group'), data=None,
                                           combobox_clicked=self.search_device_group_clicked)
        self.search_group.combobox.setFixedHeight(40)

        self.search_status = SearchComboBox(title=self.tr('Status'),
                                            data=[self.tr('All'), self.tr('Online'), self.tr('Offline')],
                                            combobox_clicked=self.search_status_camera_clicked)
        self.search_status.combobox.setFixedHeight(40)

        data = [
            self.tr('All'),
            self.tr('Face'),
            self.tr('Vehicle'),
            # self.tr('Crowd'),
            # self.tr('Access Control'),
        ]
        self.search_ai = SearchComboBox(title=self.tr('AI'), data=data,
                                        combobox_clicked=self.search_ai_clicked)
        self.search_ai.combobox.setFixedHeight(40)
        data = [
            self.tr('All'),
            self.tr('Camera'),
            self.tr('AIBox'),
            self.tr('Door'),
        ]
        self.search_device_type = SearchComboBox(title=self.tr('Device Type'), data=data,
                                        combobox_clicked=self.search_devicetype_clicked)
        self.search_device_type.combobox.setFixedHeight(40)

        ##################################################
        self.layout_btn.addWidget(self.search_camera_widget)
        self.layout_btn.addWidget(self.search_group)
        self.layout_btn.addWidget(self.search_status)
        self.layout_btn.addWidget(self.search_ai)
        self.layout_btn.addWidget(self.search_device_type)
        self.layout_btn.addWidget(button_widget)
        self.layout_btn.setStretch(0, 20)
        self.layout_btn.setStretch(1, 20)
        self.layout_btn.setStretch(2, 20)
        self.layout_btn.setStretch(3, 20)
        self.layout_btn.setStretch(4, 20)
        self.device_table = DeviceTable(parent=self,controller = self.controller)
        self.layout_camera.addWidget(control_widget)
        self.layout_camera.addWidget(self.device_table)
        self.layout_camera.setContentsMargins(0, 0, 0, 0)
        self.layout_camera.setSpacing(0)
        self.widget_camera.setLayout(self.layout_camera)

    def setup_ui_groups(self):
        self.widget_camera_group.setObjectName('widget_camera_group')

        # self.layout_btn_group = QHBoxLayout()
        # self.layout_btn_group.setContentsMargins(10, 0, 0, 0)
        control_widget = QWidget()
        self.layout_btn_group = QHBoxLayout(control_widget)
        self.layout_btn_group.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_btn_group.setContentsMargins(10, 0, 0, 0)

        self.btn_add_group = QPushButton(self.tr('Add Group'))
        self.btn_add_group.setStyleSheet(Style.PrimaryStyleSheet.get_add_button_style(theme_instance=main_controller))
        self.btn_add_group.setFixedHeight(40)
        self.btn_add_group.setIcon(QIcon(main_controller.get_theme_attribute("Image", "add_button_icon")))
        self.btn_add_group.clicked.connect(self.add_camera_group_clicked)

        self.btn_delete_group = IconButton(icon= QIcon(Style.PrimaryImage.trash_mini), style=Style.StyleSheet.button_style4, tool_tip='Delete Group')
        self.btn_delete_group.clicked.connect(self.delete_group_clicked)
        self.btn_delete_group.hide()

        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(10, 10, 10, 10)
        button_layout.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignBottom)
        button_layout.setSpacing(10)
        button_layout.addWidget(self.btn_delete_group)
        button_layout.addWidget(self.btn_add_group)
        ############ Search ##############################
        self.search_camera_group_widget = SearchBar(parent=self, title=self.tr("Search"))
        self.search_camera_group_widget.search_bar.setPlaceholderText(self.tr("Enter Group Name"))
        self.search_camera_group_widget.search_bar.setFixedHeight(40)
        # self.search_camera_group_widget.label_title.setStyleSheet(f"color: {Style.PrimaryColor.white_2};")
        self.search_camera_group_widget.search_items_signal.connect(self.search_device_group_name)
        data = [
            self.tr('All'),
            self.tr('Face'),
            self.tr('Vehicle'),
            # self.tr('Crowd'),
            # self.tr('Access Control'),
        ]
        self.search_ai_group = SearchComboBox(title=self.tr('AI'), data=data,
                                              combobox_clicked=self.search_ai_group_clicked)
        self.search_ai_group.combobox.setFixedHeight(40)

        ##################################################
        self.layout_btn_group.addWidget(self.search_camera_group_widget)
        self.layout_btn_group.addWidget(self.search_ai_group)
        self.layout_btn_group.addWidget(QWidget())
        self.layout_btn_group.addWidget(QWidget())
        self.layout_btn_group.addWidget(button_widget)
        self.layout_btn_group.setStretch(0, 20)
        self.layout_btn_group.setStretch(1, 20)
        self.layout_btn_group.setStretch(2, 20)
        self.layout_btn_group.setStretch(3, 20)
        self.layout_btn_group.setStretch(4, 20)
        self.device_group_table = DeviceGroupTable(parent=self, controller=self.controller)
        self.layout_camera_group.addWidget(control_widget)
        self.layout_camera_group.addWidget(self.device_group_table)
        self.layout_camera_group.setContentsMargins(0, 0, 0, 0)
        self.layout_camera_group.setSpacing(0)
        self.widget_camera_group.setLayout(self.layout_camera_group)

    def update_data_to_table(self):
        list_cameras = camera_model_manager.get_camera_list(server_ip = main_controller.current_controller.server.data.server_ip)
        list_groups = group_model_manager.get_group_list(server_ip = main_controller.current_controller.server.data.server_ip)
        # add camera data
        self.get_camera_parameters(list_camera=list_cameras)
        self.device_table.update_data_to_table(is_thread=False)

        # add group data
        name = QCoreApplication.translate("DeviceScreen", u"All",None)
        self.device_table.camera_group_list = [{'id':None, 'data': name}]
        self.search_group.combobox.addItem(self.device_table.camera_group_list[0]['data'])
        for group_model in list_groups.values():
            if group_model.get_property("type") != 'AI_BOX':
                item = {'id': group_model.get_property('id'),'data':group_model}
                group_model.change_model.connect(self.update_combobox_group_list)
                self.device_table.camera_group_list.append(item)
                self.search_group.combobox.addItem(item['data'].get_property('name'))
        self.device_group_table.update_data_to_table(is_thread=False)

    def listen_ws_add_camera_signal(self, data):
        # logger.debug(f"listening = {data}")
        data_event, socket_id = data
        if socket_id == main_controller.current_controller.id_event_search_camera:
            if self.new_add_camera_dialog is not None:
                self.new_add_camera_dialog.add_result_search_camera_to_table(data_event)

    def add_group_signal(self, data=None):
        group_model, is_insert_group = data
        # self.device_table.camera_group_list.append({'id': group_model.get_property('id'),'data':group_model})
        # self.search_group.combobox.addItem(group_model.data.name)
        # text = self.search_camera_group_widget.search_bar.text()
        # ai_index = self.search_ai_group.combobox.currentIndex()
        # if ai_index != 0:
        #     self.device_group_table.filter_ai = True
        self.device_group_table.put_filter_queue({FilterType.SearchCameraName: None,FilterType.SearchCameraAI:None})

    def add_tracking_group_signal(self, data):
        tracking_model = data
        # self.device_table.camera_group_list.append({'id': group_model.get_property('id'), 'data': group_model})
        # self.search_group.combobox.addItem(group_model.data.name)
        text = self.search_tracking_group_widget.search_bar.text()
        self.tracking_group_table.put_filter_queue(text)

    def delete_group_model(self, group_list:List[GroupModel] = []):
        # for group in group_list:
        #     for index,item in enumerate(self.device_table.camera_group_list):
        #         if group.get_property('id') == item['id']:
        #             self.device_table.camera_group_list.remove(item)
        #             break
        #     for idx in range(self.search_group.combobox.count()):
        #         if group.data.name == self.search_group.combobox.itemText(idx):
        #             if self.search_group.combobox.currentIndex() == idx:
        #                 text = self.search_camera_widget.search_bar.text()
        #                 self.device_table.filter_device_group = False
        #                 self.search_group.combobox.setCurrentIndex(0)
        #                 self.device_table.put_filter_queue({FilterType.SearchCameraName: text,FilterType.SearchCameraGroup: None,FilterType.SearchCameraAI:None,FilterType.SearchCameraStatus: None})
        #             self.search_group.combobox.removeItem(idx)
        #             break
        # text = self.search_camera_group_widget.search_bar.text()
        # ai_index = self.search_ai_group.combobox.currentIndex()
        # if ai_index != 0:
        #     self.device_group_table.filter_ai = True
        self.device_group_table.put_filter_queue({FilterType.SearchCameraName: None,FilterType.SearchCameraAI:None})

    def add_camera_signal(self, camera:CameraModel = None):
        # Cap nhat get_camera_parameters
        camera_list = camera_model_manager.get_camera_list()
        self.get_camera_parameters(camera_list)
        text = self.search_camera_widget.search_bar.text()
        camera_group_index = self.search_group.combobox.currentIndex()
        if camera_group_index != -1 and camera_group_index != 0:
            self.device_table.filter_device_group = True
        ai_index = self.search_ai.combobox.currentIndex()
        if ai_index != 0:
            self.device_table.filter_ai = True
        status_camera_index = self.search_status.combobox.currentIndex()
        if status_camera_index != 0:
            self.device_table.filter_status = True
        self.device_table.put_filter_queue({FilterType.SearchCameraName: text,FilterType.SearchCameraGroup: None,FilterType.SearchCameraAI:None,FilterType.SearchCameraStatus: None})

    def add_cameras_signal(self, data):
        # Cap nhat get_camera_parameters
        controller,camera_list = data
        camera_list = camera_model_manager.get_camera_list(server_ip=controller.server.data.server_ip)
        self.get_camera_parameters(camera_list)
        text = self.search_camera_widget.search_bar.text()
        camera_group_index = self.search_group.combobox.currentIndex()
        if camera_group_index != 0:
            self.device_table.filter_device_group = True
        ai_index = self.search_ai.combobox.currentIndex()
        if ai_index != 0:
            self.device_table.filter_ai = True
        status_camera_index = self.search_status.combobox.currentIndex()
        if status_camera_index != 0:
            self.device_table.filter_status = True
        self.device_table.put_filter_queue({FilterType.SearchCameraName: text,FilterType.SearchCameraGroup: None,FilterType.SearchCameraAI:None,FilterType.SearchCameraStatus: None})

    def delete_camera_model(self,camera_list:List[CameraModel] = None):
        text = self.search_camera_widget.search_bar.text()
        camera_group_index = self.search_group.combobox.currentIndex()
        if camera_group_index != 0 and camera_group_index != -1:
            self.device_table.filter_device_group = True
        ai_index = self.search_ai.combobox.currentIndex()
        if ai_index != 0:
            self.device_table.filter_ai = True
        status_camera_index = self.search_status.combobox.currentIndex()
        if status_camera_index != 0:
            self.device_table.filter_status = True
        self.device_table.put_filter_queue({FilterType.SearchCameraName: text,FilterType.SearchCameraGroup: None,FilterType.SearchCameraAI:None,FilterType.SearchCameraStatus: None})

    def delete_camera_clicked(self):
        pass
        # tam thoi comment do chua ho tro xoa nhieu camera
        id = []
        camera_list = camera_model_manager.get_camera_list(server_ip=main_controller.current_controller.server.data.server_ip)
        for item in camera_list.values():
            if item.data.check_box:
                id.append(item.get_property('id'))
        camera = Camera(id=id)
        # logger.debug(f'delete_camera_clicked = {camera}')
        dialog = WarningDialog()
        result = dialog.exec()
        if result == QDialog.Accepted:

            self.main_controller.delete_camera(data= camera)
            self.btn_delete_camera.hide()
            # self.main_controller.progress_dialog.exec()
        elif result == QDialog.Rejected:
            pass

    def add_door_signal(self, door:DoorModel = None):
        self.device_table.put_filter_queue({FilterType.SearchCameraName: None, FilterType.SearchCameraGroup: None, FilterType.SearchCameraAI: None,
                 FilterType.SearchCameraStatus: None})

    def delete_tracking_group_clicked(self):
        pass

    def delete_group_clicked(self):
        id = []
        group_list = group_model_manager.get_group_list(server_ip=main_controller.current_controller.server.data.server_ip)
        for item in group_list.values():
            if item.data.check_box:
                id.append(item.get_property('id'))
        group = Group(id=id)
        dialog = WarningDialog()
        result = dialog.exec()
        if result == QDialog.Accepted:
            main_controller.current_controller.delete_camera_group(data= group.to_dict())
            self.btn_delete_group.hide()
            # self.main_controller.progress_dialog.exec()
        elif result == QDialog.Rejected:
            pass
        # self.btn_delete_group.hide()
        # self.all_checkbox_group.setCheckState(Qt.CheckState.Unchecked)

    # Add device callback
    def add_tracking_group_clicked(self):
        # self.add_tracking_group_dialog = AddTracingScriptDialog(title=self.tr('Add Tracing Script'))
        self.add_tracking_group_dialog = ConfigTracingScriptDialog(parent=self, title=self.tr('ADD TRACING SCRIPT'),
                                                                   is_config_dialog=False, current_controller=self.controller)
        self.add_tracking_group_dialog.exec()
    def toggled(self):
        logger.debug(f'toggled')

    def add_clicked(self):
        self.new_add_camera_dialog = AddCameraDialog(parent=self)
        self.new_add_camera_dialog.exec()
        # pass

    def show_menu(self, event):
        position = event.globalPos()
        self.menu = QMenu()
        camera_action = QAction(icon=QIcon(Style.PrimaryImage.Change_mode),
                                 text=self.tr("ADD CAMERA"), parent=self.menu)
        camera_action.triggered.connect(lambda: self.menu_clicked(0))

        integrated_device_action = QAction(icon=QIcon(Style.PrimaryImage.Change_mode),
                                 text=self.tr("ADD INTEGRATED DEVICE"), parent=self.menu)
        integrated_device_action.triggered.connect(lambda: self.menu_clicked(1))
        # Disable the integrated device action
        integrated_device_action.setEnabled(False)
        
        self.menu.addAction(camera_action)
        self.menu.addAction(integrated_device_action)
        self.menu.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.menu.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.menu.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        self.menu.exec_(position)

    def menu_clicked(self,state):
        if state == 0:
            self.new_add_camera_dialog = AddCameraDialog(parent=self)
            self.new_add_camera_dialog.exec()
        elif state == 1:
            # tung.vu: comment do chưa hỗ trợ thêm device tích hợp
            # self.add_integrated_device_dialog = AddIntegratedDeviceDialog(parent=self)
            # self.add_integrated_device_dialog.exec()

            # show dialog -> đang trong quá trình phát triển       
            # ví dụ cách dùng listen_show_notification.listen_API_fail_message_signal.emit(
            #             (None, TypeRequestVMS.APPLY_AIFLOW, "AI_FLOW_NOT_APPLIED_WITHOUT_ZONE"))
            listen_show_notification.listen_API_fail_message_signal.emit(
                (None, None, self.tr("This feature is under development")))
            pass

    def add_camera_group_clicked(self):
        self.add_camera_group_dialog = AddGroupDialog(parent=self, is_add_dialog=True)
        self.add_camera_group_dialog.exec()
        # group_list = group_model_manager.get_group_list(server_ip = self.controller.server.data.server_ip)
        # for group in group_list.values():
        #     if group.data.name == 'test1':
        #         main_controller.current_controller.delete_camera_group(data=group.data)
    # Import/Export
    def export_file_clicked(self):
        list_cameras = camera_model_manager.get_camera_list()
        save_file = SaveFile(list_cameras=list_cameras)
        save_file.callback_save_file_result = self.callback_save_file_result
        result = save_file.add_object_to_file()
        if result:
            save_file.save_workbook()

    def callback_save_file_result(self, result):
        if result:
            Notifications(parent=self, title=self.tr('Successfully Downloaded File'), icon=Style.PrimaryImage.sucess_result)
        else:
            Notifications(parent=self, title=self.tr('File Download Failed'), icon=Style.PrimaryImage.fail_result)

    def import_file_clicked(self):
        file_dialog = QFileDialog()
        file_dialog.setFileMode(QFileDialog.ExistingFile)  # Chỉ cho phép chọn tệp tin đã tồn tại
        result = file_dialog.exec()
        file_paths = file_dialog.selectedFiles()
        if result == QFileDialog.Accepted:
            if file_paths:
                file_path = file_paths[0]
                select_file = SelectFile(file_path=file_path)
                list_objects = select_file.get_objects()
                list_cameras = []
                list_ip = []
                for index, object in enumerate(list_objects):
                    if index != 0:
                        camera = Camera(state=False)
                        if object.camera_name is not None:
                            camera.name = object.camera_name
                        else:
                            camera.name = f'Camera {index}'

                        if object.group is not None:
                            for group in self.controller.list_groups:
                                if object.group == group.name:
                                    camera.cameraGroupIds = [group.id]

                        if object.rtsp is not None:
                            camera.urlMainstream = object.rtsp
                            camera.cameraType = AddCameraType.RTSP_LINK
                            list_cameras.append(camera)

                if len(list_cameras) != 0:
                    camera_list = camera_model_manager.get_camera_list()
                    temp = filter_camera_model(camera_list=list_cameras, camera_list_full=camera_list)
                    main_controller.create_cameras(parent=self, listdata=temp)

    # Search callback
    def search_device_group_clicked(self, index):
        if not self.device_group_table.is_group_updating:
            text = self.search_camera_widget.search_bar.text()
            if index != 0:
                self.device_table.filter_device_group = True
                self.device_table.put_filter_queue(
                    {FilterType.SearchCameraName: text, FilterType.SearchCameraGroup: None,
                     FilterType.SearchCameraAI: None, FilterType.SearchCameraStatus: None})
            else:
                self.device_table.filter_device_group = False
                self.device_table.put_filter_queue(
                    {FilterType.SearchCameraName: text, FilterType.SearchCameraGroup: None,
                     FilterType.SearchCameraAI: None, FilterType.SearchCameraStatus: None})

    def search_ai_clicked(self, index):
        text = self.search_camera_widget.search_bar.text()
        if index != 0:
            self.device_table.filter_ai = True
            self.device_table.put_filter_queue(
                {FilterType.SearchCameraName: text, FilterType.SearchCameraGroup: None, FilterType.SearchCameraAI: None,
                 FilterType.SearchCameraStatus: None})
        else:
            self.device_table.filter_ai = False
            self.device_table.put_filter_queue(
                {FilterType.SearchCameraName: text, FilterType.SearchCameraGroup: None, FilterType.SearchCameraAI: None,
                 FilterType.SearchCameraStatus: None})

    def search_devicetype_clicked(self, index):
        text = self.search_camera_widget.search_bar.text()
        if index != 0:
            self.device_table.filter_device_type = True
            self.device_table.put_filter_queue(
                {FilterType.SearchCameraName: text, FilterType.SearchCameraGroup: None, FilterType.SearchCameraAI: None,
                 FilterType.SearchCameraStatus: None})
        else:
            self.device_table.filter_device_type = False
            self.device_table.put_filter_queue(
                {FilterType.SearchCameraName: text, FilterType.SearchCameraGroup: None, FilterType.SearchCameraAI: None,
                 FilterType.SearchCameraStatus: None})
            
    def search_device_name(self, text):
        if text != '':
            self.device_table.filter_device_name = True
            self.device_table.put_filter_queue(
                {FilterType.SearchCameraName: text, FilterType.SearchCameraGroup: None, FilterType.SearchCameraAI: None,
                 FilterType.SearchCameraStatus: None})
        else:

            self.device_table.filter_device_name = False
            self.device_table.put_filter_queue(
                {FilterType.SearchCameraName: text, FilterType.SearchCameraGroup: None, FilterType.SearchCameraAI: None,
                 FilterType.SearchCameraStatus: None})

    def search_status_camera_clicked(self, index):
        text = self.search_camera_widget.search_bar.text()
        if index != 0:
            self.device_table.filter_status = True
            self.device_table.put_filter_queue(
                {FilterType.SearchCameraName: text, FilterType.SearchCameraGroup: None, FilterType.SearchCameraAI: None,
                 FilterType.SearchCameraStatus: None})
        else:
            self.device_table.filter_status = False
            self.device_table.put_filter_queue(
                {FilterType.SearchCameraName: text, FilterType.SearchCameraGroup: None, FilterType.SearchCameraAI: None,
                 FilterType.SearchCameraStatus: None})

    def search_device_group_name(self, text):
        if text != '':
            self.device_group_table.filter_device_group_name = True
            self.device_group_table.put_filter_queue(
                {FilterType.SearchCameraName: text, FilterType.SearchCameraAI: None})
        else:
            self.device_group_table.filter_device_group_name = False
            self.device_group_table.put_filter_queue(
                {FilterType.SearchCameraName: text, FilterType.SearchCameraAI: None})

    def search_ai_group_clicked(self, index):
        text = self.search_camera_group_widget.search_bar.text()
        if index != 0:
            self.device_group_table.filter_ai = True
            self.device_group_table.put_filter_queue(
                {FilterType.SearchCameraName: text, FilterType.SearchCameraAI: None})
        else:
            self.device_group_table.filter_ai = False
            self.device_group_table.put_filter_queue(
                {FilterType.SearchCameraName: text, FilterType.SearchCameraAI: None})

    def search_tracking_group_name(self, text):
        if text != '':
            self.tracking_group_table.filter_tracking_group_name = True
            self.tracking_group_table.put_filter_queue(text)
        else:
            self.tracking_group_table.filter_tracking_group_name = False
            self.tracking_group_table.put_filter_queue(text)

    # Checkbox click
    def all_checkbox_clicked(self, state):
        if state == 2:
            logger.debug('all_checkbox_clicked')
            self.controller.all_checkbox = Qt.CheckState.Checked
            # khi click vao checkbox all thi tat ca checkbox va button se cap nhat lai trang thai
            for item in self.controller.device_data_filtered:
                item.data.check_box = True
                item.data.btn_edit = True
                item.data.btn_trash = True
            self.controller.update_state_delegate()
        else:
            logger.debug('all_checkbox_unclicked')
            self.controller.all_checkbox = Qt.CheckState.Unchecked
            # khi click vao checkbox all thi tat ca checkbox va button se cap nhat lai trang thai
            for item in self.controller.device_data_filtered:
                item.data.check_box = False
                item.data.btn_edit = False
                item.data.btn_trash = False
            self.controller.update_state_delegate()

    def all_checkbox_group_clicked(self, state):
        if state == 2:
            # logger.debug('all_checkbox_clicked')
            self.controller.all_checkbox_group = Qt.CheckState.Checked
            # khi click vao checkbox all thi tat ca checkbox va button se cap nhat lai trang thai
            for item in self.controller.device_group_data_filtered:
                item.data.check_box = True
                item.data.btn_edit = True
                item.data.btn_trash = True
            self.controller.update_state_delegate_group()
        else:
            # logger.debug('all_checkbox_unclicked')
            self.controller.all_checkbox_group = Qt.CheckState.Unchecked
            # khi click vao checkbox all thi tat ca checkbox va button se cap nhat lai trang thai
            for item in self.controller.device_group_data_filtered:
                item.data.check_box = False
                item.data.btn_edit = False
                item.data.btn_trash = False
            self.controller.update_state_delegate_group()

    def delete_groups(self, value):
        if value:
            self.btn_delete_group.hide()
        else:
            self.btn_delete_group.show()

    def delete_tracking_groups(self, value):
        if value:
            self.btn_delete_tracking_group.hide()
        else:
            self.btn_delete_tracking_group.show()

    # Other logic
    def update_combobox_group_list(self,data):
        key, name,model = data
        if key == 'name':
            # self.device_table.camera_group_list = [{'id':None, 'data': name}]
            # self.search_group.combobox.addItem(self.device_table.camera_group_list[0]['data'])
            for index, group in enumerate(self.device_table.camera_group_list):
                if index != 0:
                    # index = 0  la case All Item
                    self.search_group.combobox.setItemText(index,group['data'].data.get("name",""))

    def get_camera_parameters(self, list_camera: List[CameraModel] = []):
        # data đã được update cần cập nhật UI liên quann.
        for item in list_camera.values():
            # logger.debug(f"get_camera_parameters = {item}")
            record_resolution = []
            if item.get_property("supportedMainResolution") != None:
                supportedMainResolution = Utils.resolution_to_dict(item.get_property("supportedMainResolution"))
                main_stream_resolution = []
                list_dict = {}
                for index,resolution in enumerate(supportedMainResolution):
                    if index == 0:
                        main_stream_resolution.append([str(resolution.width) + 'x' + str(resolution.height), str(resolution.width) + 'x' + str(resolution.height) + ' (Default)',resolution])
                        list_dict[str(resolution.width) + 'x' + str(resolution.height)] = str(resolution.width) + 'x' + str(resolution.height) + ' (Default)'
                    else:
                        main_stream_resolution.append([str(resolution.width) + 'x' + str(resolution.height), str(resolution.width) + 'x' + str(resolution.height),resolution])
                        list_dict[str(resolution.width) + 'x' + str(resolution.height)] = str(resolution.width) + 'x' + str(resolution.height)
                main_stream_resolution.append(list_dict)
                for current_resolution in main_stream_resolution:
                    if current_resolution != main_stream_resolution[-1] and item.get_property("mainstreamResolution") == current_resolution[0]:
                        record_resolution.append([current_resolution[0], current_resolution[0] + ' ' + '(Current MainStream)',current_resolution[2]])
                self.controller.main_stream_resolution[item.get_property('id')] = main_stream_resolution
                #main_controller.record_resolution[item.id] = record_resolution
            if item.get_property("supportedSubResolution") != None:
                supportedSubResolution = Utils.resolution_to_dict(item.get_property("supportedSubResolution"))
                sub_stream_resolution = []
                list_dict = {}
                for index,resolution in enumerate(supportedSubResolution):
                    if index == 0:
                        sub_stream_resolution.append([str(resolution.width) + 'x' + str(resolution.height), str(resolution.width) + 'x' + str(resolution.height) + ' (Default)',resolution])
                        list_dict[str(resolution.width) + 'x' + str(resolution.height)] = str(resolution.width) + 'x' + str(resolution.height) + ' (Default)'
                    else:
                        sub_stream_resolution.append([str(resolution.width) + 'x' + str(resolution.height), str(resolution.width) + 'x' + str(resolution.height),resolution])
                        list_dict[str(resolution.width) + 'x' + str(resolution.height)] = str(resolution.width) + 'x' + str(resolution.height)
                sub_stream_resolution.append(list_dict)
                for current_resolution in sub_stream_resolution:
                    if current_resolution != sub_stream_resolution[-1] and item.get_property("substreamResolution")== current_resolution[0]:
                        record_resolution.append([current_resolution[0], current_resolution[0] + ' ' + '(Current SubStream)',current_resolution[2]])

                self.controller.sub_stream_resolution[item.get_property('id')] = sub_stream_resolution
                #main_controller.record_resolution[item.id] = record_resolution
            if len(record_resolution) != 0:
                list_dict = {}
                for stream in record_resolution:
                    list_dict[stream[0]] = stream[1]
                record_resolution.append(list_dict)
                self.controller.record_resolution[item.get_property('id')] = record_resolution
            if item.get_property("supportedMainFps") != None:
                supportedMainFps = json.loads(item.get_property("supportedMainFps"))
                main_stream_fps = []
                list_dict = {}
                for index,fps in enumerate(supportedMainFps):
                    if fps == supportedMainFps[-1]:
                        main_stream_fps.append([str(fps), str(fps) + ' (Default)',fps])
                        list_dict[fps] = str(fps) + ' (Default)'
                    else:
                        main_stream_fps.append([str(fps), str(fps),fps])
                        list_dict[fps] = str(fps)
                main_stream_fps.append(list_dict)
                self.controller.main_stream_fps[item.get_property('id')] = main_stream_fps
            if item.get_property("supportedSubFps") != None:
                supportedSubFps = json.loads(item.get_property("supportedSubFps"))
                sub_stream_fps = []
                list_dict = {}
                for index,fps in enumerate(supportedSubFps):
                    if fps == supportedSubFps[-1]:
                        sub_stream_fps.append([fps, str(fps) + ' (Default)'])
                        list_dict[fps] = str(fps) + ' (Default)'
                    else:
                        sub_stream_fps.append([fps, str(fps)])
                        list_dict[fps] = str(fps)
                sub_stream_fps.append(list_dict)
                self.controller.sub_stream_fps[item.get_property('id')] = sub_stream_fps

    # Language
    def retranslate_device_widget(self):
        logger.debug(f'retranslate_device_screen')
        # self.tree_view_widget_group.setLabelName(self.tr("List Groups"))
        # self.tree_view_widget_camera.setLabelName(QCoreApplication.translate("DeviceScreen", u"List Cameras", None))
        self.btn_add_group.setText(QCoreApplication.translate("TabDeviceWidget", u"Add Group", None))
        self.btn_add.setText(QCoreApplication.translate("TabDeviceWidget", u"Add Camera", None))
        self.tab_content_widget.setTabText(0, QCoreApplication.translate("TabDeviceWidget", u"Device Group", None))
        self.tab_content_widget.setTabText(1, QCoreApplication.translate("TabDeviceWidget", u"Device", None))
        self.device_table.retranslateUi_camera_table()
        self.device_group_table.retranslateUi_group_table()
        self.search_camera_group_widget.label_title.setText(QCoreApplication.translate("TabDeviceWidget", u"Search", None))
        self.search_camera_group_widget.search_bar.setPlaceholderText(QCoreApplication.translate("TabDeviceWidget", u"Enter Group Name", None))
        self.search_camera_widget.label_title.setText(QCoreApplication.translate("TabDeviceWidget", u"Search", None))
        self.search_camera_widget.search_bar.setPlaceholderText(QCoreApplication.translate("TabDeviceWidget", u"Enter Camera Name", None))
        self.search_group.setTitle(QCoreApplication.translate("TabDeviceWidget", u"Group", None))
        self.search_status.setTitle(QCoreApplication.translate("TabDeviceWidget", u"Status", None))
        self.search_ai.setTitle(QCoreApplication.translate("TabDeviceWidget", u"AI", None))
        self.search_device_type.setTitle(QCoreApplication.translate("TabDeviceWidget", u"Device Type", None))
        self.search_ai_group.setTitle(QCoreApplication.translate("TabDeviceWidget", u"AI", None))
        for index, item in enumerate(self.search_ai_group.data):
            if index == 0:
                item = u'All'
            elif index == 1:
                item = u'Face'
            elif index == 2:
                item = u'Vehicle'
            elif index == 3:
                item = u'Crowd'
            elif index == 4:
                item = u'Access Control'
            text = QCoreApplication.translate("TabDeviceWidget", item, None)
            self.search_ai_group.combobox.setItemText(index, text)
        item = self.search_group.combobox.itemText(0)
        if item:
            text = QCoreApplication.translate("TabDeviceWidget", item, None)
            self.search_group.combobox.setItemText(0,text)
        for idx, item_status in enumerate(self.search_status.data):
            if idx == 0:
                item_status = u'All'
            elif idx == 1:
                item_status = u'Online'
            elif idx == 2:
                item_status = u'Offline'
            text = QCoreApplication.translate("TabDeviceWidget", item_status, None)
            self.search_status.combobox.setItemText(idx,text)
        for idex, item_ai_camera in enumerate(self.search_ai.data):
            if idex == 0:
                item_ai_camera = u'All'
            elif idex == 1:
                item_ai_camera = u'Face'
            elif idex == 2:
                item_ai_camera = u'Vehicle'
            elif idex == 3:
                item_ai_camera = u'Crowd'
            elif idex == 4:
                item_ai_camera = u'Access Control'
            text = QCoreApplication.translate("TabDeviceWidget", item_ai_camera, None)
            self.search_ai.combobox.setItemText(idex,text)
        for idx, item_status in enumerate(self.search_device_type.data):
            if idx == 0:
                item_status = u'All'
            elif idx == 1:
                item_status = u'Camera'
            elif idx == 2:
                item_status = u'AIBox'
            elif idx == 3:
                item_status = u'Gate'
            text = QCoreApplication.translate("TabDeviceWidget", item_status, None)
            self.search_device_type.combobox.setItemText(idx,text)
        self.search_group.combobox.clear()
        name = QCoreApplication.translate("TabDeviceWidget", u"All", None)
        self.device_table.camera_group_list = [{'id': None, 'data': name}]
        self.search_group.combobox.addItem(self.device_table.camera_group_list[0]['data'])
        for group in group_model_manager.get_group_list():
            item = {'id': group.get_property('id'), 'data': group}
            group.change_model.connect(self.update_combobox_group_list)
            self.device_table.camera_group_list.append(item)
            self.search_group.combobox.addItem(item['data'].data.name)
        self.update()

    def set_dynamic_stylesheet(self):
        self.tab_content_widget.setStyleSheet(
            f"""
                QTabWidget::pane {{ /* The tab widget frame */
                    background-color: transparent;
                }}
                QTabBar::pane {{
                    background: transparent;
                    border: None;
                    font-weight: None
                }}
                QTabBar::tab {{
                    background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                    color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                    padding: 8px 8px;
                    font-weight: None;
                    border: None;
                    height: 16px;
                }}
                QTabBar::tab:selected {{
                    padding: 8px 8px;
                    font-weight: bold;
                    border-bottom: 2px solid {main_controller.get_theme_attribute("Color", "primary")};
                }}
                QTabBar::scroller {{width: 0px;}}
                QTabBar QToolButton {{ /* the scroll buttons are tool buttons */
                    width: 0;
                    border-width: 0px;
                }}
                QTabBar::tear {{
                    width: 0px; 
                    border: none;
                }}
                """
        )

        self.widget_camera.setStyleSheet(
            f"""
            QWidget#widget_camera {{
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
            }}
            """
        )

        self.widget_camera_group.setStyleSheet(
            f"""
            QWidget#widget_camera_group {{
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                border: None;
            }}
            """
        )

        self.search_group.set_dynamic_stylesheet()
        self.search_ai_group.set_dynamic_stylesheet()
        self.search_camera_group_widget.set_dynamic_stylesheet()

        self.search_ai.set_dynamic_stylesheet()
        self.search_camera_widget.set_dynamic_stylesheet()
        self.search_device_type.set_dynamic_stylesheet()
        self.search_status.set_dynamic_stylesheet()

        self.device_group_table.set_dynamic_stylesheet()
        self.device_table.set_dynamic_stylesheet()
        self.btn_add_group.setStyleSheet(Style.PrimaryStyleSheet.get_add_button_style(theme_instance=main_controller))
        self.btn_add_group.setIcon(QIcon(main_controller.get_theme_attribute("Image", "add_button_icon")))
        self.btn_add.setStyleSheet(Style.PrimaryStyleSheet.get_add_button_style(theme_instance=main_controller))
        self.btn_add.setIcon(QIcon(main_controller.get_theme_attribute("Image", "add_button_icon")))
        # self.btn_import_file.setIcon(QIcon(main_controller.get_theme_attribute("Image", "upload_file")))
        # self.btn_import_file.setStyleSheet(Style.PrimaryStyleSheet.get_button_style4(theme_instance=main_controller))
        # self.btn_export_file.setIcon(QIcon(main_controller.get_theme_attribute("Image", "download_file")))
        # self.btn_export_file.setStyleSheet(Style.PrimaryStyleSheet.get_button_style4(theme_instance=main_controller))

    def resizeEvent(self, event):
        self.calculate_layout()
        super().resizeEvent(event)
