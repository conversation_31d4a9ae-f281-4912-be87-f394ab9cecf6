import logging
from src.common.qml.models.camera_grid_item import CameraGridItem
from src.common.model.camera_model import Camera
logger = logging.getLogger(__name__)
from typing import List

import cv2
import numpy as np
from PySide6.QtGui import QPixmap, Q<PERSON>ainter, QPainterPath
from PySide6.QtCore import Qt, QRect, QPoint, QRectF, Signal, QPointF
from PySide6.QtWidgets import QWidget, QVBoxLayout, QSizePolicy

from src.common.model.draw_shape_model import DrawShapeModel, ShapeType
from src.common.qml.models.frame_model import FrameModel
from PySide6.QtQuickWidgets import QQuickWidget
from PySide6.QtQml import qmlRegisterType
from src.common.camera.player import Player
from src.common.model.camera_model import camera_model_manager
from src.utils.config import Config
from src.utils.utils import Utils

color_zone = (255, 0, 0) # red
# color_zone_flow_out = (255, 0, 0)
# color_zone_detect = (255, 0, 0)
# color_zone_access_control_in = (255, 0, 0)
# color_zone_access_control_out = (255, 0, 0)
# color_zone_intrusion = (255, 0, 0)
# color_zone_protect_in = (255, 0, 0)
# color_zone_protect_out = (255, 0, 0)
# color_zone_protect = (255, 0, 0)
color_line = (0, 0, 255)  # Blue

# Default width/height for initial setup
default_width_camera_frame_to_preview = 640
default_height_camera_frame_to_preview = 360


class CameraDrawFrame(QWidget):
    enable_save_button_signal = Signal(bool)
    enable_draw_button_signal = Signal(bool)
    camera_state_changed = Signal(str)

    def __init__(self, parent=None, enable_draw=False, draw_mode="polygon", camera: Camera = None):
        super().__init__(parent)
        self.camera = camera
        self.y_scale = None
        self.x_scale = None
        self.frame_w = None
        self.frame_h = None
        self.draw_shape_to_move = None
        self.current_frame = None
        self.layout = None
        self.camera_connecting = None
        self.camera_widget = None
        self.preview_camera = None
        self.stack_camera_widget = None
        self.points = []
        self.temp_draw_shape_model: DrawShapeModel = None
        self.list_draw_shape_model: List[DrawShapeModel] = []
        self.temp_points = []
        self.temp_line = []
        self.color = color_zone
        self.color_line = color_line
        self.temp_name_shape = None
        self.shape_type: ShapeType = ShapeType.ZONE_FLOW_IN
        self.selected_point_index = None
        self.mouse_press_pos = None
        self.mouse_move_pos = None
        self.current_zone_type: ShapeType = ShapeType.ZONE_FLOW_IN
        self.draw_mode = draw_mode
        self.enable_draw = enable_draw
        self.enable_edit = False
        
        # Set fixed size using default constants with 16:9 aspect ratio
        self.frame_width = default_width_camera_frame_to_preview
        self.frame_height = default_height_camera_frame_to_preview
        
        # Set fixed size and prevent resizing
        self.setFixedSize(self.frame_width, self.frame_height)
        
        # Set border radius
        self.border_radius = 4
        
        # Enable widget clipping
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # Initialize UI
        self.load_ui()

    def paintEvent(self, event):
        """Override paint event to draw rounded corners"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Create rounded rectangle path
        path = QPainterPath()
        path.addRoundedRect(QRectF(0, 0, self.width(), self.height()), self.border_radius, self.border_radius)
        
        # Set clipping path
        painter.setClipPath(path)
        
        # Draw the widget content
        super().paintEvent(event)

    def load_ui(self):
        # Use fixed size policy to prevent resizing
        self.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)

        logger.info(f"🔧 [CameraDrawFrame] Setting up camera with data: {self.camera.id}")

        # Đăng ký FrameModel cho QML
        qmlRegisterType(FrameModel, "models", 1, 0, "FrameModel")

        # ✅ TỐI ƯU: Tạo FrameModel và embed vào QWidget

        # Tạo QQuickWidget để chứa FrameModel
        self.camera_widget = QQuickWidget()
        self.camera_widget.setFixedSize(self.frame_width, self.frame_height)
        self.camera_widget.setResizeMode(QQuickWidget.ResizeMode.SizeRootObjectToView)

        # ✅ TỐI ƯU: Load QML file từ resource
        from PySide6.QtCore import QUrl
        qml_url = QUrl("qrc:/src/common/qml/components/SimpleFrameView.qml")
        self.camera_widget.setSource(qml_url)

        # Lấy FrameModel từ QML và register camera
        root_object = self.camera_widget.rootObject()
        if root_object:
            # Tìm FrameModel trong Rectangle
            frame_model = root_object.findChild(FrameModel, "frameModel")
            if frame_model:
                # tạo camera_grid_item trước
                camera_grid_item = CameraGridItem(row=0, col=0, rows_cell=1, cols_cell=1)
                logger.info(f"✅ [CameraDrawFrame] self.camera {self.camera} - {type(self.camera)}")
                # convert self.camera is dict -> CameraModel
                id = self.camera.id
                camera_model = camera_model_manager.get_camera_model(id=id)
                logger.info(f"✅ [CameraDrawFrame] camera_model {camera_model} - {type(camera_model)}")
                camera_grid_item.cameraModel = camera_model
                frame_model.itemData = camera_grid_item
                frame_model.callback_post_process = self.update_frame_data_post_process
                # Gọi trực tiếp register_player với camera dict
                logger.info(f"✅ [CameraDrawFrame] Camera registered successfully")
            else:
                logger.error(f"❌ [CameraDrawFrame] FrameModel not found in QML")

        # ✅ TỐI ƯU: Khởi tạo drawing properties đơn giản
        self._init_drawing_properties()

        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.addWidget(self.camera_widget)
        self.setLayout(self.layout)

        # ✅ FIX: Thêm method stop_video cho compatibility
        def stop_video():
            root_object = self.camera_widget.rootObject()
            if root_object:
                frame_model = root_object.findChild(FrameModel, "frameModel")
                if frame_model:
                    frame_model.unregister_player()

        self.camera_widget.stop_video = stop_video
        
        # Calculate initial scaling factors
        self.x_scale = Config.FRAME_WIDTH_DEFAULT_SERVER / self.frame_width
        self.y_scale = Config.FRAME_HEIGHT_DEFAULT_SERVER / self.frame_height

    def _init_drawing_properties(self):
        """Khởi tạo các properties cho vẽ zone"""
        self.list_draw_shape_model = []
        self.enable_draw = False
        self.enable_edit = False
        self.current_zone_type = None
        self.temp_name_shape = ""
        self.points = []
        self.temp_line = []
        self.temp_draw_shape_model = None
        self.color = (255, 0, 0)
        self.color_line = (0, 0, 255)

    def resizeEvent(self, event):
        """Override resize event to maintain fixed size"""
        # Ignore resize events to maintain fixed size
        pass

    def showEvent(self, event):
        """Override show event to ensure fixed size"""
        super().showEvent(event)
        self.setFixedSize(self.frame_width, self.frame_height)

    def moveEvent(self, event):
        """Override move event to ensure fixed size"""
        super().moveEvent(event)
        self.setFixedSize(self.frame_width, self.frame_height)

    def is_point_close(self, point1, point2, threshold):
        distance = ((point1[0] - point2[0]) ** 2 +
                    (point1[1] - point2[1]) ** 2) ** 0.5
        return distance <= threshold

    def calculate_midpoint(self, start, end):
        return (start[0] + end[0]) // 2, (start[1] + end[1]) // 2

    def calculate_edge_index(self, start, end):
        if start[0] == end[0]:
            return 0 if start[1] < end[1] else 2
        else:
            return 1 if start[0] < end[0] else 3

    def detect_selected_edge(self, touch_pos, model: DrawShapeModel):
        touch_radius = 20  # Adjust the touch radius as needed

        for i in range(len(model.rect)):
            start_point = QPoint(*model.rect[i])
            end_point = QPoint(*model.rect[(i + 1) % len(model.rect)])

            # Calculate the center and radius of the circular region
            center = QPointF((start_point.x() + end_point.x()) / 2,
                             (start_point.y() + end_point.y()) / 2)
            # touch_pos - center
            touch: QPointF = touch_pos - center

            # calculate touch_radius size of rect
            touch_radius = (np.sqrt(
                (end_point.x() - start_point.x()) ** 2 + (end_point.y() - start_point.y()) ** 2) / 2) * 0.5
            # Check if the touch position is within the circular region
            if touch.manhattanLength() < touch_radius:
                return i  # Return the index of the selected edge

        return None  # No edge selected

    def mousePressEvent(self, event):
        logger.debug(f'CAMERA DRAW FRAME: mousePressEvent: {event}')
        if self.enable_draw:
            if self.draw_mode == 'polygon':
                self.draw_polygon(event)

    def handle_mouse_press(self, event, preview_rect, is_drawing, list_draw_shape_model, shape_type, color, color_line):
        logger.debug(f'handle_mouse_press: {event} - is in preview_rect: {preview_rect.contains(event.position().toPoint())}')
        if event.button() == Qt.LeftButton and preview_rect.contains(event.position().toPoint()):
            point = (int(event.position().x()), int(event.position().y()))

            self.temp_points.append(point)
            # if self.temp_points len = 1, clear temp_draw_shape_model and remove from list_draw_shape_model
            if len(self.temp_points) == 1:
                if self.temp_draw_shape_model is not None and self.temp_draw_shape_model in list_draw_shape_model:
                    list_draw_shape_model.remove(self.temp_draw_shape_model)
                    self.temp_draw_shape_model = None
            
            if len(self.temp_points) == 4:
                # Call arrange_points and store the result in a variable
                arranged_points = self.arrange_points(self.temp_points)
                self.points = arranged_points
                # Update self.points_checkin or self.points_checkout based on the drawing mode
                if is_drawing:
                    temp_shape_model = DrawShapeModel(
                        rect=arranged_points, color=color_line, shape_type=shape_type, color_point=color, shape_name=self.temp_name_shape)
                    # Save the current rectangle's points to the list
                    list_draw_shape_model.append(temp_shape_model)
                    self.temp_draw_shape_model = temp_shape_model
                    
                # Clear the current points to start a new rectangle
                self.temp_points.clear()

    def arrange_points(self, points):
        if len(points) == 4 and self.draw_mode == 'polygon':
            # Calculate the centroid (center point) of the four points
            centroid_x = sum(x for x, y in points) / 4
            centroid_y = sum(y for x, y in points) / 4

            # Sort the points based on their angles relative to the centroid
            sorted_points = sorted(points, key=lambda p: np.arctan2(
                p[1] - centroid_y, p[0] - centroid_x))

            # Rearrange the points as top-left, top-right, bottom-right, bottom-left
            top_left, top_right, bottom_right, bottom_left = sorted_points

            return [top_left, top_right, bottom_right, bottom_left]
        elif len(points) == 2 and self.draw_mode == 'line':
            # Arrange two points from left to right based on x-coordinate
            if points[0][0] < points[1][0]:
                return points
            else:
                return [points[1], points[0]]
        return points

    def mouseMoveEvent(self, event):
        if self.enable_draw:
            list_shape_drawing = [self.temp_draw_shape_model]
            if self.selected_point_index is not None and self.mouse_press_pos is not None:
                region_limits = QRect(
                    0, 0, self.frame_width - 1, self.frame_height - 1)
                if self.draw_shape_to_move in list_shape_drawing:
                    self.handle_point_movement(
                        event, self.draw_shape_to_move, region_limits)

    def mouseReleaseEvent(self, event):
        if self.enable_draw:
            self.selected_point_index = None
            self.mouse_press_pos = None

    def enterEvent(self, event):
        if self.enable_draw:
            self.setCursor(Qt.CursorShape.PointingHandCursor)

    def draw_arrows_for_rectangles(self, image, start, end, midpoint, color, model):
        self.draw_arrow(image=image, start=start, end=end,
                        midpoint=midpoint, color=color, model=model)

    def draw_line(self, start_point, end_point, draw_color, thickness=1):
        scaled_start = (int(start_point[0]), int(start_point[1]))
        scaled_end = (int(end_point[0]), int(end_point[1]))
        cv2.line(self.current_frame, scaled_start, scaled_end,
                 draw_color, thickness, cv2.LINE_AA)

    def draw_arrow(self, image, start, end, midpoint, color, thickness=1, tipLength=0.5, arrow_size=30, model=None):

        angle = np.arctan2(end[1] - start[1], end[0] - start[0])
        '''Tat ca huong ra ngoai'''
        if np.pi / 4 < angle % np.pi < 3 * np.pi / 4:
            # Vertical edge
            arrow_x = int(midpoint[0] - arrow_size *
                          np.cos(angle - 3 * np.pi / 2))
            arrow_y = int(midpoint[1] - arrow_size *
                          np.sin(angle - 3 * np.pi / 2))
        else:
            # Horizontal edge
            arrow_x = int(midpoint[0] - arrow_size * np.cos(angle + np.pi / 2))
            arrow_y = int(midpoint[1] - arrow_size * np.sin(angle + np.pi / 2))

        # convert color to rgb
        cv2.arrowedLine(image, midpoint, (arrow_x, arrow_y),
                        color, thickness=thickness, tipLength=tipLength)
        if model:
            model.arrow.update_data(
                midpoint=midpoint, arrow_x=arrow_x, arrow_y=arrow_y, color=color)

        # if np.pi / 4 < angle % np.pi < 3 * np.pi / 4:
        #     # Vertical edge
        #     arrow_x = int(midpoint[0] - arrow_size * np.cos(angle - np.pi / 2)) // huong vao trong
        #     arrow_y = int(midpoint[1] - arrow_size * np.sin(angle - np.pi / 2))
        # else:
        #     # Horizontal edge
        #     arrow_x = int(midpoint[0] - arrow_size * np.cos(angle + np.pi)) // nam ngang
        #     arrow_y = int(midpoint[1] - arrow_size * np.sin(angle + np.pi))

    def handle_selection(self, event, model):
        self.draw_shape_to_move = model
        for i, points in enumerate(model.rect):
            point_rect = QRect(points[0] - 10, points[1] - 10, 20, 20)
            if point_rect.contains(event.position().toPoint()):
                self.selected_point_index = i
                self.mouse_press_pos = event.position()
                break

    def draw_polygon(self, event):
        logger.debug(f'draw_polygon: {event}')
        color = self.color
        color_line = self.color_line
        if self.enable_draw:
            list_shape_drawing = [
                self.temp_draw_shape_model] if self.temp_draw_shape_model else []
        else:
            list_shape_drawing = self.list_draw_shape_model
        preview_rect = self.camera_widget.geometry()
        clicked_point = (int(event.position().x()), int(event.position().y()))
        proximity_threshold = 15

        def handle_selection_or_draw(is_drawing, list_draw_shape_model, shape_draw_type):
            on_edge = False  # Flag to check if the click is on an edge
            for shape in list_draw_shape_model:
                shape: DrawShapeModel
                if shape:
                    for point in shape.rect:
                        if self.is_point_close(clicked_point, point, proximity_threshold):
                            self.handle_selection(event, shape)
                            return
                    # not need pick edge while detection and intrustion zone
                    if self.detect_selected_edge(event.position(), shape) is not None and not self.not_allow_use_pick_edge():
                        on_edge = True
                        self.change_edge_color_and_draw_arrow(self.detect_selected_edge(
                            event.position(), shape), shape.rect, model=shape)

            if not on_edge:
                self.handle_mouse_press(
                    event, preview_rect, is_drawing, list_draw_shape_model, shape_draw_type, color, color_line)

        only_draw_with_zone_type = self.current_zone_type is not None
        
        handle_selection_or_draw(only_draw_with_zone_type, list_shape_drawing, self.current_zone_type)

    def change_edge_color_and_draw_arrow(self, edge_index, points, model: DrawShapeModel):
        # Modify the color of the selected edge
        num_points = len(points)
        if num_points >= 4:
            if model.line is None:
                model.line = []
            start_point = tuple(points[edge_index])
            end_point = tuple(points[(edge_index + 1) % num_points])
            line_selected = (start_point, end_point)
            # midpoint = self.calculate_midpoint(start_point, end_point)
            if line_selected not in model.line:
                model.line.append(line_selected)
            else:
                model.line.remove(line_selected)
            
            if Config.ENABLE_MULTI_EDGE_ZONES:
                # Chỉ cho phép chọn tối đa 3 cạnh
                if len(model.line) > 3:
                    model.line.pop(0)
            else:
                # Chỉ cho phép chọn 1 cạnh
                if len(model.line) > 1:
                    model.line.pop(0)
            # update line to temp_line to draw and save, clear
            self.temp_line = model.line
            self.temp_draw_shape_model = model

    def handle_point_movement(self, event, model: DrawShapeModel, region_limits):
        if region_limits.contains(event.position().toPoint()):
            dx = int(event.position().x() - self.mouse_press_pos.x())
            dy = int(event.position().y() - self.mouse_press_pos.y())
            new_x = int(model.rect[self.selected_point_index][0] + dx)
            new_y = int(model.rect[self.selected_point_index][1] + dy)
            if region_limits.contains(QPoint(new_x, new_y)):
                current_point_drag = model.rect[self.selected_point_index]
                model.rect[self.selected_point_index] = (new_x, new_y)
                new_point_drag = model.rect[self.selected_point_index]
                self.mouse_press_pos = event.position()
                rect = self.arrange_points(model.rect)

                if model.line is not None:
                    # update line when move point
                    for i, line in enumerate(model.line):
                        line: tuple
                        for j, point in enumerate(line):
                            point: tuple
                            if point == current_point_drag:
                                model.line[i] = (
                                    new_point_drag, line[(j + 1) % 2])
                            elif point == new_point_drag:
                                model.line[i] = (
                                    line[j - 1], current_point_drag)

                    # update params for arrow, line, points and temp to draw and save, clear
                    self.temp_line = model.line

                self.points = rect
                self.temp_draw_shape_model = model

    def update_frame_data_post_process(self, mat_frame_default):
        # input mat_frame_default is mat frame from camera
        # output is pixmap frame with rounded corners
        if mat_frame_default is not None:
            # Use actual frame dimensions with 16:9 aspect ratio
            width = self.frame_width
            height = int(width * 9 / 16)  # Enforce 16:9 aspect ratio
            
            # Resize the frame to match the 16:9 dimensions
            self.current_frame = cv2.resize(
                mat_frame_default, (int(width), int(height)))

            self.frame_h, self.frame_w, c = self.current_frame.shape
            self.x_scale = Config.FRAME_WIDTH_DEFAULT_SERVER / self.frame_width
            self.y_scale = Config.FRAME_HEIGHT_DEFAULT_SERVER / self.frame_height

            # set state button save
            if self.current_zone_type == ShapeType.ZONE_FLOW_IN and self.current_zone_type == ShapeType.ZONE_FLOW_OUT:
                self.enable_save_button_signal.emit(
                    self.temp_draw_shape_model is not None and len(self.temp_line) > 0 and len(self.points) > 0)
            else:
                self.enable_save_button_signal.emit(
                    self.temp_draw_shape_model is not None and len(self.points) > 0)

            list_shape_drawing = []
            # if self.enable_draw: -> clear all shape on server and draw new shape
            if self.enable_draw:
                list_shape_drawing = [self.temp_draw_shape_model]
            else:
                list_shape_drawing = self.list_draw_shape_model
                
            for shape in list_shape_drawing:
                shape: DrawShapeModel
                if shape is not None:
                    self.draw_polygon_shape(shape)

            if self.enable_draw:
                # draw temp points
                self.draw_points_circle(
                    self.temp_points, self.current_zone_type, self.color, self.color_line)

            # Create a QPixmap with rounded corners
            pixmap = Player.mat_to_q_pixmap(mat=self.current_frame)
            # draw rounded corners
            rounded_pixmap = QPixmap(pixmap.size())
            rounded_pixmap.fill(Qt.GlobalColor.transparent)
            
            painter = QPainter(rounded_pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            
            # Create rounded rectangle path
            path = QPainterPath()
            path.addRoundedRect(QRectF(0, 0, pixmap.width(), pixmap.height()), self.border_radius, self.border_radius)
            
            # Set clipping path
            painter.setClipPath(path)
            
            # Draw the original pixmap
            painter.drawPixmap(0, 0, pixmap)
            painter.end()
            
            # set image to camera_preview with rounded corners
            return rounded_pixmap

    def draw_points_circle(self, temp_points, current_zone_type, color_zone, color_line):
        for i in range(len(temp_points)):
            point = temp_points[i]
            # # Draw the current rectangle being drawn
            
            if len(temp_points) == 4:
                self.draw_line(temp_points[i], temp_points[(i + 1) % 4], color_zone)
            elif len(temp_points) >= 2 and (i + 1) < len(temp_points):
                self.draw_line(temp_points[i], temp_points[i + 1], color_zone)

            preview_offset_x = self.camera_widget.geometry().x()
            preview_offset_y = self.camera_widget.geometry().y()

            scaled_x = int((point[0] - preview_offset_x))
            scaled_y = int((point[1] - preview_offset_y))
            # convert color to rgb
            cv2.circle(self.current_frame,
                        (scaled_x, scaled_y), 5, color_zone, -1)

    def draw_polygon_shape(self, shape: DrawShapeModel):
        # Draw previously saved rectangles
        for i in range(len(shape.rect) - 1):
            self.draw_line(shape.rect[i], shape.rect[i + 1],
                           shape.color_point)

        if len(shape.rect) % 4 == 0:
            for i in range(4):
                self.draw_line(shape.rect[i], shape.rect[(i + 1) % 4],
                               shape.color_point)

        # draw name center of polygon
        if shape.shape_name:
            center = Utils.calculate_center_of_polygon(shape.rect)
            text = shape.shape_name
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_size = 0.3
            font_thickness = 1
            font_color = shape.color_line  # White color in BGR
            # text position is center of polygon and align center of text
            text_position = (int(center[0] - 20), int(center[1] + 5))

            # Use cv2.putText() to draw the text on the image
            cv2.putText(self.current_frame, text, text_position,
                        font, font_size, font_color, font_thickness)
                    
        for point in shape.rect:
            preview_offset_x = self.camera_widget.geometry().x()
            preview_offset_y = self.camera_widget.geometry().y()

            scaled_x = int((point[0] - preview_offset_x))
            scaled_y = int((point[1] - preview_offset_y))
            # convert color to rgb
            cv2.circle(self.current_frame, (scaled_x, scaled_y),
                       5, shape.color_point, -1)
            
        if shape.line:
            logger.debug(f'shape.line: {shape.color_line}')
            if not Config.ENABLE_MULTI_EDGE_ZONES:
                first_point_tuple = shape.line[0]
                self.draw_line(first_point_tuple[0],first_point_tuple[1],
                           shape.color_line, thickness=2)
            else:
                for index, line in enumerate(shape.line):
                    point_tuple = line
                    self.draw_line(point_tuple[0],point_tuple[1],
                           shape.color_line, thickness=2)

    def update_list_draw_shape_model(self, list_draw_shape_model):
        self.list_draw_shape_model = list_draw_shape_model

    def clear_current_temp_shape(self):
        self.temp_points.clear()
        self.temp_line.clear()
        self.points.clear()
        self.temp_draw_shape_model = None
        self.temp_name_shape = None
        self.draw_shape_to_move = None
        self.enable_draw_button_signal.emit(True)

    def not_allow_use_pick_edge(self):
        return self.current_zone_type == ShapeType.ZONE_DETECT or self.current_zone_type == ShapeType.ZONE_INTRUSION
    
    def convert_hex_to_rgb(self, hex_color):
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
