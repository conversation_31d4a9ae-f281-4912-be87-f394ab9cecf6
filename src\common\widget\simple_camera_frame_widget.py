from PySide6.QtWidgets import QWidget, QVBoxLayout
from PySide6.QtCore import Signal, Slot
from src.common.qml.models.frame_model import FrameModel
from src.common.camera.player import CameraState
import logging

logger = logging.getLogger(__name__)

class SimpleCameraFrameWidget(QWidget):
    """
    Widget đơn giản sử dụng FrameModel trực tiếp
    Thay thế cho CameraFrameViewWidget phức tạp
    """
    
    # Signals tương thích với CameraWidget
    camera_state_changed = Signal(str)
    enable_save_button_signal = Signal(bool)
    
    def __init__(self, camera_data=None, parent=None):
        super().__init__(parent)
        self.camera_data = camera_data
        self.frame_width = 640
        self.frame_height = 360  # 16:9 aspect ratio
        
        self._setup_ui()
        
        if self.camera_data:
            self.set_camera_data(self.camera_data)
    
    def _setup_ui(self):
        """Thiết lập UI với FrameModel trực tiếp"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Tạo FrameModel trực tiếp
        self.frame_model = FrameModel(parent=None)  # QQuickPaintedItem không cần Qt Widget parent
        
        # Thiết lập kích thước 16:9
        self.frame_model.setFixedSize(self.frame_width, self.frame_height)
        
        # Kết nối signals
        self.frame_model.cameraStateChanged.connect(self._on_camera_state_changed)
        self.frame_model.frameCountChanged.connect(self._on_frame_ready)
        
        # Tạo widget container cho FrameModel
        self.container = QWidget.createWindowContainer(self.frame_model.quickWindow(), self)
        self.container.setFixedSize(self.frame_width, self.frame_height)
        
        layout.addWidget(self.container)
        self.setLayout(layout)
        
        logger.info(f"✅ [SimpleCameraFrameWidget] UI setup completed")
    
    @Slot(str)
    def _on_camera_state_changed(self, state):
        """Xử lý thay đổi trạng thái camera"""
        logger.debug(f"Camera state changed: {state}")
        self.camera_state_changed.emit(state)
        
        # Enable/disable save button based on camera state
        if state == CameraState.playing.value:
            self.enable_save_button_signal.emit(True)
        else:
            self.enable_save_button_signal.emit(False)
    
    @Slot()
    def _on_frame_ready(self):
        """Xử lý khi frame đã sẵn sàng"""
        logger.debug("Frame ready")
        self.enable_save_button_signal.emit(True)
    
    def set_camera_data(self, camera_data):
        """Thiết lập dữ liệu camera - TRỰC TIẾP"""
        self.camera_data = camera_data
        logger.info(f"🎥 [SimpleCameraFrameWidget] Setting camera data: {camera_data.get('id')}")
        
        # Gọi trực tiếp register_player với camera dict
        self.frame_model.register_player(camera_data)
        logger.info(f"✅ [SimpleCameraFrameWidget] Camera registered successfully")
    
    def start_camera(self):
        """Bắt đầu camera stream"""
        if self.camera_data:
            self.set_camera_data(self.camera_data)
    
    def stop_camera(self):
        """Dừng camera stream"""
        if self.frame_model:
            self.frame_model.unregister_player()
    
    def get_frame_size(self):
        """Lấy kích thước frame"""
        return self.frame_width, self.frame_height
    
    def set_frame_size(self, width, height):
        """Thiết lập kích thước frame với tỉ lệ 16:9"""
        # Đảm bảo tỉ lệ 16:9
        aspect_ratio = 16.0 / 9.0
        if width / height != aspect_ratio:
            height = int(width / aspect_ratio)
            
        self.frame_width = width
        self.frame_height = height
        
        if self.frame_model:
            self.frame_model.setFixedSize(width, height)
        if self.container:
            self.container.setFixedSize(width, height)
    
    # Compatibility methods với CameraWidget
    @property
    def camera_widget(self):
        """Compatibility property - trả về chính nó"""
        return self
    
    def stop_video(self):
        """Compatibility method với CameraWidget"""
        self.stop_camera()
    
    def update_resize(self, width, height):
        """Compatibility method với CameraWidget"""
        self.set_frame_size(width, height)
        logger.debug(f"Camera frame resized to {width}x{height}")
    
    def setAttribute(self, attribute, value=True):
        """Compatibility method với CameraWidget"""
        super().setAttribute(attribute, value)
    
    def setContentsMargins(self, left, top, right, bottom):
        """Compatibility method với CameraWidget"""
        super().setContentsMargins(left, top, right, bottom)
    
    def setSizePolicy(self, horizontal_policy, vertical_policy=None):
        """Compatibility method với CameraWidget"""
        if vertical_policy is None:
            super().setSizePolicy(horizontal_policy)
        else:
            super().setSizePolicy(horizontal_policy, vertical_policy)
    
    # Drawing properties cho AI zone
    def __init_drawing_properties(self):
        """Khởi tạo các properties cho vẽ zone"""
        self.list_draw_shape_model = []
        self.enable_draw = False
        self.enable_edit = False
        self.current_zone_type = None
        self.temp_name_shape = ""
        self.points = []
        self.temp_line = []
        self.temp_draw_shape_model = None
        self.x_scale = 1920.0 / self.frame_width
        self.y_scale = 1080.0 / self.frame_height
        self.color = (255, 0, 0)
        self.color_line = (0, 0, 255)
    
    def clear_current_temp_shape(self):
        """Clear temporary drawing shapes"""
        if hasattr(self, 'temp_draw_shape_model'):
            self.temp_draw_shape_model = None
        if hasattr(self, 'points'):
            self.points.clear()
        if hasattr(self, 'temp_line'):
            self.temp_line.clear()
    
    def not_allow_use_pick_edge(self):
        """Check if edge picking is not allowed"""
        return False
