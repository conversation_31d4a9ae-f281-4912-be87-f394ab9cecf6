import time
import threading
import logging

logger = logging.getLogger(__name__)

def safe_cleanup_after_threads_exit(threads_to_monitor, cleanup_callback, camera_id=None, timeout=2.0):
    """
    Safely execute cleanup callback after all threads have exited
    
    Args:
        threads_to_monitor: List of thread objects to monitor
        cleanup_callback: Function to call when all threads exit
        camera_id: Optional camera ID for logging
        timeout: Max wait time in seconds (default: 2.0)
    """
    def monitor_and_cleanup():
        # logger.info(f"🔗 [CLEANUP] Starting thread monitoring for camera_id: {camera_id}")
        
        start_time = time.time()
        check_interval = 0.01  # 10ms polling
        
        while time.time() - start_time < timeout:
            # Check if all threads have actually exited
            all_threads_stopped = True
            for thread in threads_to_monitor:
                if hasattr(thread, 'isRunning') and thread.isRunning():
                    all_threads_stopped = False
                    break
            
            if all_threads_stopped:
                # ✅ All threads confirmed stopped - safe to cleanup
                # logger.info(f"🔗 [CLEANUP] All threads stopped, executing cleanup for camera_id: {camera_id}")
                cleanup_callback()
                return
            
            # Small yield to prevent busy waiting
            time.sleep(check_interval)
        
        # Timeout - force cleanup
        logger.warning(f"🔗 [CLEANUP] Timeout waiting for threads, force cleanup camera_id: {camera_id}")
        cleanup_callback()
    
    # Run monitoring in background thread
    threading.Thread(target=monitor_and_cleanup, daemon=True).start()
