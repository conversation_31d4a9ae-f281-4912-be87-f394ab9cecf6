/**
 * GridItemCamera.qml - Specialized camera component kế thừa từ GridItemBase
 *
 * Ch<PERSON><PERSON> năng chính:
 * - <PERSON><PERSON> thừa từ GridItemBase.qml (OOP inheritance)
 * - Video stream rendering và playback thông qua FrameModel
 * - PTZ controls support (pan, tilt, zoom)
 * - Digital zoom functionality với mouse wheel
 * - Camera state indicators và connection status
 * - Camera info overlay với responsive design
 * - Rotation support với snap-to-cardinal-angles
 * - Camera-specific interactions và context menu
 *
 * Architecture:
 * - Inherits: GridItemBase properties, functions, signals
 * - Extends: Camera-specific functionality (PTZ, zoom, rotation)
 * - Overrides: Camera-specific behaviors (fullscreen, selection)
 * - Integration: FrameModel cho video rendering, ConnectionStateOverlay cho status
 */

import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import models 1.0
import "../controls"
import "../base"
import "../components"
import "."
import "../constants/ZIndexConstants.js" as ZIndex
import '../../../common/qml/map'


GridItemBase {
    id: root
    itemType: "event"
    property bool isOriginImageReady: false
    property bool isCropImageReady: false
    property color header_background: gridModel ? gridModel.get_color_theme_by_key("widget_background_1") : "#2D2D2D"
    signal cameraSelected()
    signal zoomChanged(real factor)
    signal fullscreenToggled(bool isFullscreen)

    Component.onCompleted: {
    }

    Component.onDestruction: {
    }

    onItemDataChanged: {
        if (itemData) {
        }
    }

    Connections {
        target: itemData
        function onFullscreenChanged() {
        }
    }

    onIsMaximizedChanged: {
        if (isMaximized) {
            fullscreenToggled(true)
        } else {
            resetZoom()
            fullscreenToggled(false)
        }
    }

    property real zoomFactor: 1.0
    property point zoomCenter: Qt.point(0, 0)
    property bool isZooming: false
    function resetZoom() {
        zoomFactor = 1.0
        zoomCenter = Qt.point(root.width / 2, root.height / 2)
        isZooming = false
        zoomChanged(zoomFactor)
    }
    function selectCamera() {
        cameraSelected()
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 8
        spacing: 4
        // Video display area
        Rectangle {
            id: topRect
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredHeight: 300
            color: "transparent"
            border.color: header_background
            border.width: 1
            radius: 4

            Image {
                id: originImage
                anchors.centerIn: topRect
                width: topRect.width
                height: topRect.height
                source: root.itemData ? root.itemData.eventModel.imageFullUrl : ""
                fillMode: Image.PreserveAspectFit
                onStatusChanged: {
                    if (status === Image.Ready) {
                        isOriginImageReady = true
                    }
                }
            }

            Text{
                anchors.centerIn: topRect
                text: qsTr("Loading Image...")
                font.pixelSize: Math.max(10, Math.min(14, root.width / 25))
                font.weight: Font.Medium
                color: "gray"
                visible: !isOriginImageReady
            }


        }
        Rectangle {
            id: bottomRect
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredHeight: 100
            color: "transparent"

            RowLayout {
                anchors.fill: parent
                spacing: 8

                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: "transparent"
                    border.color: header_background
                    border.width: 1
                    radius: 4
                    Image {
                        id: cropImage
                        anchors.centerIn: parent
                        width: parent.width
                        height: parent.height
                        source: root.itemData ? root.itemData.eventModel.imageUrl : ""
                        fillMode: Image.PreserveAspectFit
                        onStatusChanged: {
                            if (status === Image.Ready) {
                                isCropImageReady = true
                            }
                        }
                    }

                    Text{
                        anchors.centerIn: parent
                        text: qsTr("Loading Image...")
                        font.pixelSize: Math.max(10, Math.min(14, root.width / 25))
                        font.weight: Font.Medium
                        color: "gray"
                        visible: !isCropImageReady
                    }
                }
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: "transparent"
                    border.color: header_background
                    border.width: 1
                    radius: 4
                    clip: true
                    ColumnLayout {
                        id: colLayout
                        anchors.fill: parent
                        anchors.margins: 4
                        RowLayout  {
                            Layout.fillWidth: true
                            Text {
                                text: qsTr("Loại AI")
                                Layout.preferredWidth: colLayout.width / 2
                                elide: Text.ElideRight 
                                font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                font.weight: Font.Medium
                                color: "gray"
                            }
                            Text {
                                text: root.itemData ? root.itemData.eventModel.aiType : ""
                                Layout.preferredWidth: colLayout.width / 2
                                elide: Text.ElideRight 
                                font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                font.weight: Font.Medium
                                color: "gray"
                            }
                        }
                        RowLayout  {
                            Layout.fillWidth: true
                            Text {
                                text: qsTr("Tên")
                                font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                font.weight: Font.Medium
                                color: "gray"
                                Layout.preferredWidth: colLayout.width / 2
                                elide: Text.ElideRight 
                            }
                            Text {
                                text: root.itemData ? root.itemData.eventModel.name : ""
                                Layout.preferredWidth: colLayout.width / 2
                                elide: Text.ElideRight 
                                font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                font.weight: Font.Medium
                                color: "gray"
                            }
                        }
                        RowLayout  {
                            Layout.fillWidth: true
                            Text {
                                text: qsTr("Trạng thái")
                                font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                font.weight: Font.Medium
                                color: "gray"
                                Layout.preferredWidth: colLayout.width / 2
                                elide: Text.ElideRight 
                            }
                            Text {
                                text: root.itemData ? root.itemData.eventModel.status : ""
                                Layout.preferredWidth: colLayout.width / 2
                                elide: Text.ElideRight 
                                font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                font.weight: Font.Medium
                                color: "gray"
                            }
                        }

                        RowLayout  {
                            Layout.fillWidth: true
                            Text {
                                text: "Camera"
                                font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                font.weight: Font.Medium
                                color: "gray"
                                Layout.preferredWidth: colLayout.width / 2
                                elide: Text.ElideRight 
                            }
                            Text {
                                text: root.itemData ? root.itemData.eventModel.cameraName : ""
                                Layout.preferredWidth: colLayout.width / 2
                                elide: Text.ElideRight 
                                font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                font.weight: Font.Medium
                                color: "gray"
                            }
                        }
                        RowLayout  {
                            Layout.fillWidth: true
                            Text {
                                text: qsTr("Thời gian")
                                font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                font.weight: Font.Medium
                                color: "gray"
                                Layout.preferredWidth: colLayout.width / 2
                                elide: Text.ElideRight 
                            }
                            Text {
                                text: root.itemData ? root.itemData.eventModel.createdAtLocalDate : ""
                                Layout.preferredWidth: colLayout.width / 2
                                elide: Text.ElideRight 
                                font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                font.weight: Font.Medium
                                color: "gray"
                            }
                        }
                    }

                }
            }


        }
    }
    Loader {
        id: buttonControlsLoader
        // ✅ CONTENT BOUNDS: Position within content bounds area
        x: root.contentBoundsX
        y: root.contentBoundsY
        width: root.contentBoundsWidth
        height: root.contentBoundsHeight
        z: ZIndex.gridItemControls
        active: shouldShowButtonControls
        asynchronous: true

        // ✅ VISIBILITY LOGIC: Show when hovered/selected but hide during fullscreen animations
        property bool shouldShowButtonControls: {
            // Hide during fullscreen transitions (like border canvas)
            if (root.itemData && root.itemData.isAnimating) return false
            // Show when hovered or selected (even in fullscreen)
            return root.isHovered || root.isSelected
        }

        onActiveChanged: {
        }

        sourceComponent: Component {
            GridItemButtonControls {
                gridItem: root
                itemType: "event"
                anchors.fill: parent

                // ✅ CONTENT BOUNDS: Already positioned within content bounds by loader
                contentBoundsX: 0
                contentBoundsY: 0
                contentBoundsWidth: parent.width
                contentBoundsHeight: parent.height

                onCloseButtonClicked: function(item) {
                    if (root.gridModel) {
                        root.gridModel.isSave = false
                        root.gridModel.removeItemAt(root.gridRow, root.gridCol)
                    }
                }

                onMaximizeButtonClicked: function(item) {
                    // ✅ CENTRALIZED: Use reusable fullscreen handler
                    if (root.itemData && root.animationManager) {
                        var targetState = !root.itemData.fullscreen
                        root.animationManager.handleFullscreenTransition(
                            root, targetState, "EVENT_MAXIMIZE"
                        )
                    }
                }
            }
        }
    }
    ConnectionStateOverlay {
        id: connectionOverlay
        itemData: root.itemData
        isDarkTheme: root.isDarkTheme
        baseZIndex: root.itemData ? root.itemData.zIndex + 5 : 20
    }

}
