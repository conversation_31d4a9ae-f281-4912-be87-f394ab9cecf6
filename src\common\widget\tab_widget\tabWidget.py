import src.utils.log_utils as LogUtils
import logging
logger = logging.getLogger(__name__)
import datetime
import json
from concurrent.futures import Thread<PERSON>oolExecutor
from enum import Enum

import PySide6
from PySide6.QtCore import Qt, QPoint, Signal, QCoreApplication, QEvent, QRect
from PySide6.QtSvgWidgets import QSvgWidget
from PySide6.QtWidgets import QTabWidget, QMenu, QToolButton, QWidget, QHBoxLayout, QDialog, QLabel, QVBoxLayout, \
    QApplication, QListView, QSizePolicy, QFrame, QPushButton
from PySide6.QtGui import QAction, QMouseEvent, QStandardItemModel, QStandardItem, QFontMetrics

from src.common.model.event_data_model import EventModel, MetadataHuman, MetadataTraffic, MetadataAccessControl, \
    MetadataCrowed
from src.common.widget.dialog_ai_event_widget import EventDialog
from src.common.widget.image_widget import ImageWidget
from src.common.controller.main_controller import main_controller
from src.styles.style import Style
from src.utils.config import Config

class EventAIType(Enum):
    FACE = 'HUMAN'
    ZONE_DETECT = 'ANPR'
    ACCESS_CONTROL = 'ACCESS_CONTROL'
    CROWD = 'CROWD'


class WarningContextType(Enum):
    FREQUENCY = 'FREQUENCY'
    NONE = 'NONE'
    ALL = 'ALL'
    CROWD = 'CROWD'
    PROFILE = 'PROFILE'

class TabWidget(QTabWidget):
    tab_closed_signal = Signal(int)
    close_all_tab_signal = Signal()
    add_tab_signal = Signal()
    close_other_tab_signal = Signal(int)
    button_logout_signal = Signal(str)
    button_change_server_signal = Signal(str)
    button_language_signal = Signal(str)
    load_menu_signal = Signal(int)
    def __init__(self,parent = None):
        super().__init__(parent)
        self.__context_menu_p = 0
        self.__initLastRemovedTabInfo()
        self.__initUi()
        self.callback_load_menu = None
        self.setMouseTracking(True)

    def __initLastRemovedTabInfo(self):
        self.__last_removed_tab_idx = []
        self.__last_removed_tab_widget = []
        self.__last_removed_tab_title = []

    def __initUi(self):
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.__prepareMenu)
        self.setTabsClosable(True)
        self.tabCloseRequested.connect(self.removeTab)

        # self.corner_widget = QWidget()
        # self.corner_layout = QHBoxLayout()
        # self.corner_layout.setContentsMargins(8, 8, 32, 8)
        # self.corner_widget.setLayout(self.corner_layout)
        # # self.btn_noti = ClickableQImageLabel(Style.PrimaryImage.bell_noti)
        # # self.btn_noti.clicked.connect(self.button_noti_click)
        # self.btn_user = ClickableQImageLabel(Style.PrimaryImage.avt_user)
        # self.btn_user.clicked.connect(self.button_user_click)
        # # self.corner_layout.addWidget(self.btn_noti)
        # self.corner_layout.addWidget(self.btn_user)
        # self.setCornerWidget(self.corner_widget)
        # QTabBar::close-button
        # self.setStyleSheet(f'''
        #     QTabBar::close-button {{width: 12px; height: 12px; subcontrol-position: right; margin: 4px 0px 0px 0px;}}
        # ''')
        # self.warning_event_dialog = WarningEventDialog()

    def button_noti_click(self):
        button_pos = self.btn_noti.mapToGlobal(self.btn_noti.rect().bottomRight())
        self.warning_event_dialog.showAt(button_pos)
        
    def button_user_click(self):
        menu = QMenu(self)
        menu.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        menu.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        menu.setStyleSheet(Style.StyleSheet.context_menu)
        # Create menu actions
        self.logout_action = QAction(self.tr("Logout"), self)
        self.change_server_action = QAction(self.tr("Change Server"), self)
        # Connect the actions to respective slots or functions
        self.logout_action.triggered.connect(self.logout)
        self.change_server_action.triggered.connect(self.change_server)

        # Add actions to the menu
        menu.addAction(self.logout_action)
        if Config.ENABLE_CHANGE_SERVER:
            menu.addAction(self.change_server_action)

        # Show the menu
        menu.exec_(self.btn_user.mapToGlobal(self.btn_user.rect().bottomLeft()))

    def logout(self):
        print("logout")
        self.button_logout_signal.emit("Logout")

    def change_server(self):
        print("change server")
        self.button_change_server_signal.emit("Change Server")

    def __prepareMenu(self, p):
        
        tab_idx = self.tabBar().tabAt(p)
        print(f'__prepareMenu {tab_idx} {self.currentIndex()}')
        if tab_idx != -1 and self.currentIndex() == tab_idx:
        # if tab_idx != -1:
            self.__context_menu_p = p


            # closeOtherTabAction = QAction(self.tr('Close Other Tabs'))
            # closeOtherTabAction.triggered.connect(self.closeOtherTab)

            # closeTabToTheLeftAction = QAction(self.tr('Close Tabs to the Left'))
            # closeTabToTheLeftAction.triggered.connect(self.closeTabToLeft)

            # closeTabToTheRightAction = QAction(self.tr('Close Tabs to the Right'))
            # closeTabToTheRightAction.triggered.connect(self.closeTabToRight)

            # reopenClosedTabAction = QAction(self.tr('Reopen Closed Tab'))
            # reopenClosedTabAction.triggered.connect(self.reopenClosedTab)

            self.menu = QMenu(self)
            self.menu.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
            self.menu.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
            self.menu.setStyleSheet(Style.StyleSheet.context_menu)
            print(f'DEBUG: __prepareMenu {tab_idx} {self.currentIndex()}')
            menu_list = self.callback_load_menu(tab_idx)
            if menu_list['new_view']:
                self.menu.addAction(menu_list['new_view'])
            if menu_list['open_view']:
                self.menu.addAction(menu_list['open_view'])
            self.menu.addSeparator()
            if menu_list['add_to_virtual_window']:
                self.menu.addAction(menu_list['add_to_virtual_window'])
            if menu_list['add_to_savedview']:
                self.menu.addAction(menu_list['add_to_savedview'])
            if menu_list['save_as']:
                self.menu.addAction(menu_list['save_as'])
            self.menu.addSeparator()
            # self.load_menu_signal.emit(tab_idx)
            self.closeTabAction = QAction(self.tr('Close Tab'))
            self.closeTabAction.triggered.connect(self.closeTab)

            self.closeAllTabAction = QAction(self.tr('Close All Tabs'))
            self.closeAllTabAction.triggered.connect(self.closeAllTab)
            self.menu.addAction(self.closeTabAction)
            self.menu.addAction(self.closeAllTabAction)


            # menu.addAction(closeOtherTabAction)
            # menu.addAction(closeTabToTheLeftAction)
            # menu.addAction(closeTabToTheRightAction)
            # menu.addAction(reopenClosedTabAction)
            # self.closeTabAction = QAction(self.tr('Close Tab'))
            # self.closeTabAction.triggered.connect(self.closeTab)

            # self.closeAllTabAction = QAction(self.tr('Close All Tabs'))
            # self.closeAllTabAction.triggered.connect(self.closeAllTab)
            # self.menu.addAction(self.closeTabAction)
            # self.menu.addAction(self.closeAllTabAction)
            self.menu.exec(self.mapToGlobal(p))

    def load_menu(self):
        self.closeTabAction = QAction(self.tr('Close Tab'))
        self.closeTabAction.triggered.connect(self.closeTab)

        self.closeAllTabAction = QAction(self.tr('Close All Tabs'))
        self.closeAllTabAction.triggered.connect(self.closeAllTab)
        self.menu.addAction(self.closeTabAction)
        self.menu.addAction(self.closeAllTabAction)

    def rename_tab(self, idx, title):
        self.setTabText(idx, title)

    def removeTab(self, idx):
        # neu chi co 1 tab thi khong cho close
        # if self.count() != 1:
        self.tab_closed_signal.emit(idx)
        self.__saveLastRemovedTabInfo(idx)
        super(TabWidget, self).removeTab(idx)

    def __saveLastRemovedTabInfo(self, idx):
        self.__last_removed_tab_idx.append(idx)
        self.__last_removed_tab_widget.append(self.widget(idx))
        self.__last_removed_tab_title.append(self.tabText(idx))

    def keyPressEvent(self, e):
        if e.modifiers() & Qt.AltModifier and e.key() == Qt.Key_Left:
            self.setCurrentIndex(self.currentIndex() - 1)
        elif e.modifiers() & Qt.AltModifier and e.key() == Qt.Key_Right:
            self.setCurrentIndex(self.currentIndex() + 1)
        elif e.modifiers() & Qt.ControlModifier and e.key() == Qt.Key_F4:
            self.closeTab()
        return super().keyPressEvent(e)
    

    def closeTab(self):
        if isinstance(self.__context_menu_p, QPoint):
            tab_idx = self.tabBar().tabAt(self.__context_menu_p)
            self.removeTab(tab_idx)
            self.__context_menu_p = 0
        else:
            self.removeTab(self.currentIndex())
        self.tab_closed_signal.emit(self.count())

    def closeAllTab(self):
        self.close_all_tab_signal.emit()
        self.clear()
        
        self.add_tab_signal.emit()
        
    def closeOtherTab(self):
        if isinstance(self.__context_menu_p, QPoint):
            tab_idx = self.tabBar().tabAt(self.__context_menu_p)
            self.__removeTabFromLeftTo(tab_idx)
            tab_idx = 0
            self.setCurrentIndex(tab_idx)
            self.__removeTabFromRightTo(tab_idx)
        #self.close_other_tab_signal.emit(self.count())

    def closeTabToLeft(self):
        if isinstance(self.__context_menu_p, QPoint):
            tab_idx = self.tabBar().tabAt(self.__context_menu_p)
            self.__removeTabFromLeftTo(tab_idx)
        self.tab_closed_signal.emit(self.count())

    def __removeTabFromLeftTo(self, idx):
        for i in range(idx - 1, -1, -1):
            self.removeTab(i)

        #self.tab_closed_signal.emit(self.count())

    def __removeTabFromRightTo(self, idx):
        for i in range(self.count() - 1, idx, -1):
            self.removeTab(i)

        #self.tab_closed_signal.emit(self.count())

    def closeTabToRight(self):
        if isinstance(self.__context_menu_p, QPoint):
            tab_idx = self.tabBar().tabAt(self.__context_menu_p)
            self.__removeTabFromRightTo(tab_idx)

        self.tab_closed_signal.emit(self.count())

    def reopenClosedTab(self):
        pass
        # todo enable/disable action dynamically by existence of closed tab
        # chua support tinh nang nay
        # if len(self.__last_removed_tab_idx) > 0:
        #     for i in range(len(self.__last_removed_tab_idx) - 1, -1, -1):
        #         self.insertTab(self.__last_removed_tab_idx[i],
        #                        self.__last_removed_tab_widget[i],
        #                        self.__last_removed_tab_title[i])
        #     self.setCurrentIndex(self.__last_removed_tab_idx[-1])
        #     self.__initLastRemovedTabInfo()

    # override layout of corner widget to make it next to tab bar
    def setCornerWidget(self, widget, corner=Qt.TopRightCorner):
        # set widget to after last tab
        super().setCornerWidget(widget, corner)

    def retranslateUi(self):
        # Create menu actions
        # self.logout_action.setText(QCoreApplication.translate("TabWidget", u"Logout", None))
        # self.warning_event_dialog.retranslate_ui_warning_dialog_list()
        pass


class WarningEventDialog(QDialog):
    def __init__(self):
        super().__init__()
        self.page_idx = 1
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.setModal(False)
        self.layout = QVBoxLayout()
        custom_widget = QWidget()
        custom_widget.setStyleSheet("background-color: white; border-radius: 4px;")
        custom_widget.setFixedHeight(600)
        layout_notification = QVBoxLayout()
        layout_notification.setAlignment(Qt.AlignmentFlag.AlignTop)
        '''*********'''
        self.layout_button = QHBoxLayout()
        self.button_all = ButtonFilterNotification(title=self.tr("All"), type_button="All")
        self.button_all.click = self.sort_list_by_all
        self.button_unread = ButtonFilterNotification(title=self.tr("Unread"), type_button="Unread")
        self.button_unread.click = self.sort_list_by_unread
        self.layout_button.addWidget(self.button_all)
        self.layout_button.addWidget(self.button_unread)
        self.warning_list_view = WarningListView(self)
        self.warning_list_view.verticalScrollBar().valueChanged.connect(self.load_more_data)
        self.warning_list_view.verticalScrollBar().setStyleSheet(
            f'''    
                QScrollBar:vertical {{
                    background-color: white;
                    width: 8px;
                    margin: 0px 0px 0px 0px;
                }}
                QScrollBar::handle:vertical {{
                    background-color: {Style.PrimaryColor.on_background};
                    border-radius: 4px;
                    min-height: 20px;
                }}
                QScrollBar::add-line:vertical {{
                    background: none;
                }}
                QScrollBar::sub-line:vertical {{
                    background: none;
                }}
                QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                    background: none;
                }}
                QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {{
                    width: 0px;
                    height: 0px;
                    background: none;
                }}
            '''
        )
        layout_notification.addLayout(self.layout_button)
        layout_notification.addWidget(self.warning_list_view)
        custom_widget.setLayout(layout_notification)
        self.layout.addWidget(custom_widget)
        # self.setMaximumSize(500, 500)
        self.setLayout(self.layout)

    def is_item_visible(self, item_index):
        viewport = self.warning_list_view.viewport()
        rect = self.warning_list_view.visualRect(item_index)
        return rect.top() >= 0 and rect.bottom() <= viewport.height()

    def load_more_data(self):
        scroll_bar = self.warning_list_view.verticalScrollBar()
        max_value = scroll_bar.maximum()
        current_value = scroll_bar.value()
        item_index_to_check = self.warning_list_view.list_view_model.index(self.warning_list_view.today_index, 0)
        if self.is_item_visible(item_index_to_check):
            # Load item warning today
            self.page_idx += 1
            main_controller.get_current_day_event_for_warning(
                parent=self, page_idx=self.page_idx, page_size=20, exact=False, warning=True)
        else:
            # Load item warning yesterday
            item_index_to_check_yesterday = self.warning_list_view.list_view_model.index(
                self.warning_list_view.list_view_model.rowCount() - 1, 0)
            if self.is_item_visible(item_index_to_check_yesterday):
                # Load item warning today
                self.page_idx += 1
                main_controller.get_yesterday_event_by_thread(
                    parent=self, page_idx=self.page_idx, page_size=10, exact=False, warning=True)
            else:
                pass

        # if current_value == max_value:
        #     self.page_idx += 1
        #     main_controller.get_current_day_event_for_warning(
        #         parent=self, page_idx=self.page_idx, page_size=30, exact=False, warning=True)

    def showAt(self, position):
        # Move and show the dialog
        screen_geometry = QApplication.primaryScreen().availableGeometry()

        # Calculate the new x and y position, ensuring they are within the bounds of the screen
        x = max(0, min(position.x() - 250, screen_geometry.width() - self.width()))
        y = max(0, min(position.y(), screen_geometry.height() - self.height()))

        self.move(position.x() - 250, position.y())
        self.setModal(False)
        self.show()

    def sort_list_by_all(self, mouse_event):
        main_controller.is_sort_by_unread = False
        self.button_all.setStyleSheet(f'''
            QPushButton {{ 
                background-color: #B5122E; 
                color: white; 
                border-radius: 4px; 
                border: 1px solid #B5122E
            }}
             QPushButton:hover {{
                background-color: {Style.PrimaryColor.on_hover_button}
            }}

            QPushButton:pressed {{
                background-color: {Style.PrimaryColor.on_hover_button}
            }}
        ''')
        self.button_unread.setStyleSheet(f'''
            QPushButton {{ 
                background-color: white; 
                color: #B5122E; 
                border-radius: 4px; 
                border: 1px solid #B5122E
            }}
            QPushButton:hover {{
                background-color: {Style.PrimaryColor.on_hover_button}
            }}

            QPushButton:pressed {{
                background-color: {Style.PrimaryColor.on_hover_button}
            }}
        ''')
        # show all item
        self.warning_list_view.list_view_model.clear()
        self.warning_list_view.populate_notifications(list_alert_today=main_controller.list_alert_noti_today,
                                                      list_alert_older=main_controller.list_alert_noti_yesterday)

    def sort_list_by_unread(self, mouse_event):
        main_controller.is_sort_by_unread = True
        self.button_unread.setStyleSheet(f'''
                    QPushButton {{ 
                        background-color: #B5122E; 
                        color: white; 
                        border-radius: 4px; 
                        border: 1px solid #B5122E
                    }}
                     QPushButton:hover {{
                        background-color: {Style.PrimaryColor.on_hover_button}
                    }}

                    QPushButton:pressed {{
                        background-color: {Style.PrimaryColor.on_hover_button}
                    }}
                ''')
        self.button_all.setStyleSheet(f'''
                    QPushButton {{ 
                        background-color: white; 
                        color: #B5122E; 
                        border-radius: 4px; 
                        border: 1px solid #B5122E
                    }}
                    QPushButton:hover {{
                        background-color: {Style.PrimaryColor.on_hover_button}
                    }}

                    QPushButton:pressed {{
                        background-color: {Style.PrimaryColor.on_hover_button}
                    }}
                ''')
        # sort by unread
        self.warning_list_view.list_view_model.clear()
        self.warning_list_view.populate_notifications(list_alert_today=main_controller.list_alert_noti_today,
                                                      list_alert_older=main_controller.list_alert_noti_yesterday,
                                                      sort_unread=True)

    def retranslate_ui_warning_dialog_list(self):
        self.button_all.setText(self.tr("All"))
        self.button_unread.setText(self.tr("Unread"))
        self.warning_list_view.retranslate_ui_list_alert()


class WarningListView(QListView):
    def __init__(self, parent=None):
        super(WarningListView, self).__init__(parent)
        self.tab_widget: WarningEventDialog = parent
        self.setFixedWidth(320)
        self.thread_pool = ThreadPoolExecutor(max_workers=8)
        self.list_alert_today = []
        self.list_alert_older = []
        self.today_label = None  # Store a reference to the "Today" label item
        self.today_index = None
        self.older_label = None
        self.older_index = None
        self.setup_ui()

    def setup_ui(self):
        self.setContentsMargins(0, 0, 0, 0)
        # self.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.list_view_model = QStandardItemModel()
        self.clicked.connect(self.item_clicked)
        self.setModel(self.list_view_model)
        self.populate_notifications()
        self.set_style_sheet()

    def item_clicked(self, index):
        item: ItemNotification = self.list_view_model.itemFromIndex(index)
        if item is not None and isinstance(item, ItemNotification):
            self.tab_widget.main_controller.update_cctv_func(item.event_id, True)
            item.on_item_click()

        # logger.debug("HanhLT: chay vao day    ", item.main_widget.layout().itemAt(0).layout().itemAt(0).widget().setVisible(False))
        # logger.debug("HanhLT: item.state_read   ", item.state_read)
        # Giải thích: item là ItemNotification, .main_widget là lấy main_widget, .layout() lấy layout của main_widget chính là QHBoxLayout,
        # .itemAt(0) thứ nhất lấy item đầu tiên đc add vào cái QHBoxLayout đó, .layout() lấy layout của item đầu tiên đó ở đây là QVBoxLayout,
        # .itemAt(0) thứ hai lấy item đầu  tiên vừa đc add vào QVBoxLayout đó, .widget() lấy ra widget được add vào chính là
        # SvgWidget và setVisible cho nó

    def set_style_sheet(self):
        self.setStyleSheet("""
        QListView { 
            background-color: transparent; 
        }
        QListView::item:selected {
            background-color: white;
            color: white;
        }
        """)

    def populate_notifications(self, list_alert_today=None, list_alert_older=None, sort_unread=False):
        # Add "Today" and "Older" labels
        self.today_label = ItemDateName(QCoreApplication.translate("WarningListView", "Today"))
        self.today_label.setFlags(self.today_label.flags() & ~Qt.ItemIsSelectable)  # Make label unselectable
        self.older_label = ItemDateName(QCoreApplication.translate("WarningListView", "Yesterday"))
        self.older_label.setFlags(self.older_label.flags() & ~Qt.ItemIsSelectable)

        self.today_index = self.list_view_model.rowCount()
        self.label_no_warning_today = QStandardItem(QCoreApplication.translate("WarningListView", "No warnings"))
        self.label_no_warning_today.setFlags(
            self.label_no_warning_today.flags() & ~Qt.ItemIsSelectable)  # Make label unselectable
        self.label_no_warning = QStandardItem(QCoreApplication.translate("WarningListView", "No warnings"))
        self.label_no_warning.setFlags(self.label_no_warning.flags() & ~Qt.ItemIsSelectable)  # Make label unselectable

        # Add labels and items to the model
        self.list_view_model.appendRow(self.today_label)
        self.setIndexWidget(self.list_view_model.indexFromItem(self.today_label), self.today_label.main_widget)
        if list_alert_today:
            if sort_unread:
                count = 0
                for item in list_alert_today:
                    if not item.read and count < 30:
                        self.add_to_list_today(item)
                        count += 1  # Increment the counter
                    # If 30 items have been added, break out of the loop
                    if count == 30:
                        break
            else:
                count = 0
                for item_today in list_alert_today:
                    if count < 30:
                        self.add_to_list_today(item_today)
                        count += 1  # Increment the counter
                    # If 30 items have been added, break out of the loop
                    if count == 30:
                        break
        else:
            self.list_view_model.appendRow(self.label_no_warning_today)
        self.list_view_model.appendRow(self.older_label)
        self.setIndexWidget(self.list_view_model.indexFromItem(self.older_label), self.older_label.main_widget)
        if list_alert_older:
            if sort_unread:
                count_older = 0
                for item_yesterday in list_alert_older:
                    if not item_yesterday.read and count_older < 30:
                        self.add_to_list_older(item_yesterday)
                        count_older += 1  # Increment the counter
                    # If 30 items have been added, break out of the loop
                    if count_older == 30:
                        break
            else:
                count = 0
                for it in list_alert_older:
                    if count < 30:
                        self.add_to_list_older(it)
                        count += 1  # Increment the counter
                    # If 30 items have been added, break out of the loop
                    if count == 30:
                        break
        else:
            self.list_view_model.appendRow(self.label_no_warning)

    def update_newest_item(self, event: EventModel):
        if self.today_index is not None:
            self.tab_widget.main_controller.list_alert_noti_today.append(event)
            # Add items below the "Today" label
            item_widget = ItemNotification(event=event, thread_pool=self.thread_pool)
            self.list_view_model.insertRow(1, item_widget)
            self.setIndexWidget(self.list_view_model.indexFromItem(item_widget), item_widget.main_widget)
            self.today_index += 1

    def add_to_list_today(self, event: EventModel):
        if self.today_index is not None:
            # Add items below the "Today" label
            item_widget = ItemNotification(event=event,
                                           thread_pool=self.thread_pool)  # Check if a similar item already exists in the model
            self.list_view_model.insertRow(self.today_index + 1, item_widget)
            self.setIndexWidget(self.list_view_model.indexFromItem(item_widget), item_widget.main_widget)
            self.today_index += 1
        self.label_no_warning_today.setData("", Qt.DisplayRole)

    def add_to_list_older(self, event: EventModel):
        self.older_index = self.list_view_model.rowCount() - 1
        if self.older_index is not None:
            # Add items below the "Older" label
            item_widget = ItemNotification(event=event, thread_pool=self.thread_pool)
            self.list_view_model.insertRow(self.older_index + 1, item_widget)
            self.setIndexWidget(self.list_view_model.indexFromItem(item_widget), item_widget.main_widget)
            self.older_index += 1
        self.label_no_warning.setData("", Qt.DisplayRole)

    def retranslate_ui_list_alert(self):
        self.today_label.setTitle(QCoreApplication.translate("WarningListView", "Today"))
        self.older_label.setTitle(QCoreApplication.translate("WarningListView", "Yesterday"))
        self.label_no_warning_today.setText(QCoreApplication.translate("WarningListView", "No warnings"))
        self.label_no_warning.setText(QCoreApplication.translate("WarningListView", "No warnings"))
        for index in range(self.list_view_model.rowCount()):
            item = self.list_view_model.item(index, 0)
            if isinstance(item, ItemNotification):
                item.retranslate_ui_item_noti()

class ItemNotification(QStandardItem):
    def __init__(self, event: EventModel = None, thread_pool: ThreadPoolExecutor = None):
        super().__init__()
        self.thread_pool = thread_pool
        self.state_read = False
        self.event = event
        self.event_id = event.id
        self.event_type = event.get_property("event_type")
        self.event_name = event.get_property("soCmt")
        self.event_camera_name = event.camera_model.camera_name
        self.warning_type = event.camera_model.warning_type
        self.times_between_event = event.camera_model.times_between_event
        self.event_num = event.camera_model.event_num
        self.time_range = event.camera_model.time_range
        self.event_image_crop = event.crop_image
        self.event_profiles = event.profiles
        self.metadata_human = None
        self.metadata_license_plate = None
        self.metadata_crowd = None
        self.event_image_origin = event.image
        self.event_quantity_person = None
        self.event_read = event.read
        self.profile_list = event.profile_list
        self.load_ui()

    def load_ui(self):
        self.event_type = EventAIType.FACE  # default
        self.event_type_name = QCoreApplication.translate("ItemNotification", "Face")

        # GET TYPE
        event_type = self.event.event_type
        if event_type == EventAIType.FACE.value:
            self.event_type = EventAIType.FACE
            self.event_type_name = QCoreApplication.translate("ItemNotification", "Face")
        elif event_type == EventAIType.ZONE_DETECT.value:
            self.event_type = EventAIType.ZONE_DETECT
            self.event_type_name = QCoreApplication.translate("ItemNotification", "License plate")
        elif event_type == EventAIType.ACCESS_CONTROL.value:
            self.event_type = EventAIType.ACCESS_CONTROL
            self.event_type_name = QCoreApplication.translate("ItemNotification", "Access control")
        elif event_type == EventAIType.CROWD.value:
            self.event_type = EventAIType.CROWD
            self.event_type_name = QCoreApplication.translate("ItemNotification", "Crowd")

        utc_datetime = datetime.datetime.strptime(self.event.thoiGianXuatHien, "%Y-%m-%dT%H:%M:%S.%fZ")
        # convert to local timezone - 7
        local_datetime = utc_datetime.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)
        # get only dd/MM/YYYY - hh:mm:ss
        event_time = local_datetime.strftime("%H:%M:%S")
        self.event_time = event_time

        self.image_object_warning = ImageWidget(
            height=42, allow_click_to_show_origin=False, thread_pool=self.thread_pool, width_image=68)
        preview_image_path = self.event_image_crop
        if self.event_type == 'CROWD':
            preview_image_path = self.event_image_origin
        self.image_object_warning.load_from_url(preview_image_path)
        self.image_object_warning.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.image_object_warning.setContentsMargins(0, 0, 0, 0)

        # parser metadata
        # self.event_item.metadata is json string
        self.event_metadata_parser = json.loads(self.event.metadata)
        if self.event_type == EventAIType.FACE:
            self.metadata_human = MetadataHuman.from_dict(self.event_metadata_parser)
        elif self.event_type == EventAIType.ZONE_DETECT or self.event_type == EventAIType.ACCESS_CONTROL:
            if self.event_type == EventAIType.ZONE_DETECT:
                self.metadata_license_plate = MetadataTraffic.from_dict(self.event_metadata_parser)
            if self.event_type == EventAIType.ACCESS_CONTROL:
                self.metadata_license_plate = MetadataAccessControl.from_dict(self.event_metadata_parser)
            # get license plate x,y,w,h and crop
            self.license_plate_x = self.metadata_license_plate.license_plate_x
            self.license_plate_y = self.metadata_license_plate.license_plate_y
            self.license_plate_w = self.metadata_license_plate.license_plate_w
            self.license_plate_h = self.metadata_license_plate.license_plate_h
            if self.license_plate_x and self.license_plate_y and self.license_plate_w and self.license_plate_h:
                self.image_object_warning.crop_rect = QRect(self.license_plate_x, self.license_plate_y,
                                                            self.license_plate_w,
                                                            self.license_plate_h)
                self.image_object_warning.load_from_url(self.event_image_origin)
        elif self.event_type == EventAIType.CROWD:
            self.metadata_crowd = MetadataCrowed.from_dict(self.event_metadata_parser)
            self.event_quantity_person = self.metadata_crowd.count

        # Create UI
        self.main_widget = QWidget()
        self.main_widget.setObjectName("main_widget")
        self.main_widget.setStyleSheet('''
            QWidget#main_widget { 
                background-color: white; 
            }
            QWidget:hover#main_widget {
                background-color: lightblue;  /* Change the background color on hover */
            }
        ''')
        self.main_widget.setFixedWidth(320)
        self.main_layout = QHBoxLayout(self.main_widget)
        self.main_layout.setSpacing(8)
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.main_widget.setContentsMargins(0, 0, 0, 0)
        self.widget_state_read = QWidget()
        self.layout_state_read = QVBoxLayout()

        self.svg_state_read = QSvgWidget()
        self.svg_state_read.load(Style.PrimaryImage.state_read_warning)
        self.svg_state_read.setFixedSize(10, 10)
        self.layout_state_read.addWidget(self.svg_state_read)

        self.layout_image_event = QVBoxLayout()
        self.layout_image_event.setContentsMargins(0, 0, 0, 0)
        self.layout_image_event.setSpacing(0)
        self.layout_image_event.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # Load an image using QPixmap

        self.label_time_warning = QLabel(self.event_time)
        self.label_time_warning.setStyleSheet("background-color: transparent")
        self.label_time_warning.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout_image_event.addWidget(self.image_object_warning)
        self.layout_image_event.addWidget(self.label_time_warning)

        self.layout_content_event = QVBoxLayout()
        self.layout_content_event.setContentsMargins(0, 0, 0, 0)
        self.layout_content_event.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout_content_event.setSpacing(0)
        self.layout_title = QHBoxLayout()
        self.layout_title.setContentsMargins(0, 0, 0, 0)
        self.layout_title.setSpacing(5)
        self.object_name = QLabel(self.event_name)
        self.object_name.setStyleSheet("font-weight: bold; background-color: transparent")
        self.label_blacklist = QLabel()
        self.label_blacklist.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_blacklist.setFixedHeight(28)
        self.label_blacklist.setStyleSheet(f"color: white; background-color: {Style.PrimaryColor.primary}; border-radius: 4px")
        self.layout_title.addWidget(self.object_name)
        if self.profile_list:
            for item in self.profile_list:
                self.label_blacklist.setText(str(item.name))
                self.layout_title.addWidget(self.label_blacklist)

        # Warning context
        self.warning_context_layout = QHBoxLayout()
        self.warning_context_label = QLabel(QCoreApplication.translate("ItemNotification", "Warning context: "))
        self.warning_context_label.setStyleSheet("background-color: transparent")
        self.warning_context_content = QLabel("")
        self.warning_context_content.setStyleSheet("background-color: transparent")
        self.warning_context_content.setWordWrap(True)
        font_metrics = QFontMetrics(self.warning_context_content.font())
        self.label_method_content = QLabel("")
        self.label_method_content.setStyleSheet("background-color: transparent")
        self.label_method_content.setWordWrap(True)
        # GET TYPE WARNING
        warning_type = self.event.warning_type
        if warning_type == WarningContextType.NONE.value:
            self.warning_context_content.setText("")
            self.label_method_content.setText("")
        elif warning_type == WarningContextType.ALL.value:
            if self.event_type == EventAIType.ZONE_DETECT:
                max_width = 100
                text = QCoreApplication.translate("ItemNotification", "Whenever Vehicle Appears")
                elided_text = font_metrics.elidedText(text, Qt.ElideRight, max_width)
                self.warning_context_content.setText(elided_text)
            elif self.event_type == EventAIType.FACE:
                max_width = 100
                text = QCoreApplication.translate("ItemNotification", "Whenever Human Appears")
                elided_text = font_metrics.elidedText(text, Qt.ElideRight, max_width)
                self.warning_context_content.setText(elided_text)
            self.label_method_content.setText(f"Camera {self.event_camera_name}")
        elif warning_type == WarningContextType.FREQUENCY.value:
            self.warning_context_content.setText(QCoreApplication.translate("ItemNotification", "Frequency"))
            self.label_method_content.setText(
                QCoreApplication.translate("ItemNotification", "Appears ")
                + f"{str(self.event_num)} " + QCoreApplication.translate("ItemNotification", "times within ") +
                f"{str(int(int(self.time_range)/3600))} " + QCoreApplication.translate("ItemNotification", "hour"))
        elif warning_type == WarningContextType.PROFILE.value:
            self.warning_context_content.setText(QCoreApplication.translate("ItemNotification", "Warning Group"))
            self.label_method_content.setText(f"Camera {self.event_camera_name}")
        elif warning_type == WarningContextType.CROWD.value:
            if self.event_profiles:
                max_width = 100
                text = QCoreApplication.translate("ItemNotification", "Counting & Warning Group")
                elided_text = font_metrics.elidedText(text, Qt.ElideRight, max_width)
                self.warning_context_content.setText(elided_text)
                self.label_method_content.setText(f"Camera {self.event_camera_name}")
            else:
                self.warning_context_content.setText(QCoreApplication.translate("ItemNotification", "Counting"))
                self.label_method_content.setText(
                    QCoreApplication.translate("ItemNotification", "Quantity: ") + f"{self.event_quantity_person}")
        self.warning_context_layout.addWidget(self.warning_context_label, 30)
        self.warning_context_layout.addWidget(self.warning_context_content, 70)

        self.layout_content_event.addLayout(self.layout_title)
        self.layout_content_event.addLayout(self.warning_context_layout)
        self.layout_content_event.addWidget(self.label_method_content)

        self.main_layout.addLayout(self.layout_state_read, 5)
        self.main_layout.addLayout(self.layout_image_event, 15)
        self.main_layout.addLayout(self.layout_content_event, 80)

        if self.event.read:
            self.svg_state_read.setVisible(False)

        self.setSizeHint(self.main_widget.sizeHint())
        self.setData(self.main_widget, Qt.UserRole)

        # create listen click to item
        # self.main_widget.mousePressEvent = self.on_item_click

    def on_item_click(self):
        self.state_read = True
        self.svg_state_read.setVisible(False)
        self.dialog = EventDialog(event=self.event, show_warning_info=True)
        self.dialog.exec()

    def retranslate_ui_item_noti(self):
        if self.event_type == EventAIType.FACE:
            self.event_type_name = QCoreApplication.translate("ItemNotification", "Face")
        elif self.event_type == EventAIType.ZONE_DETECT:
            self.event_type_name = QCoreApplication.translate("ItemNotification", "License plate")
        elif self.event_type == EventAIType.ACCESS_CONTROL:
            self.event_type_name = QCoreApplication.translate("ItemNotification", "Access control")
        elif self.event_type == EventAIType.CROWD:
            self.event_type_name = QCoreApplication.translate("ItemNotification", "Crowd")


        self.warning_context_label.setText(QCoreApplication.translate("ItemNotification", "Warning context: "))

        font_metrics = QFontMetrics(self.warning_context_content.font())
        warning_type = self.event.warning_type
        if warning_type == WarningContextType.NONE.value:
            self.warning_context_content.setText("")
            self.label_method_content.setText("")
        elif warning_type == WarningContextType.ALL.value:
            if self.event_type == EventAIType.ZONE_DETECT:
                max_width = 100
                text = QCoreApplication.translate("ItemNotification", "Whenever Vehicle Appears")
                elided_text = font_metrics.elidedText(text, Qt.ElideRight, max_width)
                self.warning_context_content.setText(elided_text)
            elif self.event_type == EventAIType.FACE:
                max_width = 100
                text = QCoreApplication.translate("ItemNotification", "Whenever Human Appears")
                elided_text = font_metrics.elidedText(text, Qt.ElideRight, max_width)
                self.warning_context_content.setText(elided_text)
            self.label_method_content.setText(f"Camera {self.event_camera_name}")
        elif warning_type == WarningContextType.FREQUENCY.value:
            self.warning_context_content.setText(QCoreApplication.translate("ItemNotification", "Frequency"))
            self.label_method_content.setText(
                QCoreApplication.translate("ItemNotification", "Appears ")
                + f"{str(self.event_num)} " + QCoreApplication.translate("ItemNotification", "times within ") +
                f"{str(self.time_range)} " + QCoreApplication.translate("ItemNotification", "hour"))
        elif warning_type == WarningContextType.PROFILE.value:
            self.warning_context_content.setText(QCoreApplication.translate("ItemNotification", "Warning Group"))
            self.label_method_content.setText(f"Camera {self.event_camera_name}")
        elif warning_type == WarningContextType.CROWD.value:
            if self.event_profiles:
                max_width = 100
                text = QCoreApplication.translate("ItemNotification", "Counting & Warning Group")
                elided_text = font_metrics.elidedText(text, Qt.ElideRight, max_width)
                self.warning_context_content.setText(elided_text)
                self.label_method_content.setText(f"Camera {self.event_camera_name}")
            else:
                self.warning_context_content.setText(QCoreApplication.translate("ItemNotification", "Counting"))
                self.label_method_content.setText(
                    QCoreApplication.translate("ItemNotification", "Quantity: ") + f"{self.event_quantity_person}")


class ItemDateName(QStandardItem):
    def __init__(self, title):
        super().__init__()
        self.title = title
        self.load_ui()

    def load_ui(self):
        self.main_widget = QWidget()
        self.main_layout = QVBoxLayout(self.main_widget)
        self.main_layout.setSpacing(1)
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignVCenter)

        self.title_label = QLabel(self.title)
        self.title_label.setStyleSheet(f'''
            QLabel {{ 
                color: {Style.PrimaryColor.primary}; 
                font-size: 16px; 
            }}''')

        self.divider = QFrame()
        self.divider.setFrameShape(QFrame.Shape.HLine)
        self.divider.setFrameShadow(QFrame.Shadow.Sunken)
        self.divider.setFixedHeight(2)
        self.divider.setStyleSheet(f"background-color: {Style.PrimaryColor.on_background};")
        self.main_layout.addWidget(self.title_label)
        self.main_layout.addWidget(self.divider)

        self.setSizeHint(self.main_widget.sizeHint())
        self.setData(self.main_widget, Qt.UserRole)

    def setTitle(self, title):
        self.title_label.setText(title)

class ButtonFilterNotification(QPushButton):
    def __init__(self, title=None, type_button=None, click=None):
        super().__init__()
        self.title = title
        self.click = click
        self.type_button = type_button
        self.load_ui()

    def load_ui(self):
        self.setFixedHeight(30)
        self.setText(self.title)
        if self.type_button == "All":
            self.setStyleSheet(f'''
                QPushButton {{ 
                    background-color: #B5122E; 
                    color: white; 
                    border-radius: 4px; 
                    border: 1px solid #B5122E
                }}
                 QPushButton:hover {{
                    background-color: {Style.PrimaryColor.on_hover_button}
                }}
                
                QPushButton:pressed {{
                    background-color: {Style.PrimaryColor.on_hover_button}
                }}
            ''')
        else:
            self.setStyleSheet(f'''
                QPushButton {{ 
                    background-color: white; 
                    color: #B5122E; 
                    border-radius: 4px; 
                    border: 1px solid #B5122E
                }}
                QPushButton:hover {{
                    background-color: {Style.PrimaryColor.on_hover_button}
                }}
                
                QPushButton:pressed {{
                    background-color: {Style.PrimaryColor.on_hover_button}
                }}
            ''')

    def mousePressEvent(self, e: PySide6.QtGui.QMouseEvent):

        if self.click is not None:
            self.click(e)
