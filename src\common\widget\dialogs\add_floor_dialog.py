
from PySide6.QtCore import Qt, QFileInfo
from src.presentation.device_management_screen.widget.list_custom_widgets import CustomLineEdit
from src.styles.style import Style
from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QSizePolicy, QLabel, QPushButton, QFileDialog
)
from PySide6.QtGui import QIcon, QPixmap, QPainter
from PySide6.QtSvg import QSvgRenderer
from src.common.widget.dialogs.base_dialog import NewBaseDialog, FooterType
from src.utils.config import Config
from src.presentation.device_management_screen.widget.list_custom_widgets import InputWithTitle, SpinBoxWithTitle
import logging
logger = logging.getLogger(__name__)

class AddFloorDialog(NewBaseDialog):
    def __init__(self, parent=None, name=None, ok_title='Create Floor', image_url=None ):
        self.ok_title = ok_title
        self.main_layout = QVBoxLayout()
        self.main_layout.setSpacing(0)

        self.image_url = image_url
        self.building_name = InputWithTitle(title=self.tr("Building Name"), text_placeholder=self.tr('Building Name'))
        if name is not None:
            self.building_name.setText(name)
        self.building_name.setDisabled(True)

        self.floor_name = CustomLineEdit(title=self.tr(
            'Floor Name'), key='floor_name')
        self.floor_name = InputWithTitle(title=self.tr("Floor Name"), text_placeholder=self.tr('Floor Name'))
        self.floor_name.line_edit.textEdited.connect(self.floorNameTextEdited)

        self.upload_image = QPushButton(self.tr('Upload image (File png, jpg, jpeg)'))
        self.upload_image.setStyleSheet(
            f'''
                QPushButton {{
                    background-color: {Style.PrimaryColor.primary};
                    color: {Style.PrimaryColor.white};
                    border-radius: 4px;
                    font-weight: bold;
                    font-size: 14px;
                }}
            '''
        )
        self.input_level = SpinBoxWithTitle(title=self.tr('Level'), default_value=1, max_min_range=(1, 100))
        self.input_level.spin_box.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)

        # make self.upload_image width wrap content
        self.upload_image.setFixedHeight(40)
        self.upload_image.setIcon(QIcon(Style.PrimaryImage.add))
        self.upload_image.clicked.connect(self.upload_image_clicked)

        self.image_label = QLabel(self.tr('Image'))
        self.image_label.setStyleSheet(
            "background-color: #efefef; border-radius: 4px; border: 1px solid #e0e0e0; margin: 5px;")
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setFixedHeight(200)

        
        if self.image_url is not None:
            pixmap = QPixmap(self.image_url)
            self.image_label.setPixmap(pixmap.scaled(
                self.image_label.width(), self.image_label.height(), aspectMode=Qt.AspectRatioMode.KeepAspectRatio, mode=Qt.TransformationMode.SmoothTransformation))
        footer_type = FooterType.SAVE_CANCEL

        self.label_floor_name_warning = QLabel(self.tr('Floor name cannot be empty'))
        self.label_floor_name_warning.setWordWrap(True)
        self.label_floor_name_warning.setVisible(False)
        self.label_floor_name_warning.setStyleSheet(
                f"color: {Style.PrimaryColor.error}; font-weight: 400; font-style: italic; font-size: 11px")

        self.label_floor_image_warning = QLabel(self.tr('Floor image cannot be empty'))
        self.label_floor_image_warning.setWordWrap(True)
        self.label_floor_image_warning.setVisible(False)
        self.label_floor_image_warning.setStyleSheet(
                f"color: {Style.PrimaryColor.error}; font-weight: 400; font-style: italic; font-size: 11px")

        self.main_layout.addWidget(self.building_name)
        self.main_layout.addWidget(self.floor_name)
        self.main_layout.addWidget(self.label_floor_name_warning)
        self.main_layout.setSpacing(10)
        self.main_layout.addWidget(self.image_label)
        self.main_layout.addWidget(self.upload_image)
        self.main_layout.addWidget(self.label_floor_image_warning)
        self.main_layout.setSpacing(10)
        self.main_layout.addWidget(self.input_level)

        widget_main = QWidget()
        widget_main.setFixedWidth(Config.WIDTH_DIALOG_VERTICAL)
        widget_main.setLayout(self.main_layout)

        super().__init__(parent, title=ok_title, content_widget=widget_main,
                         width_dialog=Config.WIDTH_DIALOG_VERTICAL, max_height_dialog=560, footer_type=footer_type)
        self.save_update_signal.connect(self.handle_accept_signal)

    def upload_image_clicked(self):
        file_dialog = QFileDialog()
        file_dialog.setNameFilter("Ảnh (*.png *.jpg *.jpeg, *.webp, *.svg)")
        file_dialog.setFileMode(QFileDialog.ExistingFile)
        if file_dialog.exec():
            file_path = file_dialog.selectedFiles()[0]
            self.image_url = file_path
            pixmap = QPixmap(file_path)

            self.image_label.setPixmap(pixmap.scaled(
                self.image_label.width(), self.image_label.height(), aspectMode=Qt.AspectRatioMode.KeepAspectRatio))
            self.label_floor_image_warning.setVisible(False)

    def floorNameTextEdited(self):
        if self.floor_name.text().strip() != "":
            self.label_floor_name_warning.setVisible(False)

    def handle_accept_signal(self):
        name = self.floor_name.text()
        if name.strip() == "":
            self.label_floor_name_warning.setVisible(True)
            return
        if not self.image_url or self.image_url.strip() == "":
            self.label_floor_image_warning.setVisible(True)
            return
        self.accept()