import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "math_utils.js" as MMath

Rectangle {
    width: 400
    height: 520
    focus: true 
    color: backgroundColor
    border.width: 1
    radius: 2
    border.color: primaryColor

    property color backgroundColor: timeLineManager.timeLineController ? timeLineManager.timeLineController.get_color_theme_by_key("main_background") : "red"
    property color primaryColor: timeLineManager.timeLineController ? timeLineManager.timeLineController.get_color_theme_by_key("primary") : "red"
    property color textColor: timeLineManager.timeLineController ? timeLineManager.timeLineController.get_color_theme_by_key("text_color_all_app") : "#F5F5F5"
    property color daytimeOnColor: timeLineManager.timeLineController ? timeLineManager.timeLineController.get_color_theme_by_key("daytime_background_on") : "blue"
    property color daytimeBackgroundOn: Qt.rgba(
        daytimeOnColor.r,
        daytimeOnColor.g,
        daytimeOnColor.b,
        0.2
    )
    property color currentTimeBackground: Qt.rgba(
        primaryColor.r,
        primaryColor.g,
        primaryColor.b,
        0.2
    )

    property color inactiveDayTextColor: Qt.rgba(
        textColor.r,
        textColor.g,
        textColor.b,
        0.6
    )

    Connections{
        target: timeLineManager.timeLineController
        function onThemeChanged() {
            backgroundColor = timeLineManager.timeLineController.get_color_theme_by_key("main_background")
            primaryColor = timeLineManager.timeLineController.get_color_theme_by_key("primary")
            textColor = timeLineManager.timeLineController.get_color_theme_by_key("text_color_all_app")
            color = backgroundColor
        }
    }

    ColumnLayout {
        spacing: 10
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.top: parent.top
        anchors.leftMargin: 10
        anchors.rightMargin: 10
        anchors.topMargin: 10
        RowLayout {
            Layout.alignment: Qt.AlignCenter
            Layout.fillWidth: true
            spacing: 10
            IconButton {
                width: 30
                icon.source: "qrc:src/assets/playback/previous_frame.png"
                onClicked:  {
                    calendarModel.selectedDates = []
                    calendarModel.selectedHours = []
                    if (calendarModel.monthIndex === 1) {
                        calendarModel.monthIndex = 12
                        calendarModel.yearValue--
                    } else {
                        calendarModel.monthIndex--
                    }
                    calendarModel.updateCalendar()
                }
            }
            Text {
                id: monthLabel
                horizontalAlignment: Text.AlignHCenter
                Layout.fillWidth: true
                text: calendarModel.monthText
                color: textColor
                font.pixelSize: 18
            }

            IconButton {
                width: 30
                icon.source: "qrc:src/assets/playback/next_frame.png"
                isDisabled: false
                onClicked:  {
                    calendarModel.selectedDates = []
                    calendarModel.selectedHours = []
                    if (calendarModel.monthIndex === 12) {
                        calendarModel.monthIndex = 1
                        calendarModel.yearValue++
                    } else {
                        calendarModel.monthIndex++
                    }
                    calendarModel.updateCalendar()
                }
            }
        }
        GridLayout {
            id: calendarGrid
            columns: 7
            Layout.fillWidth: true
            Layout.fillHeight: true
            Repeater {
                model: calendarModel.weekDays
                delegate: Text {
                    text: modelData
                    Layout.alignment: Qt.AlignCenter
                    font.bold: true
                    font.pixelSize: 14
                    color: textColor
                }
            }
            Repeater {
                id: dayRepeater
                model: calendarModel.dayList
                delegate: Rectangle {
                    width: 40
                    height: 40
                    Layout.fillWidth: true
                    property bool isToday: (calendarModel.yearValue === new Date().getFullYear() &&
                                            calendarModel.monthIndex === (new Date().getMonth() + 1) &&
                                            modelData.day === (new Date().getDate()).toString())
                    color: getDayColor(modelData, isToday)
                    radius: 5
                    border.color: getDayBorderColor(modelData, isToday)
                    border.width: modelData.day === "" ? 0 : 2

                    Text {
                        id: day
                        anchors.centerIn: parent
                        text: modelData.day
                        font.bold: calendarModel.selectedDates.includes(modelData)
                        color: calendarModel.selectedDates.includes(modelData) ? primaryColor : textColor
                    }
                    Rectangle {
                        width: parent.width*0.75
                        height: 2
                        anchors{
                            top: day.bottom
                            left: day.left
                            right: day.right
                        }
                        color: primaryColor 
                        visible: modelData.isData 
                    }
                    
                    MouseArea {
                        anchors.fill: parent
                        enabled: modelData.enabled
                        onClicked: {
                            if (modelData.day !== "") {
                                let newSelectedDates = calendarModel.selectedDates.slice()
                                if (ctrlPressed) {
                                    if (newSelectedDates.includes(modelData)) {
                                        if (newSelectedDates[0] !== modelData)
                                        {
                                            // nếu ngày cuối được chọn không trùng ngày min thì xóa ngày cũ đi, push lại ngày được chọn xuống cuối để tính toán fullRangeSort cho chuẩn
                                            // nếu ngày cuối được chọn trùng với ngày min thì bỏ qua không phải thêm lại mảng
                                            newSelectedDates.splice(newSelectedDates.indexOf(modelData), 1)
                                            newSelectedDates.push(modelData)
                                        }
                                    } else {
                                        newSelectedDates.push(modelData)
                                    }
                                } else {
                                    newSelectedDates = [modelData]
                                }
                                calendarModel.updateSelectedDates(newSelectedDates)
                            }
                        }
                    }
                }
            }
        }
        GridLayout {
            id: timeGrid
            columns: 6
            Layout.fillWidth: true

            Repeater {
                id: timeRepeater
                model: calendarModel.hourList
                delegate: Rectangle {
                    // width: 60
                    Layout.fillWidth: true
                    height: 30
                    color: (function(){
                        if (modelData.enabled)
                        {
                            return calendarModel.selectedHours.includes(modelData) ? currentTimeBackground : daytimeBackgroundOn
                        }
                        else{
                            return "transparent"
                        }
                    })()
                    radius: 5
                    border.color: (function(){
                        if (modelData.enabled)
                        {
                            return calendarModel.selectedHours.includes(modelData) ? primaryColor : daytimeOnColor
                        }
                        else{
                            return "transparent"
                        }
                    })()
                    
                    border.width: 1

                    Text {
                        id: hour
                        anchors.centerIn: parent
                        text: modelData.hour
                        font.bold: calendarModel.selectedHours.includes(modelData)
                        color: calendarModel.selectedHours.includes(modelData) ? primaryColor : textColor
                    }

                    Rectangle {
                        width: parent.width*0.75
                        height: 2
                        anchors{
                            top: hour.bottom
                            left: hour.left
                            right: hour.right
                        }
                        color: primaryColor 
                        visible: modelData.isData 
                    }
                    MouseArea {
                        anchors.fill: parent
                        enabled: modelData.enabled
                        onClicked: {
                            let newSelectedHours = calendarModel.selectedHours.slice()
                            if (ctrlPressed) {
                                if (newSelectedHours.includes(modelData)) {
                                    if (newSelectedHours[0] !== modelData) {
                                        newSelectedHours.splice(newSelectedHours.indexOf(modelData), 1)
                                        newSelectedHours.push(modelData)
                                    }
                                    
                                } else {
                                    newSelectedHours.push(modelData)
                                }
                            } else {
                                newSelectedHours = [modelData]
                            }
                            calendarModel.updateSelectedHours(newSelectedHours)
                        }
                    }
                }
            }
        }
    }
    property var calendarModel: timeLineManager.timeLineController.calendarModel
    property bool ctrlPressed: false
    // Component.onCompleted: timeLineManager.timeLineController.calendarModel.updateCalendar()
    function getDayColor(modelData, isToday){
        if (modelData.enabled)
        {
            return calendarModel.selectedDates.includes(modelData) ? currentTimeBackground : (modelData.day === "" ? "transparent" : (isToday ? currentTimeBackground : daytimeBackgroundOn))
        }
        else {
            return "transparent"
        } 
    }

    function getDayBorderColor(modelData, isToday){
        if (modelData.enabled)
        {
            return calendarModel.selectedDates.includes(modelData) ? primaryColor : (modelData.day === "" ? "transparent" : (isToday ? primaryColor : daytimeOnColor))
        }
        else {
            return "transparent"
        } 
    }

    Keys.onPressed: (event) => {
        parent.forceActiveFocus() 
        if (event.key === Qt.Key_Control) ctrlPressed = true
    }

    Keys.onReleased: {
        if (event.key === Qt.Key_Control) ctrlPressed = false
    }
}
