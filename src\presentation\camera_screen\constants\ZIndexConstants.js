/**
 * ZIndexConstants.js - Centralized Z-Index Constants
 * 
 * CHỨC NĂNG:
 * - <PERSON><PERSON><PERSON> nghĩa tất cả z-index constants dưới dạng JavaScript constants
 * - <PERSON><PERSON><PERSON> b<PERSON><PERSON> tính nhất quán và tránh conflict z-index
 * - <PERSON><PERSON> dàng maintain và update z-index hierarchy
 * 
 * SỬ DỤNG:
 * import "ZIndexConstants.js" as ZIndex
 * z: ZIndex.gridItemBase
 */

/**
 * CONTENT LAYER (-10 to 299)
 * Các components chính của ứng dụng: background, main containers, drag/drop
 */

// Background elements (below Repeater)
var contentContainer = -10;        // Main containers, backgrounds
var contentBackground = -5;        // Background elements (drop areas, etc.)
var contentMouseTracker = -4;      // Global mouse tracking
var contentWheelHandler = -2;      // Global wheel handling

// Grid overlay elements (above Repeater, below grid items)
var contentGridLines = 5;          // Grid lines overlay (above Repeater, below grid items)
var contentDragHighlight = 7;      // Drag visual feedback (above grid lines, below grid items)
var selectionOverlay = 8;          //Selection rectangle overlay

// Main content
var contentBase = 0;               // Default content level
var contentNormal = 10;            // Grid items normal state
var contentSelected = 15;          // Selected grid items
var contentDragged = 20;           // Items being dragged
var contentHovered = 25;           // Items being hovered

// Fullscreen
var contentFullscreen = 250;       // Fullscreen grid item
var contentFullscreenControls = 270;  // Fullscreen controls

/**
 * GRID ITEM LAYER (100 to 199)
 * Các components của grid items: video, controls, overlays
 */
var gridItemBase = 100;             // Grid item base
var gridItemContent = 105;          // Grid item content (video, map, etc.)
var gridItemOverlay = 110;          // Grid item overlays
var gridItemActionHandler = 115;    // Action handler (mouse events)
var gridItemControls = 120;         // Button controls
var gridItemTooltip = 125;          // Tooltips and hints

// Modal elements (dialogs, popups, PTZ controls)
var gridItemModalBackground = 300;  // Modal background/overlay
var gridItemDialog = 350;           // Dialog windows
var gridItemPopup = 360;            // Popup menus (PTZ controls)
var gridItemContextMenu = 370;      // Context menus
var gridItemModalTooltip = 380;     // Modal tooltips
var gridItemCloseButton = 390;      // Modal close buttons

/**
 * NOTIFICATION LAYER (400 to 999)
 * Notifications, alerts, debug, system messages
 */

// Notifications
var notificationBackground = 400;       // Notification background
var notificationMessage = 450;          // Notification messages
var notificationAlert = 460;            // Alert notifications
var notificationToast = 470;            // Toast notifications
var notificationBadge = 480;            // Notification badges

// Debug
var notificationDebugBackground = 500;  // Debug background
var notificationDebugOverlay = 600;     // Debug overlays
var notificationDebugIndicator = 700;   // Debug indicators
var notificationDebugLabel = 800;       // Debug labels
var notificationDebugConsole = 900;     // Debug console

// System (highest priority)
var notificationLoading = 1000;         // Loading indicators
var notificationError = 1100;           // Error overlays
var notificationCrash = 1200;           // Crash handlers
var notificationEmergency = 9999;       // Emergency overlays

/**
 * UTILITY FUNCTIONS
 * Helper functions for z-index calculations
 */

/**
 * Get z-index for grid item based on state
 */
function getGridItemZ(isFullscreen, isSelected, isDragging, isHovered) {
    if (isFullscreen) {
        return contentFullscreen;
    } else if (isDragging) {
        return contentDragged;
    } else if (isSelected) {
        return contentSelected;
    } else if (isHovered) {
        return contentHovered;
    } else {
        return contentNormal;
    }
}

/**
 * Get z-index for grid item controls
 */
function getGridItemControlsZ(isFullscreen) {
    if (isFullscreen) {
        return contentFullscreenControls;
    } else {
        return gridItemControls;
    }
}

/**
 * Get z-index for modal components
 */
function getModalZ(modalType) {
    switch (modalType) {
        case "dialog": return gridItemDialog;
        case "popup": return gridItemPopup;
        case "contextMenu": return gridItemContextMenu;
        case "tooltip": return gridItemModalTooltip;
        default: return gridItemModalBackground;
    }
}

/**
 * DEBUG: Print z-index hierarchy
 */
function printHierarchy() {
    console.log("=== Z-INDEX CONSTANTS HIERARCHY ===");
    
    console.log("CONTENT LAYER (-10 to 299):");
    console.log("  Background (below Repeater):");
    console.log("    contentContainer:", contentContainer);
    console.log("    contentBackground:", contentBackground);
    console.log("    contentMouseTracker:", contentMouseTracker);
    console.log("    contentWheelHandler:", contentWheelHandler);
    console.log("  Grid Overlays (above Repeater, below grid items):");
    console.log("    contentGridLines:", contentGridLines);
    console.log("    contentDragHighlight:", contentDragHighlight);
    console.log("  Main Content:");
    console.log("    contentBase:", contentBase);
    console.log("    contentNormal:", contentNormal);
    console.log("    contentSelected:", contentSelected);
    console.log("    contentDragged:", contentDragged);
    console.log("    contentHovered:", contentHovered);
    console.log("  Fullscreen:");
    console.log("    contentFullscreen:", contentFullscreen);
    console.log("    contentFullscreenControls:", contentFullscreenControls);
    
    console.log("GRID ITEM LAYER (100 to 399):");
    console.log("  Grid Components:");
    console.log("    gridItemBase:", gridItemBase);
    console.log("    gridItemContent:", gridItemContent);
    console.log("    gridItemOverlay:", gridItemOverlay);
    console.log("    gridItemActionHandler:", gridItemActionHandler);
    console.log("    gridItemControls:", gridItemControls);
    console.log("    gridItemTooltip:", gridItemTooltip);
    console.log("  Modal Components:");
    console.log("    gridItemModalBackground:", gridItemModalBackground);
    console.log("    gridItemDialog:", gridItemDialog);
    console.log("    gridItemPopup:", gridItemPopup);
    console.log("    gridItemContextMenu:", gridItemContextMenu);
    console.log("    gridItemModalTooltip:", gridItemModalTooltip);
    console.log("    gridItemCloseButton:", gridItemCloseButton);
    
    console.log("NOTIFICATION LAYER (400 to 9999):");
    console.log("  Notifications:");
    console.log("    notificationBackground:", notificationBackground);
    console.log("    notificationMessage:", notificationMessage);
    console.log("    notificationAlert:", notificationAlert);
    console.log("    notificationToast:", notificationToast);
    console.log("    notificationBadge:", notificationBadge);
    console.log("  Debug:");
    console.log("    notificationDebugBackground:", notificationDebugBackground);
    console.log("    notificationDebugOverlay:", notificationDebugOverlay);
    console.log("    notificationDebugIndicator:", notificationDebugIndicator);
    console.log("    notificationDebugLabel:", notificationDebugLabel);
    console.log("    notificationDebugConsole:", notificationDebugConsole);
    console.log("  System:");
    console.log("    notificationLoading:", notificationLoading);
    console.log("    notificationError:", notificationError);
    console.log("    notificationCrash:", notificationCrash);
    console.log("    notificationEmergency:", notificationEmergency);
    console.log("=====================================");
}
