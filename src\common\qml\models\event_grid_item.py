from PySide6.QtCore import QObject, Property, Signal
from src.common.qml.models.grid_item import GridItem
from src.common.model.event_data_model import EventModel
import logging
logger = logging.getLogger(__name__)

class EventGridItem(GridItem):
    eventModelChanged = Signal()
    def __init__(self,row:int = 0,col:int = 0,rows_cell:int = 1,cols_cell:int = 1):
        super().__init__(row = row,col= col,rows_cell = rows_cell,cols_cell = cols_cell)
        self._itemType = "event"
        self._eventModel = None

    @Property(EventModel,notify=eventModelChanged)
    def eventModel(self):
        return self._eventModel
    
    @eventModel.setter
    def eventModel(self, value: EventModel):
        if self._eventModel != value:
            self._eventModel = value
            self.eventModelChanged.emit() 

            
