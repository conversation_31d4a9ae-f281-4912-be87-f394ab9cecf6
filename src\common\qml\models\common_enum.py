from enum import IntEnum
from PySide6.QtCore import QEnum, QObject

class CommonEnum(QObject):
    class StreamType(IntEnum):
        MAIN_STREAM = 0
        SUB_STREAM = 1
        SUB_STREAM2 = 2
        VIDEO_STREAM = 3
        # main_stream = 0
        # sub_stream = 1
        # human_intrusion_stream = 2
        # human_flow_stream = 3
        # human_access_control_stream = 4
        # human_weapon_stream = 5
        # human_feature_stream = 6
        # vehicle_intrusion_stream = 7
        # vehicle_flow_stream = 8
        # vehicle_access_control_stream = 9
        # vehicle_weapon_stream = 10
        # vehicle_feature_stream = 11
        # video_stream = 12
        
    QEnum(StreamType)
    class ItemType(IntEnum):
        LABEL = 0
        CAMERA = 1
        FLOOR_MAP = 2
        DIGITAL_MAP = 3
        EVENT = 4
        VIDEOLOCAL = 5

    QEnum(ItemType)
    class CameraState(IntEnum):
        CONNECTED_REC_PIN = 0
        CONNECTED_REC_UNPIN = 1
        CONNECTED_NOREC_PIN = 2
        CONNECTED_NOREC_UNPIN = 3
        DISCONNECTED_REC_PIN = 4
        DISCONNECTED_REC_UNPIN = 5
        DISCONNECTED_NOREC_PIN = 6
        DISCONNECTED_NOREC_UNPIN = 7
    QEnum(CameraState)

    class TabType(IntEnum):
        NORMAL = 0
        VIRTUALWINDOW = 1
        SAVEDVIEW = 2
        MAPVIEW = 3
        BUIDINGVIEW = 4
        FLOORVIEW = 5
        TRACKINGVIEW = 6
    QEnum(TabType)