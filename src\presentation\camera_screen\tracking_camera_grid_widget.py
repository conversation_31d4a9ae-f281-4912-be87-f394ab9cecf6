import math
from typing import List
from PySide6.QtCore import Qt, <PERSON><PERSON><PERSON><PERSON>, QTimer, QPoint, Signal,QUrl
from PySide6.QtQuickWidgets import QQuickWidget
from PySide6.QtGui import <PERSON>GuiApplication, QStandardItem, QIcon, QResizeEvent
from PySide6.QtWidgets import Q<PERSON>rid<PERSON>ayout, QWidget, QStackedWidget, QVBoxLayout, QLabel, QSizePolicy, QFrame, \
    QHBoxLayout
from src.common.widget.widget_for_custom_grid.map_grid_item_widget import MapGridWidgetItem
from src.common.model.aiflows_model import <PERSON><PERSON><PERSON>,aiflow_model_manager
from src.common.widget.custom_titlebar.custom_component.widget_button_system import WidgetButtonSystem
from src.utils.config import Config
from src.common.widget.button_state import ButtonState
from src.styles.style import Style
from src.common.controller.main_controller import main_controller
from src.common.model.main_tree_view_model import TreeType
from src.common.model.device_models import TabType
from src.common.key_board.key_board_manager import key_board_manager

from src.common.model.camera_model import Camera, CameraModel, CameraModelManager
from src.common.qml.models.camera_grid_item import CameraGridItem
from src.common.widget.event.event_widget import EventWidget
from src.common.model.event_data_model import EventAI
from src.common.model.camera_model import camera_model_manager
from src.common.camera.video_player_manager import video_player_manager
from src.common.qml.models.grid_model import GridModel
from src.presentation.camera_screen.managers.grid_manager import gridManager
import logging
logger = logging.getLogger(__name__)
class TrackingCameraGridWidget(QWidget):
    signal_close_tracking_window = Signal(tuple)

    def __init__(self, parent=None, width=960, height=480, tab_name=None,
                 screen_index=0, item: QStandardItem = None, is_demo=False, tracking_node_model=None,gridModel = None):
        super().__init__(parent)
        print("Initializing TrackingCameraGridWidget with shared GridModel...")
        self.setWindowIcon(QIcon(Style.PrimaryImage.icon128))
        self.gridModel = gridModel
        self.gridModel.loadData()
        self.is_demo = is_demo
        self.tracking_node_model = tracking_node_model
            # main_controller.list_parent[tab_name] = [screen_index,self]

        self.item = item
        if self.item is not None:
            self.item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'open_all_virtual')))
        self.grid_width = width
        self.grid_height = height
        self.resize_timer = None

        self.setMinimumWidth(self.grid_width)
        self.setMinimumHeight(self.grid_height)
        self.screen_index_number = screen_index
        self.frame = QFrame(self)
        self.main_widget = QWidget(self.frame)
        self.main_widget.setObjectName("main_widget")
        self.main_widget.setStyleSheet(f'''
                    QWidget#main_widget {{
                        background-color: {main_controller.get_theme_attribute('Color', 'main_background')};
                    }}
                ''')
        screen_geometry = QGuiApplication.screens()[self.screen_index_number].geometry()
        self.grid_width = screen_geometry.width()
        self.grid_height = screen_geometry.height()
        self.frame.setGeometry(QRect(0, 0, self.grid_width, self.grid_height))
        self.current_grid_value = None
        self.list_camera_stream_widget = {}
        self.camera_grid_layout = QVBoxLayout()
        self.camera_grid_layout.setContentsMargins(0, 0, 0, 0)
        self.camera_grid_layout.setSpacing(0)
        self.camera_grid_layout.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignLeft)

        self.quick_widget = QQuickWidget()
        self.quick_widget.setResizeMode(QQuickWidget.ResizeMode.SizeRootObjectToView)
        self.quick_widget.rootContext().setContextProperty("gridModel", self.gridModel)
        self.quick_widget.setSource(QUrl("qrc:/src/presentation/camera_screen/components/VideoWallGrid.qml"))

        if self.quick_widget.status() == QQuickWidget.Error:
            errors = self.quick_widget.errors()
            print("QML Loading Errors:")
            for error in errors:
                print(f"  - {error}")
        else:
            # Try to call setGridModel function on QML root object
            root_object = self.quick_widget.rootObject()
            if root_object:
                try:
                    # Call QML function to set gridModel
                    root_object.setGridModel(self.gridModel)
                except Exception as e:
                    logger.error(f"❌ Error calling setGridModel: {e}")
            else:
                logger.error(f"❌ No root object found")
        self.camera_grid_layout.addWidget(self.quick_widget)

        self.main_widget.setLayout(self.camera_grid_layout)
        self.setGeometry(screen_geometry)

        self.tab_closed = False
        self.setWindowTitle(self.gridModel.get_property("name","ahihi"))
        logger.info(f'VirtualCameraGridWidget done')
        self.screen_index = QLabel(self)
        self.screen_index.setStyleSheet(Style.StyleSheet.label_style2)
        self.screen_index.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.screen_index.setVisible(False)
        self.screen_index.setGeometry(self.grid_width / 2 - 50, 0, 100, 100)
        main_controller.list_parent[self.gridModel.get_property("id",None)] = [screen_index, self]
        if self.is_demo:
            self.widget_button_system = WidgetButtonSystem(parent=self, window_parent=self, just_show_close=True)
            self.widget_button_system.button_close_signal.connect(lambda: self.close_tracing_window_click(self)) 

    def close_tracing_window_click(self, parent):
        if self.isVisible():
            self.clearFocus()
            self.close()

    def resizeEvent(self, event):
        super().resizeEvent(event)
        self.quick_widget.setGeometry(0, 0, self.width(), self.height())
        frame_size = self.frameGeometry()
        if self.is_demo:
            self.widget_button_system.setGeometry(0, 0, frame_size.size().width() - 10, 32)
    
    def close_tracking_window(self, screen_index, tracking_window, tracking_node_model):
        # self.tracing_windows[screen_index].remove((tracking_window, tracking_node_model))
        if screen_index in main_controller.tracing_windows_dict:
            main_controller.tracing_windows_dict[screen_index].remove((tracking_window, tracking_node_model))
            old_value = main_controller.tracing_windows_dict[screen_index]
            if len(old_value) == 0:
                del main_controller.tracing_windows_dict[screen_index]

    def closeEvent(self, event):
        for item in self.gridModel._listGridItems._items.values():
            if isinstance(item,CameraGridItem):
                item.isPlaying = False
        self.close_tracking_window(self.screen_index_number, self, self.tracking_node_model)
        # self.deleteLater()



