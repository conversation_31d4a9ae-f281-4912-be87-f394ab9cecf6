import QtQuick
import QtQuick.Controls.Material 
import QtQuick.Layouts
import Qt5Compat.GraphicalEffects

Rectangle {
    id: root
    radius: 10
    z: 10
    property color textColor: map2dController ? map2dController.get_color_theme_by_key("text_color_all_app") : "black"
    property color primaryColor: map2dController ? map2dController.get_color_theme_by_key("primary") : "white"
    property color backgroundColor: map2dController ? map2dController.get_color_theme_by_key("main_background") : "white"
    property color borderColor: Qt.rgba(
        textColor.r,
        textColor.g,
        textColor.b,
        0.5
    )

    property color nonActiveColor: Qt.rgba(
        textColor.r,
        textColor.g,
        textColor.b,
        0.35
    )

    property var camData: null
    property var iconType: 0
    property var _mapState: null
    property alias redrawButton: _redrawButton
    
    property var isIconTab: {
        if (!camData) return false;
        return camData.fovMode === "ICON"
    }

    signal changeType(string type)
    signal camDataUpdated(var newData)
    signal deleteCamera()
    signal redrawPolygon()

    Component.onCompleted: {
        if (!camData){
            iconType = 0;
        }
        else iconType = JSON.parse(camData.fovData).icon_type;

    }

    function updateCamData(prop, value) {
        var newData = Object.assign({}, camData)
        newData[prop] = value
        camData = newData
        camDataUpdated(camData)
    }

    function updateFovData(prop, value) {
        if (!camData) return;

        var newData = Object.assign({}, camData);
        var fovData = JSON.parse(newData.fovData);
        
        fovData[prop] = value;
        newData.fovData = JSON.stringify(fovData);

        camData = newData;
        camDataUpdated(camData);
    }

    width: 340
    height: contentColumn.implicitHeight + 20
    color: backgroundColor

    Column{
        id: contentColumn
        anchors.centerIn: parent
        anchors.margins: 40
        spacing: 12

        RowLayout{
            anchors.left: parent.left
            spacing: 2
            Image{
                id: displayedIcon
                width: 28
                height: 28
                source: "qrc:/src/assets/map/iconCamera" + (iconType + 1) +".svg"
                fillMode: Image.PreserveAspectFit
                sourceSize: Qt.size(width, height)
                Layout.preferredWidth: width
                Layout.preferredHeight: height
            }

            ColorOverlay {
                anchors.fill: displayedIcon
                source: displayedIcon
                color: textColor
                Layout.preferredWidth: 0
                Layout.preferredHeight: 0
            }

            Text{
                text: qsTr("Camera Settings")
                color: textColor
                font.pixelSize: 16
            }
        }

        Text {
            text: qsTr("Size")
            color: textColor
            font.pixelSize: 14
        }

        Rectangle{
            id: sizeButtonZone
            width: parent.width
            height: 32
            color: nonActiveColor
            border.color: borderColor
            border.width: 1
            radius: 10

            Rectangle {
                id: highlight
                y: 0
                height: parent.height
                width: selectedButton ? selectedButton.width : 0
                x: selectedButton ? selectedButton.x : 0
                radius: 10
                color: backgroundColor

                Behavior on x {
                    NumberAnimation { duration: 200; easing.type: Easing.InOutQuad }
                }
                Behavior on width {
                    NumberAnimation { duration: 200; easing.type: Easing.InOutQuad }
                }
            }

            RowLayout {
                spacing: 0
                anchors.fill: sizeButtonZone
                Button{
                    id: smallButton
                    text: qsTr("Small")
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Material.foreground: textColor
                    background: Rectangle{
                        anchors.fill: smallButton
                        color: camData ?( camData.size === 1 ? backgroundColor : "transparent") : "transparent"
                        radius: 10
                        border.color: borderColor
                        border.width: camData ? (camData.size === 1 ? 1 : 0) : 0
                        Behavior on border.width { NumberAnimation { duration:200; easing.type: Easing.InOutQuad } }
                        Behavior on border.color { ColorAnimation { duration:200; easing.type: Easing.InOutQuad } }
                    }

                    onClicked: () =>{
                        updateCamData("size", 1)
                    }
                }
                Button{
                    id: mediumButton
                    text: qsTr("Medium")
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Material.foreground: textColor
                    background: Rectangle{
                        anchors.fill: mediumButton
                        color: camData ?( camData.size === 2 ? backgroundColor : "transparent") : "transparent"
                        radius: 10
                        border.color: borderColor
                        border.width: camData ? (camData.size === 2 ? 1 : 0) : 0
                        Behavior on border.width { NumberAnimation { duration:600; easing.type: Easing.InOutQuad } }
                        Behavior on border.color { ColorAnimation { duration:600; easing.type: Easing.InOutQuad } }
                    }
                    onClicked: () =>{
                        updateCamData("size", 2)
                    }
                }
                Button{
                    id: largeButton
                    text: qsTr("Large")
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Material.foreground: textColor
                    background: Rectangle{
                        anchors.fill: largeButton
                        color: camData ?( camData.size === 3 ? backgroundColor : "transparent") : "transparent"
                        radius: 10
                        border.color: borderColor
                        border.width: camData ? (camData.size === 3 ? 1 : 0) : 0
                        Behavior on border.width { NumberAnimation { duration:600; easing.type: Easing.InOutQuad } }
                        Behavior on border.color { ColorAnimation { duration:600; easing.type: Easing.InOutQuad } }
                    }
                    onClicked: function(){
                        updateCamData("size", 3)
                    }
                }
            }
        }

        RowLayout {
            anchors.left: parent.left
            anchors.right: parent.right
            height: 32
            spacing: 6
            Text{
                Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                text: qsTr("Show as:")
                color: textColor
                font.pixelSize: 14
            }

            Item {
                Layout.fillWidth: true
            }

            RadioButton {
                id: iconRadio
                Layout.alignment: Qt.AlignVCenter
                Material.foreground: textColor
                text: qsTr("Icon")
                font.pixelSize: 14
                checked: isIconTab

                // override the little circle (“indicator”)
                indicator: Item {
                    implicitWidth: 20; implicitHeight: 20
                    anchors.verticalCenter: parent.verticalCenter
                    // outer ring
                    Rectangle {
                        anchors.fill: parent
                        radius: width/2
                        color: "transparent"
                        border.width: iconRadio.checked ? 6 : 2
                        border.color: iconRadio.checked
                                    ? primaryColor
                                    : Qt.rgba(textColor.r, textColor.g, textColor.b, 0.5)
                    }
                }

                onToggled: function() {
                    if (checked) {
                        isIconTab = true
                        updateCamData("fovMode", "ICON")
                        changeType("ICON")
                        iconType = 0
                    }
                }
            }

            RadioButton {
                id: shapeRadio
                Layout.alignment: Qt.AlignVCenter
                text: qsTr("Shape")
                font.pixelSize: 14
                Material.foreground: textColor
                checked: !isIconTab

                // override the little circle (“indicator”)
                indicator: Item {
                    implicitWidth: 20; implicitHeight: 20
                    anchors.verticalCenter: parent.verticalCenter
                    // outer ring
                    Rectangle {
                        anchors.fill: parent
                        radius: width/2
                        color: "transparent"
                        border.width: shapeRadio.checked ? 6 : 2
                        border.color: shapeRadio.checked
                                    ? primaryColor
                                    : Qt.rgba(textColor.r, textColor.g, textColor.b, 0.5)
                    }
                }

                onToggled: {
                    if (checked) {
                        isIconTab = false
                        updateCamData("fovMode", "RECTANGLE")
                        changeType("RECTANGLE")
                    }
                }
            }

        }

        RowLayout {
            height: 40
            visible: isIconTab
            spacing: 20
            anchors.left: parent.left
            anchors.right: parent.right
            Repeater {
                model: 5
                delegate: Button{
                    id: iconButton
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    width: 32
                    height: 32
                    background: Rectangle{
                        id: iconButtonBackground
                        anchors.fill: iconButton
                        color: "transparent"
                        radius: 8
                        border.color: textColor
                        border.width: iconType === index ? 1 : 0

                        Image {
                            id: camIcon
                            anchors.centerIn: parent
                            source: "qrc:/src/assets/map/iconCamera" + (index + 1) +".svg"
                            width: iconButtonBackground.width * 0.9
                            height: iconButtonBackground.width * 0.9
                            fillMode: Image.PreserveAspectFit
                            sourceSize: Qt.size(width, height)
                        }
                        ColorOverlay {
                            anchors.fill: camIcon
                            source: camIcon
                            color: textColor
                        }
                    }
                    
                    onClicked: function(){
                        iconType = index
                        updateFovData("icon_type", index)
                    }
                }
            }
        }

        Row{
            height: 52
            visible: !isIconTab
            spacing: 20
            anchors.horizontalCenter: parent.horizontalCenter
        
            Button{
                width: 44
                height: 54

                background: Rectangle{
                    color: "transparent"
                    radius: width / 2
                    Image {
                        anchors.centerIn: parent
                        source: "qrc:/src/assets/map/iconRectangle" + (camData.fovMode === "RECTANGLE" ? "Clicked.svg" : ".svg")
                        width: 40
                        height: 40
                    }
                }
                
                onClicked: function(){
                    updateCamData("fovMode", "RECTANGLE")
                    changeType("RECTANGLE")
                }
            }
        
            Button{
                width: 44
                height: 54
                background: Rectangle{
                    color: "transparent"
                    radius: width / 2
                    Image {
                        anchors.centerIn: parent
                        source: "qrc:/src/assets/map/iconCircle" + (camData.fovMode === "CIRCLE" ? "Clicked.svg" : ".svg")
                        width: 40
                        height: 40
                    }
                }
                
                onClicked: function(){
                    updateCamData("fovMode", "CIRCLE")
                    changeType("CIRCLE")
                }
            }
        
            Button{
                width: 44
                height: 54
                background: Rectangle{
                    color: "transparent"
                    radius: width / 2
                    Image {
                        anchors.centerIn: parent
                        source: "qrc:/src/assets/map/iconPolygon" + (camData.fovMode === "POLYGON" ? "Clicked.svg" : ".svg")
                        width: 40
                        height: 40
                    }
                }
                
                onClicked: function(){
                    updateCamData("fovMode", "POLYGON")
                    changeType("POLYGON")
                }
            }
        }

        Text {
            text: qsTr("Color")
            color: textColor
            font.pixelSize: 14
        }

        Row{
            height: 52
            spacing: 12
            anchors.horizontalCenter: parent.horizontalCenter
            Repeater {
                model: ["#FF9A24", "#675DF0", "#FF6969", "#1191E3", "#EF5E9A", "#109FA1", "#31B23A"]

                delegate: Item{
                    id: itemRoot
                    width: 32
                    height: 32
                    Rectangle {
                        anchors.fill: parent
                        color: "transparent"
                        radius: 10
                        border.color: textColor
                        border.width: camData.color === modelData ? 1 : 0
                    }

                    Button{
                        id: colorButton
                        width: itemRoot.width * 0.8
                        height: itemRoot.width * 0.8
                        anchors.centerIn: parent
                        background: Rectangle{
                            color: modelData
                            radius: 8
                            anchors.fill: colorButton
                        }

                        onClicked: function(){
                            updateCamData("color", modelData)
                        }
                    }
                }
            }
        }

        
        Rectangle{
            height: 2
            width: parent.width
            color: Qt.rgba(
                textColor.r,
                textColor.g,
                textColor.b,
                0.2
            )
        }

        Text{
            text: qsTr("Preferences")
            color: textColor
            font.pixelSize: 14
        }

        Row {
            visible: camData ? camData.fovMode === "ICON" : false
            height: 24
            spacing: -14
            CheckBox {
                id: showFovCheckBox
                checked: camData.fovEnable

                indicator: Rectangle {
                    implicitWidth: 18
                    implicitHeight: 18
                    radius: 6
                    color: showFovCheckBox.checked ? primaryColor : backgroundColor
                    border.color: primaryColor
                    border.width: 1

                    Canvas {
                        id: checkboxFovCanvas
                        anchors.fill: parent
                        visible: showFovCheckBox.checked
                        anchors.margins: 4
                        onPaint: function(){
                            var ctx = getContext("2d")
                            ctx.clearRect(0, 0, checkboxFovCanvas.width, checkboxFovCanvas.height)
                            ctx.strokeStyle = backgroundColor
                            ctx.lineWidth = 2
                            ctx.beginPath()
                            ctx.moveTo(0, checkboxFovCanvas.height / 2)
                            ctx.lineTo(checkboxFovCanvas.width / 3, checkboxFovCanvas.height)
                            ctx.lineTo(checkboxFovCanvas.width, 0)
                            ctx.stroke()
                        }
                    }
                }

                onClicked: function(){
                    updateCamData("fovEnable", showFovCheckBox.checked)
                }
            }

            Text {
                text: qsTr("Show field of view")
                color: textColor
                font.pixelSize: 14
                verticalAlignment: Text.AlignVCenter
            }
        }

        Row {
            height: 24
            spacing: -14
            CheckBox {
                id: showNameCheckBox
                checked: camData.nameEnable

                indicator: Rectangle {
                    implicitWidth: 18
                    implicitHeight: 18
                    radius: 6
                    color: showNameCheckBox.checked ? primaryColor : backgroundColor
                    border.color: primaryColor
                    border.width: 1

                    Canvas {
                        id: checkboxNameCanvas
                        anchors.fill: parent
                        anchors.margins: 4
                        visible: showNameCheckBox.checked

                        onPaint: function(){
                            var ctx = getContext("2d")
                            ctx.clearRect(0, 0, checkboxNameCanvas.width, checkboxNameCanvas.height)
                            ctx.strokeStyle = backgroundColor
                            ctx.lineWidth = 2
                            ctx.beginPath()
                            ctx.moveTo(0, checkboxNameCanvas.height / 2)
                            ctx.lineTo(checkboxNameCanvas.width / 3, checkboxNameCanvas.height)
                            ctx.lineTo(checkboxNameCanvas.width, 0)
                            ctx.stroke()
                        }
                    }
                }

                onClicked: function(){
                    updateCamData("nameEnable", showNameCheckBox.checked)
                }
            }

            Text {
                text: qsTr("Show name")
                color: textColor
                font.pixelSize: 14
                verticalAlignment: Text.AlignVCenter
            }
        }

        Button{
            id: _redrawButton
            text: qsTr("Redraw")
            font.pixelSize: 14
            visible: false
            Material.foreground: textColor
            background: Rectangle{
                radius: 9
                color: primaryColor
            }

            onClicked: function(){
                redrawPolygon();
            }
        }

        Button{
            width: parent.width
            height: 48
            text: qsTr("Delete")
            font.pixelSize: 14
            font.weight: Font.Bold
            Material.foreground: "white"
            icon.source: "qrc:/src/assets/images/delete_icon.svg"
            icon.width: 18
            icon.height: 18
            background: Rectangle{
                radius: 6
                color: "#ED4845"
                MouseArea{
                    anchors.fill: parent
                    hoverEnabled: true
                    cursorShape: Qt.PointingHandCursor
                    onEntered: {
                        parent.color = "#E03E3B"
                    }
                    onExited: {
                        parent.color = "#ED4845"
                    }
                }
            }

            onClicked: function(){
                deleteCamera()
            }
        }
    }

    Connections{
        target: map2dController
        function onThemeChangeSignal(){
            textColor = map2dController.get_color_theme_by_key("text_color_all_app")
            primaryColor = map2dController.get_color_theme_by_key("primary")
            backgroundColor = map2dController.get_color_theme_by_key("main_background")
            borderColor = Qt.rgba(
                textColor.r,
                textColor.g,
                textColor.b,
                0.5
            )
            nonActiveColor = Qt.rgba(
                textColor.r,
                textColor.g,
                textColor.b,
                0.35
            )
            checkboxFovCanvas.requestPaint()
            checkboxNameCanvas.requestPaint()
        }
    }

    property Button selectedButton: {
        switch (camData && camData.size) {
            case 1: return smallButton
            case 2: return mediumButton
            case 3: return largeButton
            default: return null
        }
    }
}
