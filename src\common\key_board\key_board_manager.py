from PySide6.QtCore import Signal,QObject,Qt,QEvent
from src.utils.camera_qsettings import Camera_Qsettings
from src.common.controller.main_controller import main_controller
import logging
from src.common.key_board.shortcut_key import Short<PERSON><PERSON><PERSON><PERSON>,ShortCutKeyModel,shortcut_key_model_manager
logger = logging.getLogger(__name__)

class KeyBoardManager(QObject):
    __instance = None
    def __init__(self):
        super().__init__()
        self.shortcut_keys = {}
        self.create_keys_base()
        self.keys = {}
        self.timer_list = []
        self.is_keyboard = True # Cờ này quản lý việc chỉ lắng nghe keyboard khi đang ở màn hình CameraScreen
    @staticmethod
    def get_instance():
        if KeyBoardManager.__instance is None:
            KeyBoardManager.__instance = KeyBoardManager()
        return KeyBoardManager.__instance

    def create_keys_base(self):
        if len(Camera_Qsettings.get_instance().json_shortcut_ids) == 0:
            # self.shortcut_keys[str(Qt.Key.Key_Slash)] = {'data': [],'func': None} # '/' key
            # self.shortcut_keys[str(Qt.Key.Key_Asterisk)] = {'data': [],'func': None} # '*' key {'id': None,'view': view_name,'tree_type': None,'key_list': None}
            self.shortcut_keys[str(Qt.Key.Key_Alt)] = {'data': [],'func_start_key': None} # 'Alt' key
        else:
            self.shortcut_keys = Camera_Qsettings.get_instance().json_shortcut_ids

        # logger.debug(f'create_keys_base = {self.shortcut_keys}')
    def set_func(self,start_key = None, func = None,func_type = None):
        # if start_key == Qt.Key.Key_Slash or start_key == Qt.Key.Key_Asterisk:
        #     data = self.shortcut_keys[str(start_key)]
        #     data['func'] = func
        if start_key == Qt.Key.Key_Alt:
            data = self.shortcut_keys[str(start_key)]
            if func_type == 'func_start_key':
                data['func_start_key'] = func

    def add_key(self, start_key = None, keys = None):
        if str(start_key) in self.shortcut_keys:
            self.shortcut_keys[str(start_key)] = keys
        else:
            self.shortcut_keys[str(start_key)] = keys

    def add_data(self,start_key = None,id = None, name = None, tree_type = None,key_list = None):
        for item in self.shortcut_keys[str(start_key)]['data']:
            if item['name'] == name and tree_type == item['tree_type']:
                item['id'] = id
                Camera_Qsettings.get_instance().set_shortcut_ids(json_shortcut_ids=self.shortcut_keys)
                return
        self.shortcut_keys[str(start_key)]['data'].append({'id': id,'name': name,'tree_type':tree_type,'key_list':key_list})
        Camera_Qsettings.get_instance().set_shortcut_ids(json_shortcut_ids=self.shortcut_keys)

    def remove_data(self,start_key = None,name = None, tree_type = None):
        pass
        # data = self.shortcut_keys[str(start_key)]
        # for item in data['data']:
        #     if name == item['name'] and tree_type == item['tree_type']:
        #         data['data'].remove(item)
        #         Camera_Qsettings.get_instance().set_shortcut_ids(json_shortcut_ids=self.shortcut_keys)
        #         break

    def get_shortcut_id(self,start_key = None, name = None,tree_type = None):
        data = self.shortcut_keys[str(start_key)]
        for item in data['data']:
            if name == item['name'] and tree_type == item['tree_type']:
                return item['id']
        return None

    def find_shortcut_id(self,start_key = None, id = None):
        data = self.shortcut_keys[str(start_key)]
        for item in data['data']:
            if item['id'] == id:
                return id
        return None

    def run_func(self,start_key = None,id = None):
        data = self.shortcut_keys[str(start_key)]
        # logger.debug(f'run_func = {data}')
        for item in data['data']:
            if item['id'] == id:
                name = item['name']
                logger.debug(f'run_func = {name}')
                tree_type = item['tree_type']
                data['func'](name = name,tree_type = tree_type)
                break

    def get_start_key(self):
        if len(self.keys) > 0:
            for key, item in self.keys.items():
                return key
        return None
    def keys_to_number(self,number_list = []):
        result = 0
        for number in number_list:
            if number == Qt.Key.Key_0 or number == Qt.Key.Key_Insert:
                result = result*10 + 0
            elif number == Qt.Key.Key_1 or number == Qt.Key.Key_End:
                result = result*10 + 1
            elif number == Qt.Key.Key_2 or number == Qt.Key.Key_Down:
                result = result*10 + 2
            elif number == Qt.Key.Key_3 or number == Qt.Key.Key_PageDown:
                result = result*10 + 3
            elif number == Qt.Key.Key_4 or number == Qt.Key.Key_Left:
                result = result*10 + 4
            elif number == Qt.Key.Key_5 or number == Qt.Key.Key_Clear:
                result = result*10 + 5
            elif number == Qt.Key.Key_6 or number == Qt.Key.Key_Right:
                result = result*10 + 6
            elif number == Qt.Key.Key_7 or number == Qt.Key.Key_Home:
                result = result*10 + 7
            elif number == Qt.Key.Key_8 or number == Qt.Key.Key_Up:
                result = result*10 + 8
            elif number == Qt.Key.Key_9 or number == Qt.Key.Key_PageUp:
                result = result*10 + 9
            else:
                return None
        return result
    def is_number(self,key):
        if key == Qt.Key.Key_0 or \
            key == Qt.Key.Key_1 or \
            key == Qt.Key.Key_2 or \
            key == Qt.Key.Key_3 or \
            key == Qt.Key.Key_4 or \
            key == Qt.Key.Key_5 or \
            key == Qt.Key.Key_6 or \
            key == Qt.Key.Key_7 or \
            key == Qt.Key.Key_8 or \
            key == Qt.Key.Key_9 or \
            key == Qt.Key.Key_Insert or \
            key == Qt.Key.Key_End or \
            key == Qt.Key.Key_Down or \
            key == Qt.Key.Key_PageDown or \
            key == Qt.Key.Key_Left or \
            key == Qt.Key.Key_Clear or \
            key == Qt.Key.Key_Right or \
            key == Qt.Key.Key_Home or \
            key == Qt.Key.Key_Up or \
            key == Qt.Key.Key_PageUp:
            return True
        return False
    def stop_timer_list(self):
        for timer in self.timer_list:
            try:
                timer.stop()
            except Exception as e:
                logger.error(f'stop_timer_list error: {e}')
        self.timer_list = []

class KeyPressFilter(QObject):
    def __init__(self):
        super().__init__()
        self.key_pressed = False
    def eventFilter(self, obj, event):
        # tạm thời hiện tại chỉ lắng nghe keyboard khi ở màn hình CameraScreen thôi
        # logger.debug(f"event dddd")
        if event.type() == QEvent.KeyPress and not self.key_pressed:
            key_event = event
            key = key_event.key()
            # logger.debug(f'eventFilter = {key_event} - {key}')
            # Xử lý sự kiện keyPressEvent ở đây
            if int(key) in shortcut_key_model_manager.shortcut_key_list:
                key_board_manager.keys.clear()
                key_board_manager.keys[str(key)] = {'key_list': []}
            elif str(key) in key_board_manager.shortcut_keys:
                if key == Qt.Key.Key_Alt:
                    key_board_manager.stop_timer_list()
                    key_board_manager.keys.clear()
                    key_board_manager.keys[str(key)] = {'screen_number': [],'number_camera':[],'screen_selected': None}
                    # show number man hinh chinh
                    custom_tab_widget = main_controller.list_parent['CustomTabWidget']
                    widget = custom_tab_widget.getCurrentWidget()
                    # widget.show_screen_index(0)
                    # widget.show_item_index()
                    ########################
                    key_board_manager.keys[str(key)][0] = custom_tab_widget
                    screen_list = key_board_manager.shortcut_keys[str(Qt.Key.Key_Alt)]['func_start_key']()
                    if len(screen_list) > 0:
                        for index,screen in enumerate(screen_list):
                            key_board_manager.keys[str(key)][index + 1] = screen
                            virtual_window_widget = screen[1]
                            # virtual_window_widget.show_screen_index(index + 1)
                            # virtual_window_widget.show_item_index()
                    # logger.debug(f'key_board_manager.keys = {key_board_manager.keys}')
            else:
                start_key = key_board_manager.get_start_key()
                if start_key is not None:
                    if start_key == str(Qt.Key.Key_Slash) or start_key == str(Qt.Key.Key_Asterisk):
                        # logger.debug(f'start key = {start_key}')
                        if key != Qt.Key.Key_Return and key != Qt.Key.Key_Enter:
                            for idx,value in key_board_manager.keys.items():
                                value['key_list'].append(key)
                                # print(f"key_list: {value}")
                                break
                        else:

                            for idx,value in key_board_manager.keys.items():
                                logger.debug(f'idx = {idx} value = {value}')
                                if len(value['key_list']) > 0:
                                    number = key_board_manager.keys_to_number(number_list=value['key_list'])

                                    # key_board_manager.run_func(start_key=idx,id = number)
                                    ok = shortcut_key_model_manager.run_func(start_key=int(idx),id = number)
                                    if not ok:
                                        shortcut_key_model_manager.notification_signal.emit("This ShortcutID does not exist.")
                                    break
                            key_board_manager.keys = {}
                    elif start_key == str(Qt.Key.Key_Alt):
                        # logger.debug(f'start key = {start_key}')
                        if key != Qt.Key.Key_Return and key != Qt.Key.Key_Enter:
                            for idx,value in key_board_manager.keys.items():
                                if key_board_manager.is_number(key=key):
                                    # Chỉ xử lý key là ký tự number
                                    value['screen_number'].append(key)
                                    number = key_board_manager.keys_to_number(number_list=value['screen_number'])
                                    if number in key_board_manager.keys[start_key] and value['screen_selected'] is None:
                                        value['screen_selected'] = number
                                        # tim thấy số màn hình mà nguoi dùng đã chọn
                                        if number == 0:
                                            custom_tab_widget = key_board_manager.keys[start_key][0]
                                            widget = custom_tab_widget.getCurrentWidget()
                                            widget.change_screen_index_color()
                                            # widget_selected = grid_item_selected.data['widget']
                                            # if widget_selected is not None:
                                            #     # bỏ focus camera item trước đó
                                            #     if hasattr(widget_selected.stack_item, 'grid_item_unclicked'):
                                            #         widget_selected.stack_item.grid_item_unclicked()
                                        else:

                                            virtual_window = key_board_manager.keys[start_key][number]
                                            screen_index = virtual_window[0]
                                            virtual_window_widget = virtual_window[1]
                                            virtual_window_widget.change_screen_index_color()
                                            # widget_selected = grid_item_selected.data['widget']
                                            # if widget_selected is not None:
                                            #     # bỏ focus camera item trước đó
                                            #     if hasattr(widget_selected.stack_item, 'grid_item_unclicked'):
                                            #         widget_selected.stack_item.grid_item_unclicked()

                                    else:
                                        # Trường hợp ko tìm thấy mình hình thì check xem phím tắt trước đã tìm thấy chưa
                                        if value['screen_selected'] is not None:

                                            # Tiếp tục nhận key để tìm camera item trong grid
                                            if value['screen_selected'] == 0:
                                                # Man hinh chinh
                                                value['number_camera'].append(key)
                                                number = key_board_manager.keys_to_number(number_list=value['number_camera'])
                                                screen_widget = key_board_manager.keys[start_key][value['screen_selected']]
                                                widget = screen_widget.getCurrentWidget()
                                                widget.find_item_index(number)
                                            else:

                                                # man hinh virtual
                                                value['number_camera'].append(key)
                                                number = key_board_manager.keys_to_number(number_list=value['number_camera'])
                                                vitual_window_widget = key_board_manager.keys[start_key][value['screen_selected']]
                                                vitual_window_widget[1].find_item_index(number)
                                    break

                else:

                    # widget_selected = grid_item_selected.data['widget']
                    # screen = grid_item_selected.data['screen']
                    # if widget_selected is not None:
                        if key == Qt.Key.Key_Delete:
                            # Xử lý phím Delete
                            logger.info("Delete key pressed in KeyPressFilter")
                            # screen = grid_item_selected.data['screen']
                            # if screen == 'Main':
                            #     custom_tab_widget = main_controller.list_parent['CustomTabWidget']
                            #     widget = custom_tab_widget.getCurrentWidget()
                            #     # Gọi phương thức xóa camera được chọn
                            #     widget.delete_selected_cameras()
                            # elif screen is not None and screen in main_controller.list_parent:
                            #     camera_grid_base = main_controller.list_parent[screen][1]
                                # Gọi phương thức xóa camera được chọn
                                # camera_grid_base.delete_selected_cameras()
                        elif key == Qt.Key.Key_6:
                            # screen =  grid_item_selected.data['screen']
                            # type = grid_item_selected.data['type']
                            # if screen == 'Main':
                            #     custom_tab_widget = main_controller.list_parent['CustomTabWidget']
                            #     widget = custom_tab_widget.getCurrentWidget()
                                # widget.process_change_item(action = 6)
                            # elif screen is not None and screen in main_controller.list_parent:
                            #     camera_grid_base = main_controller.list_parent[screen][1]
                            #     camera_grid_base.process_change_item(action = 6)
                            pass
                        elif key == Qt.Key.Key_4:
                            # screen =  grid_item_selected.data['screen']
                            # type = grid_item_selected.data['type']
                            # if screen == 'Main':
                            #     custom_tab_widget = main_controller.list_parent['CustomTabWidget']
                            #     widget = custom_tab_widget.getCurrentWidget()
                                # widget.process_change_item(action = 4)
                            # elif screen is not None and screen in main_controller.list_parent:
                            #     camera_grid_base = main_controller.list_parent[screen][1]
                            #     camera_grid_base.process_change_item(action = 4)
                            pass
                        elif key == Qt.Key.Key_2:
                            # screen =  grid_item_selected.data['screen']
                            # type = grid_item_selected.data['type']
                            # if screen == 'Main':
                            #     custom_tab_widget = main_controller.list_parent['CustomTabWidget']
                            #     widget = custom_tab_widget.getCurrentWidget()
                            #     widget.process_change_item(action = 2)
                            # elif screen is not None and screen in main_controller.list_parent:
                            #     camera_grid_base = main_controller.list_parent[screen][1]
                            #     camera_grid_base.process_change_item(action = 2)
                            pass
                        elif key == Qt.Key.Key_8:
                            # screen =  grid_item_selected.data['screen']
                            # type = grid_item_selected.data['type']
                            # if screen == 'Main':
                            #     custom_tab_widget = main_controller.list_parent['CustomTabWidget']
                            #     widget = custom_tab_widget.getCurrentWidget()
                            #     widget.process_change_item(action = 8)
                            # elif screen is not None and screen in main_controller.list_parent:
                            #     camera_grid_base = main_controller.list_parent[screen][1]
                            #     camera_grid_base.process_change_item(action = 8)
                            pass
                        else:
                            # trường hợp start_key = None là đang không lắng nghe key đặc biệt '/' "*" "Alt"
                            # xử lý các trường hợp lắng nghe phím đơn khi đang focus vào camera rồi (R, P, D, Del, I, Enter)
                            # logger.debug(f'eventFilter1 =')
                            # if hasattr(widget_selected, 'callback_keyPressEvent'):
                            #     # widget_selected là object CameraWidget
                            #     widget_selected.callback_keyPressEvent(event)
                            pass

            self.key_pressed = True
        elif event.type() == QEvent.KeyRelease and self.key_pressed:
            # logger.debug(f'QEvent.KeyRelease')
            # widget_selected = grid_item_selected.data['widget']
            # if widget_selected is not None:
            #     # logger.debug(f'QEvent.KeyRelease1')
            #     if hasattr(widget_selected, 'callback_keyReleaseEvent'):
            #         # logger.debug(f'QEvent.KeyRelease2')
            #         widget_selected.callback_keyReleaseEvent(event)
            self.key_pressed = False
        return super().eventFilter(obj, event)


key_board_manager = KeyBoardManager.get_instance()
