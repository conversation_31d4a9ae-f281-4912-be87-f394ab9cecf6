
from src.styles.style import Style
from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import QWidget
import os
import logging
logger = logging.getLogger(__name__)

basedir = os.path.dirname(__file__)
class BaseWidget(QWidget):
    clicked = Signal(tuple)
    def __init__(self):
        super().__init__()
        self.setObjectName("BaseWidget")
        self.main_widget = None

    def set_style(self,hover = False):
        if hover:
            self.setStyleSheet(f'''
                QWidget#BaseWidget {{
                        background-color: transparent;
                        border-radius: 8px;
                        border: 1px solid #FFFFFF;
                        }}
                QLabel {{
                        color: {Style.PrimaryColor.menu_title};
                        font-size: 14px;
                        }}  
                QPushButton{{
                    background-color: {Style.PrimaryColor.primary};
                    color: {Style.PrimaryColor.white};
                    font-size: 14px;
                    border: none;
                    border-radius: 4px;
                    padding: 1px;}}
                QPushButton::disabled{{
                    background-color: #5c5c5c;}}
                QPushButton::pressed{{
                    background-color: {Style.PrimaryColor.button_color};}}   

            '''
            )
        else:
            self.setStyleSheet(f'''
                QWidget#BaseWidget {{
                        background-color: transparent;
                        border-radius: 8px;
                        border: 1px solid #FFFFFF;
                        }}
                QLabel {{
                        color: {Style.PrimaryColor.menu_title};
                        font-size: 14px;
                        }}  
                QPushButton {{
                        color: {Style.PrimaryColor.white_2};
                        }}  
                QPushButton{{
                    background-color: {Style.PrimaryColor.primary};
                    color: {Style.PrimaryColor.white};
                    font-size: 14px;
                    border: none;
                    border-radius: 4px;
                    padding: 1px;}}
                QPushButton::disabled{{
                    background-color: #5c5c5c;}}
                QPushButton::pressed{{
                    background-color: {Style.PrimaryColor.button_color};}}       

            '''
            )



    # def enterEvent(self, event):
    #     if self.main_widget is not None:
    #     # self.set_style(hover=True)

    # def leaveEvent(self, event):
    #     pass
    #     # self.set_style(hover=False)
