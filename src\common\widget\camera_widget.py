# from src.common.qml.models.common_enum import CommonEnum
# import pickle
# from typing import List, Callable
# import threading
# from src.common.controller.controller_manager import controller_manager,Controller
# from src.common.model.main_tree_view_model import TreeType
# from src.common.widget.custom_image import CustomImage
# import logging
# import time
# from dataclasses import replace
# import math
# from PySide6 import QtGui
# from PySide6.QtCore import Signal, Qt, QRect, QPoint, QEvent, QTimer, QMimeData, QSize, QPropertyAnimation
# from PySide6.QtWidgets import QLabel, QHBoxLayout, QWidget, QVBoxLayout, QMenu, QSizePolicy, QStackedWidget, \
#     QApplication, QDialog, QGraphicsBlurEffect
# from PySide6.QtGui import QAction, QPen, QPixmap, QPainter, QColor, QDrag, QCursor, QKeySequence
# import cv2
# from src.common.widget.menus.custom_menus import CustomMenuWithCheckbox, SubMenuOpenCameraInTab
# from src.common.widget.warning_alert_camera_widget import WarningAlertCameraWidget
# from src.common.widget.button_state import ButtonState

# from src.styles.style import Style, Theme
# from src.common.controller.main_controller import main_controller
# from src.common.model.camera_model import Camera, CameraModel
# from src.common.model.record_model import RecordData, record_model_manager
# from src.common.model.camera_model import Camera, CameraModel,PtzCapType

# from src.common.widget.ptz_widget import PtzDialog
# from src.common.camera.player import StreamCameraType,Player,CameraState
# from src.common.camera.video_player_manager import video_player_manager
# from queue import Queue
# from src.utils.config import Config
# # from src.common.widget.custom_opengl_widget import CustomOpenGLWidget
# from src.presentation.device_management_screen.widget.list_custom_widgets import CustomIcon
# from src.common.widget.notifications.notify import Notifications
# from src.common.onvif_api.worker_thread import WorkerThread
# from src.common.key_board.key_board_manager import key_board_manager
# from src.common.model.aiflows_model import AiFlow,AiFlowModel,aiflow_model_manager
# # import video_play_back lib ######
# from src.common.onvif_api.calib_data import CalibData,Manufacturer
# from src.presentation.device_management_screen.widget.ai_state import AIFlowType
# from src.common.qml.models.timelinecontroller import TimeLineController,SpeedStatus
# from src.common.qml.models.schedule_model import schedule_manager,ScheduleModel
# import datetime
# import uuid
# from typing import Callable
# import threading
# logger = logging.getLogger(__name__)

# class CameraWidget(QWidget):
#     widget_clicked = Signal(QWidget, bool)
#     change_timeline_to_live = Signal()
#     camera_state_changed = Signal(str)
#     widget_size_changed = Signal(list)
#     snapshot_updated_signal = Signal(QtGui.QPixmap)
#     ptz_signal = Signal(str)
#     close_widget_signal = Signal(object)
#     right_mouse_event = Signal(list)
#     full_screen_signal = Signal(Camera)
#     visibilityChanged = Signal(bool)
#     # debug
#     ENABLE_DEBUG = False
#     # count frame to show button reconnect
#     SHOW_BTN_RECONNECT_FRAME_COUNT = 3

#     warning_signal = Signal(tuple)

#     def __init__(self, width=None, height=None, stream_link="", camera_name=None, camera_id=None, maintain_aspect_ratio=True, parent=None,callback = None,
#                  root_width=None, root_height=None, camera_model: CameraModel = None, stream_type = StreamCameraType.sub_stream,
#                  tab_model = None,is_virtual_window = False, is_tracking_item=False, stack_item = None, use_post_process_image=False, callback_post_process_image = None, is_show_header = True, is_show_footer = True):
#         super().__init__(parent)

#         # Lưu trữ các tham số
#         self.tab_model = tab_model
#         self.is_virtual_window = is_virtual_window
#         self.stack_item = stack_item
#         self.is_show_header = is_show_header
#         self.is_show_footer = is_show_footer

#         # init visibility
#         self.installEventFilter(self)

#         # Initialize blur effect and animation
#         self.blur_effect = QGraphicsBlurEffect()
#         self.blur_effect.setBlurRadius(0)
#         self.blur_animation = QPropertyAnimation(self.blur_effect, b"blurRadius")
#         self.blur_animation.setDuration(500)  # 500ms duration
#         self.blur_animation.finished.connect(self._on_blur_animation_finished)
#         self.is_show_header = is_show_header
#         self.is_show_footer = is_show_footer
#         self.is_show_border = False
#         self.setFocusPolicy(Qt.StrongFocus)
#         self.setMouseTracking(True)
#         self.use_post_process_image = use_post_process_image
#         self.callback_post_process_image = callback_post_process_image
#         self.is_tracking_item = is_tracking_item
#         self.is_drawing = False
#         self.start_pos = None
#         self.end_pos = None
#         self.drag = None
#         self.flag_init_ui = True
#         self.now = time.time()
#         self.camera_model = camera_model
#         if self.camera_model is not None:
#             self.camera_model.change_model.connect(self.update_change_model)
#             self.controller:Controller = controller_manager.get_controller(server_ip=self.camera_model.get("server_ip"))
        
#         # logger.debug(f'TAG_INDEX: self.tab_model: {self.tab_model} - {self.tab_model.data.currentGrid} - {self.controller.current_grid_model}')
#         # tung.vu: Cập nhật logic chuyển stream khi kéo camera vào lưới - chọn stream_type
#         if self.tab_model is not None:
#             if self.tab_model.data.currentGrid['row'] > 2 and self.tab_model.data.currentGrid['column'] > 2:
#                 grid_item = self.tab_model.data.listGridData.get(self.stack_item.index, None)
#                 if grid_item is not None:
#                     if grid_item.is_fullscreen:
#                         self.stream_type = StreamCameraType.main_stream
#                     else:
#                         self.stream_type = StreamCameraType.sub_stream
#             else:
#                 self.stream_type = StreamCameraType.main_stream
#         else:
#             # use default stream_type from constructor
#             self.stream_type = stream_type

#         self.camera_status = True
#         self.camera_id = camera_id
#         self.previous_camera_status = True
#         self.frame_delay = 0

#         # self.controller.get_stream_url(cameraId=self.camera_model.get_property("id", None))
#         self.setObjectName(f'Camera_{camera_id}')
#         self.uuid = uuid.uuid4()
#         self.message = None
#         self.profileToken = self.camera_model.data.profileToken
#         self.key_pressed = False
#         self.callback = callback
#         self.camera_grid_widget = parent
#         self.camera_name = camera_name
#         self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
#         self.maintain_aspect_ratio = maintain_aspect_ratio
#         self.root_width = root_width
#         self.root_height = root_height
#         self.preview_width = width
#         self.preview_height = height - 24
#         self.player = None

#         video_player_manager.register_player(self,self.camera_model,self.stream_type)
#         self.camera_state_changed.connect(self.on_camera_state_changed)
#         self.widget_size_changed.connect(self.on_widget_size_changed)
#         self.warning_signal.connect(self.trigger_warning_alert)
#         self.camera_state = CameraState.stopped
#         self.previous_camera_state = CameraState.stopped
#         self.widget_size = None
#         self.previous_widget_size = None
#         self.is_support_ptz = False
#         self.root_stack_widget = None
#         self.hl_btn_camera_widget = None
#         self.hl_lb_info_camera_widget = None
#         self.lb_camera_name = None
#         self.lb_camera_state = None
#         self.preview_camera = None
#         self.count_try_reconnect = 0
#         self.camera_disconnect = QtGui.QPixmap(Style.PrimaryImage.ic_camera_disconnect)
#         self.restart_path = QtGui.QPixmap(Style.PrimaryImage.restart_path)
#         self.camera_connecting = QtGui.QPixmap(Style.PrimaryImage.ic_camera_connecting)
#         self.camera_pause = QtGui.QPixmap(Style.PrimaryImage.pause_path)
#         self.recording_header_path = QtGui.QPixmap(Style.PrimaryImage.recording_icon_header)
#         self.fullscreen_state = ButtonState.FullscreenType.EXIT_FULLSCREEN
#         self.snapshot = None
#         self.pix = None
#         self.enable_audio = False
#         self.footer_widget = None
#         self.header_top_widget = None
#         self.header_ptz_widget = None
#         self.background_state = None
#         self.warning_alert_widget = None
#         if Config.ENABLE_WARNING_ALERT_CAMERA:
#             self.alert_queue = []
#             self.alert_timer = QTimer()
#             self.alert_timer.timeout.connect(self.process_alerts)
#             self.alert_duration_timer = QTimer(self)
#             self.alert_duration_timer.setSingleShot(True)
#             self.alert_duration_timer.timeout.connect(self.handle_alert_timeout)
#             self.next_tracking_queue = []
#             self.next_tracking_timer = QTimer()
#             self.next_tracking_timer.setSingleShot(True)
#         self.load_ui()
#         self.flag_init_ui = False
#         self.is_ptz_dialog = False
#         self.crop_frame_dialog = None
#         self.ptz_dialog = None
#         self.start_pos = 0
#         self.end_pos = 0
#         self.is_3d_locate = False
#         self.is_crop_frame = False
#         self.is_ptz_arrow = False
#         self.start_time = time.time()
#         self.start_wheel_time = time.time()
#         self.timer = None
#         self.previous_direction = None
#         self.gesture_status = False
#         self.calib_data = self.get_calib_data()
#         self.match_start_point = None
#         self.match_end_point = None
#         self.allow_ptz_arrow_queue = False
#         self.coordinate_queue = Queue()
#         self.current_time = time.time()
#         self.previous_time = time.time()
#         # Initialize worker threads with proper callbacks
#         self.process_queue_thread = WorkerThread(parent=self, target=self.process_queue, callback=self.on_process_queue_complete)
#         self.process_joystick_queue_thread = WorkerThread(parent=self, target=self.process_joystick_queue, callback=self.on_process_joystick_queue_complete)
#         self.current_joystick_msg = {}
#         self.previous_joystick_msg = {}
#         # Config TimeLineControl
#         self.record_data:RecordData = record_model_manager.register_record_data(self)


#         self.timelinecontroller = None
#         self.record_url = None
#         self.start_duration = 0
#         self.end_duration = 0
#         self.seek_time = 0
#         # Add blur effect
#         self.preview_camera.setGraphicsEffect(self.blur_effect)
#         record_model_manager.start_subcribe(camera_id=self.camera_model.get_property("id", None),widget=self)
#         self.input_queue = Queue()
#         self.threads = self.start_threads(1,self.process_data)

#     def start_subcribe(self,camera_id = None):
#         def callback():
#             self.controller.get_videos(parent=self, cameraIds=self.camera_model.get_property("id", None))
#         self.controller.api_client.start_subcribe(data={"cameraId": camera_id},callback = None)

#     def stop_subcribe(self,camera_id = None):
#         self.controller.start_subcribe(data={"cameraId": camera_id})

        
#     def _on_blur_animation_finished(self):
#         """Handle blur animation completion"""
#         if self.blur_effect.blurRadius() == 0:
#             self.blur_effect.setEnabled(False)

#     def create_timelinecontroller(self):
#         self.timelinecontroller = TimeLineController(parent=self)
#         self.timelinecontroller.positionClicked.connect(self.positionClicked)
#         self.timelinecontroller.isPlayChanged.connect(self.isPlayChanged)
#         self.timelinecontroller.isLiveChanged.connect(self.isLiveChanged)
#         self.timelinecontroller.isNextChunkClicked.connect(self.isNextChunkClicked)
#         self.timelinecontroller.isPreviousChunkClicked.connect(self.isPreviousChunkClicked)
#         self.timelinecontroller.speedStatusChanged.connect(self.speedStatusChanged)
#         self.timelinecontroller.nextFrameChanged.connect(self.nextFrameChanged)
#         self.timelinecontroller.hoverPositionChanged.connect(self.hoverPositionChanged)
#         self.timelinecontroller.showMenu.connect(self.show_menu)
#         return self.timelinecontroller
    
#     def recordDataChanged(self):
#         if self.timelinecontroller is not None:
#             if not self.timelinecontroller.isTimeLine:
#                 self.timelinecontroller.setIsTimeLine(True)
#                 self.timelinecontroller.initData(self.record_data.start_duration, self.record_data.end_duration)
#             self.timelinecontroller.updateRecordDuration(self.record_data)
            

#     def process_player(self,player:Player):
#         if not self.is_virtual_window:
#             logger.debug(f'player ========== START = {player}')
#         self.player = player
#         self.player.connect_status = True
#         self.player.set_send_mat_frame(self.use_post_process_image and self.callback_post_process_image is not None)
#         self.player.register_signal(self)
#         self.player.update_resize(width = self.preview_width, height = self.preview_height,uuid = self.uuid)
#         if self.tab_model is not None:
#             for key, grid_item in self.tab_model.data.listGridData.items():
#                 if self == grid_item.widget:
#                     virtual_widget = grid_item.virtual_widget
#                     if virtual_widget is not None:
#                         virtual_widget.player = self.player
#                         virtual_widget.player.set_send_mat_frame(virtual_widget.use_post_process_image and virtual_widget.callback_post_process_image is not None)
#                         virtual_widget.player.register_signal(virtual_widget)
#                         virtual_widget.player.update_resize(width = virtual_widget.preview_width, height = virtual_widget.preview_height,uuid = virtual_widget.uuid)
#         else:
#             logger.error(f'process_player: tab_model is None')
            
#         logger.debug(f'player ========== START = {self.player.stream_type}')
#         if not self.player.isRunning():
#             logger.debug(f'player ========== START')
#             if self.player.stream_type != CommonEnum.StreamType.VIDEO_STREAM:
#                 self.player.start_thread()

#         def update_stream_url(reponse, streamIndex):
#             if self.player is not None:
#                 # [
#                 #     {
#                 #         "url": "https://implacable-ai.ai-vlab.com/camera/fb2c9da1-8ccf-4ade-9745-b7733d236c5e_0.flv",
#                 #         "index": 0,
#                 #         "state": "CONNECTED"
#                 #     },
#                 #     {
#                 #         "url": "https://implacable-ai.ai-vlab.com/camera/fb2c9da1-8ccf-4ade-9745-b7733d236c5e_1.flv",
#                 #         "index": 1,
#                 #         "state": "CONNECTED"
#                 #     }
#                 # ]
#                 # Giải thích logic:
#                 # - Nếu streamIndex trùng với index của item và state là CONNECTED thì lấy url của item đó
#                 # - Nếu không thì lấy url của item có state là CONNECTED
#                 data = reponse.json()
#                 logger.info(f'update_stream_url: data = {data}')
#                 # Lọc chỉ các luồng có trạng thái CONNECTED
#                 connected_streams = [item for item in data if item["state"] == "CONNECTED" or item["state"] is None]
                
#                 if connected_streams:
#                     # Đầu tiên tìm luồng phù hợp với chỉ số được yêu cầu
#                     target_stream = next((stream for stream in connected_streams if stream["index"] == streamIndex), None)
                    
#                     # Nếu không tìm thấy luồng phù hợp với chỉ số, sử dụng luồng kết nối đầu tiên có sẵn
#                     if target_stream is None:
#                         target_stream = connected_streams[0]
                        
#                     self.player.stream_link = target_stream["url"]
#                     logger.debug(f'update_stream_url = {target_stream["url"]}')
#                 else:
#                     logger.debug(f'update_stream_url: Không có url nào được lấy')
#         def update_stream_url_ai_flow(reponse, aiFlowId):
#             if self.player is not None:
#                 # if "Camera_58.187.139.132_7" == self.camera_model.data.name:
#                     # logger.info(f'process_player {reponse.text}')
#                 self.player.stream_link = reponse.text
#                 logger.debug(f'update_stream_url_ai_flow = {reponse.text}')

#         if self.stream_type == StreamCameraType.main_stream:
#             self.controller.get_stream_url_thread(cameraId=self.camera_model.get_property("id", None), streamIndex=0, callback=update_stream_url)
#         elif self.stream_type == StreamCameraType.sub_stream:
#             self.controller.get_stream_url_thread(cameraId=self.camera_model.get_property("id", None), streamIndex=1, callback=update_stream_url)
#         elif self.stream_type == StreamCameraType.video_stream:
#             self.player.load_media(self.record_url, seek_time=self.seek_time,start_duration=self.start_duration,end_duration=self.end_duration)
#             self.timelinecontroller.register_player(player=self.player)
#             # widget.player.set_position(duration_time_need_seek)
#             self.player.play_video()
#         else:
#             ai_type =  StreamCameraType.get_ai_type(self.stream_type) 
#             logger.debug(f'ai_type: {ai_type}')
#             list_aiflow = self.controller.get_aiflows(cameraIds=self.camera_model.get_property("id", None))
#             logger.debug(f'list_aiflow: {list_aiflow}')
#             if list_aiflow is not None:
#                 for aiflow_model in list_aiflow:
#                     logger.debug(f'aiflow_model: {aiflow_model.data.type} - {ai_type}')
#                     if ai_type == aiflow_model.data.type:
#                         self.controller.get_ai_stream_url_thread(aiFlowId=aiflow_model.data.id,callback=update_stream_url_ai_flow)
#             else:
#                 logger.error(f'process_player: list_aiflow is None')

#     def get_calib_data(self):
#         if self.camera_model.data.cameraBranch == "Avigilon":
#             return CalibData.get_data(type=Manufacturer.Avigilon)
#         else:
#             return CalibData.get_data(type = Manufacturer.Invalid)

#     def stop_video(self):
#         if self.player is not None:
#             self.player.stop_video()
#             record_model_manager.close_record_signal.emit(self.camera_id)
#             video_player_manager.unregister_player(self)

#     def enterEvent(self, event):
#         if self.is_virtual_window:
#             return
#         super().enterEvent(event)
#         # Xử lý khi con trỏ chuột vào QWidget


#     def leaveEvent(self, event):
#         if self.is_virtual_window:
#             return
#         super().leaveEvent(event)


#     def btn_record_clicked(self):
#         pass

#     def btn_volume_clicked(self):
#         pass

#     def btn_crop_frame_clicked(self):
#         if self.is_crop_frame:
#             self.is_crop_frame = False
#             self.btn_crop_frame.set_icon(main_controller.get_theme_attribute("Image", "icon_crop_off"))
#             self.rectangle_widget.setVisible(False)
#             self.rectangle_widget.is_enable = False
#         else:
#             self.is_crop_frame = True
#             self.disable_header_top(is_3d_locate = True,is_ptz_dialog= True,is_ptz_arrow=True)
#             self.btn_crop_frame.set_icon(Style.PrimaryImage.icon_crop_on)
#             self.rectangle_widget.setVisible(True)
#             self.rectangle_widget.type = 'crop_frame'
#             self.rectangle_widget.is_enable = True
#             self.update_border()

#     def btn_ptz_arrow_clicked(self):
#         if self.is_ptz_arrow:
#             self.is_ptz_arrow = False
#             self.btn_ptz_arrow.set_icon(main_controller.get_theme_attribute("Image", "icon_ptz_arrow_off"))
#             self.rectangle_widget.setVisible(False)
#             self.rectangle_widget.is_enable = False
#         else:
#             self.is_ptz_arrow = True
#             self.disable_header_top(is_crop_frame = True,is_ptz_dialog= True,is_3d_locate=True)
#             self.btn_ptz_arrow.set_icon(Style.PrimaryImage.icon_ptz_arrow_on)
#             self.rectangle_widget.setVisible(True)
#             self.rectangle_widget.label.show()
#             self.rectangle_widget.type = 'ptz_arrow'
#             self.rectangle_widget.is_enable = True
#             self.update_border()

#     def btn_3_d_locate_clicked(self):
#         if self.is_3d_locate:
#             self.is_3d_locate = False
#             self.btn_3_d_locate.set_icon(main_controller.get_theme_attribute("Image", "icon_drag_zoom_off"))
#             self.rectangle_widget.setVisible(False)
#             self.rectangle_widget.is_enable = False

#         else:
#             self.is_3d_locate = True
#             self.disable_header_top(is_crop_frame = True,is_ptz_dialog= True,is_ptz_arrow=True)
#             self.btn_3_d_locate.set_icon(Style.PrimaryImage.icon_drag_zoom_on)
#             self.rectangle_widget.setVisible(True)
#             self.rectangle_widget.type = '3d_local'
#             self.rectangle_widget.is_enable = True
#             self.update_border()

#     def btn_ptz_clicked(self):
#         logger.debug(f'is_ptz_dialog = {self.is_ptz_dialog}')
#         if self.is_ptz_dialog:
#             self.is_ptz_dialog = False
#             self.btn_ptz.set_icon(main_controller.get_theme_attribute("Image", "icon_ptz_off"))
#             if self.ptz_dialog is not None:
#                 self.ptz_dialog.close()
#         else:
#             self.is_ptz_dialog = True
#             if self.camera_model.data.type == "AVIGILON":
#                 widget_pos = self.mapToGlobal(self.pos())

#                 self.ptz_dialog = PtzDialog(parent=self,controller=self.controller,camera_model=self.camera_model)
#                 # if self.ptz_dialog.is_ptz_avaiable():
#                 self.ptz_dialog.move(widget_pos.x() + self.preview_width - self.ptz_dialog.width(),widget_pos.y() + 24)
#                 self.ptz_dialog.show()
#                 self.btn_ptz.set_icon(Style.PrimaryImage.icon_ptz_on)
#                 self.update_border()
#             else:
#                 if self.camera_model.data.ptzCap is not None and len(self.camera_model.data.ptzCap) > 0:
#                     widget_pos = self.mapToGlobal(self.pos())

#                     self.ptz_dialog = PtzDialog(parent=self,controller=self.controller,camera_model=self.camera_model)
#                     # if self.ptz_dialog.is_ptz_avaiable():
#                     self.ptz_dialog.move(widget_pos.x() + self.preview_width - self.ptz_dialog.width(),widget_pos.y() + 24)
#                     self.ptz_dialog.show()
#                     self.btn_ptz.set_icon(Style.PrimaryImage.icon_ptz_on)
#                     self.update_border()
#                 else:
#                     # camera này không hỗ trợ PTZ
#                     Notifications(parent=main_controller.list_parent['CameraScreen'], title=self.tr('This Camera does not support PTZ'),icon=Style.PrimaryImage.fail_result)
#                 self.disable_header_top(is_3d_locate = True,is_crop_frame = True,is_ptz_arrow=True)
            
#     def disable_header_top(self,is_crop_frame = None,is_3d_locate = None,is_ptz_dialog = None, is_ptz_arrow = None):
#         if is_crop_frame:
#             if Config.ENABLE_CROP_FRAME:
#                 self.is_crop_frame = False
#                 self.btn_crop_frame.set_icon(main_controller.get_theme_attribute("Image", "icon_crop_off"))
#                 if self.crop_frame_dialog is not None:
#                     self.crop_frame_dialog.close()
#                 self.crop_frame_dialog = None
#                 self.rectangle_widget.setVisible(False)
#                 self.rectangle_widget.is_enable = False

#         if is_ptz_arrow:
#             if hasattr(self, 'is_ptz_arrow_configuration') and self.is_ptz_arrow_configuration:
#                 self.is_ptz_arrow = False
#                 self.btn_ptz_arrow.set_icon(main_controller.get_theme_attribute("Image", "icon_ptz_arrow_off"))
#                 self.rectangle_widget.label.hide()
#                 self.rectangle_widget.setVisible(False)
#                 self.rectangle_widget.is_enable = False

#         if is_3d_locate:
#             if hasattr(self, 'is_3d_locate_configuration') and self.is_3d_locate_configuration:
#                 self.is_3d_locate = False
#                 self.btn_3_d_locate.set_icon(main_controller.get_theme_attribute("Image", "icon_drag_zoom_off"))
#                 self.rectangle_widget.setVisible(False)
#                 self.rectangle_widget.is_enable = False

#         if is_ptz_dialog:
#             if self.camera_model.data.ptzCap is not None and len(self.camera_model.data.ptzCap) > 0:
#                 self.is_ptz_dialog = False
#                 self.btn_ptz.set_icon(main_controller.get_theme_attribute("Image", "icon_ptz_off"))
#                 if self.ptz_dialog is not None:
#                     self.ptz_dialog.close()
#                 self.ptz_dialog = None

#     # def btn_close_clicked(self):
#     #     self.close_widget_signal.emit((self, self.camera_name))

#     def update_border(self):
#         current_tab = main_controller.current_tab
#         if grid_item_selected.is_tab_index(current_tab):
#             camera_id = grid_item_selected.data['camera_id']
#             if camera_id != self.camera_id:
#                 self.grid_item_clicked(main_controller.current_tab)
#         else:
#             self.grid_item_clicked(main_controller.current_tab)

#     def update_state(self):
#         if self.camera_model != None:
#             icon = CameraModel.get_icon(self.camera_model.state_merged)
#             self.lb_camera_state.setPixmap(QtGui.QPixmap(icon))
#             if self.camera_model.data.aiFlowIds != None:
#                 for id in self.camera_model.data.aiFlowIds:
#                     aiflow:AiFlowModel = aiflow_model_manager.get_aiflow_model(id=id)
#                     if aiflow is not None and aiflow.data.is_apply():
#                         if aiflow.data.is_recognition_and_protection():
#                             self.recognition_security_icon.show()
#                         if aiflow.data.is_risk_identification():
#                             self.risk_identification_icon.show()

#         if grid_item_selected.is_tab_index(main_controller.current_tab):
#             if grid_item_selected.data['tab_index'] is not None and grid_item_selected.data['camera_id'] == self.camera_model.get_property("id", None):
#                 grid_item_selected.data['widget'] = self
            
#     def grid_item_clicked(self, current_tab):
#         if self.stack_item is not None:
#             self.stack_item.grid_item_clicked(current_tab)
#         #JoystickManager.get_instance().register(WidgetType.CameraScreen,self.key_received_signal)

#     def grid_item_unclicked(self):
#         widget = grid_item_selected.data['widget']
#         if widget is not None:
#             widget.enable_audio = False
#             if main_controller.audio_thread != None:
#                 main_controller.audio_thread.stop_thread()
#             widget.disable_header_top(is_crop_frame=True,is_3d_locate=True,is_ptz_dialog=True,is_ptz_arrow=True)
#             if self.stack_item is not None:
#                 self.stack_item.grid_item_unclicked()
#             grid_item_selected.set_data(screen = None,type = None,tab_index = None,camera_id = None, widget = None)
            
#     def switch_player(self,stream_type = StreamCameraType.main_stream):
#         logger.debug(f'switch_player: {stream_type}')
#         if stream_type != self.player.stream_type:
#             video_player_manager.unregister_player(self)
#             self.stream_type = stream_type
#             for key, grid_item in self.tab_model.data.listGridData.items():
#                 if self == grid_item.widget:
#                     virtual_widget = grid_item.virtual_widget
#                     if virtual_widget is not None:
#                         video_player_manager.unregister_player(virtual_widget)
#                         virtual_widget.stream_type = stream_type

#             video_player_manager.register_player(self,self.camera_model,stream_type)
#             if not self.player.isRunning():
#                 logger.info(f'player ==========')
#                 if self.player.stream_type != StreamCameraType.video_stream:
#                     self.player.start_thread()

#     @staticmethod
#     def mat_resize(width=None, height=None, maintain_aspect_ratio=True, mat=None):
#         resize_width = width
#         resize_height = height
#         h, w, c = mat.shape
#         if width is not None and height is not None:
#             if maintain_aspect_ratio:
#                 aspect_ratio = w / float(h)
#                 if width / float(height) > aspect_ratio:
#                     resize_width = int(aspect_ratio * height)
#                 else:
#                     resize_height = int(width / aspect_ratio)
#             else:
#                 resize_width = width
#                 resize_height = height
#         elif width is not None:
#             resize_width = width
#             resize_height = int((h / w) * resize_width)
#         elif height is not None:
#             resize_height = height
#             resize_width = int((w / h) * resize_height)

#         if resize_width is not None and resize_height is not None:
#             mat = cv2.resize(mat, (resize_width, resize_height))
#             # logger.debug(f'mat_resize() resize_width = {resize_width} resize_height = {resize_height}')
#         return mat

#     @staticmethod
#     def mat_to_q_pixmap(mat = None, width = None, height = None, x = None, y = None):
#         h, w, c = mat.shape
#         d = mat.dtype.itemsize
#         s = c * w * d
#         if width == None and height == None:
#             img = QtGui.QImage(
#                 mat, w, h, s, QtGui.QImage.Format_RGB888).rgbSwapped()
#         else:
#             img = QtGui.QImage(
#                 mat, w, h, s, QtGui.QImage.Format_RGB888).rgbSwapped()
#             img = img.copy(x, y, width, height)
#         return QtGui.QPixmap.fromImage(img)

#     def load_ui(self):
#         self.preview_camera = CustomImage()
#         self.preview_camera.setAlignment(Qt.AlignmentFlag.AlignCenter)
#         self.preview_camera.setObjectName("preview_camera")
#         self.preview_camera.setMinimumSize(100,100)
#         self.preview_camera.setContentsMargins(0, 0, 0, 0)
#         self.preview_camera.setMargin(0)
#         self.preview_camera.setAttribute(Qt.WA_OpaquePaintEvent, True)
#         self.preview_camera.setAttribute(Qt.WA_NoSystemBackground, True)
#         self.preview_camera.setSizePolicy(QSizePolicy.Policy.Ignored, QSizePolicy.Policy.Ignored)
        

#         self.root_stack_widget = QStackedWidget()
#         self.root_stack_widget.setContentsMargins(0, 0, 0, 0)
#         self.setStyleSheet("background-color: transparent;")
#         self.root_stack_widget.setStyleSheet(
#                 f'''
#                     QStackedWidget {{
#                         margin: 0px;
#                         padding: 0px;
#                         background-color: {main_controller.get_theme_attribute("Color", "camera_widget_background")};
#                         border: 0px solid transparent;
#                         border-bottom-left-radius: 0px;
#                         border-bottom-right-radius: 0px;
#                         border-top-right-radius: 0px;
#                         border-top-right-radius: 0px;
#                     }}
#                 '''
#             )
        
#         ###################
#         # add preview camera to layout
#         self.root_stack_widget.addWidget(self.preview_camera)
#         # self.root_stack_widget.addWidget(self.widget)
#         # if Config.ENABLE_WARNING_ALERT_CAMERA:
#         #     self.root_stack_widget.addWidget(self.warning_alert_widget)

#         self.root_stack_widget.setCurrentIndex(0)
#         self.layout_vertical = QVBoxLayout()
#         self.layout_vertical.setContentsMargins(0, 0, 0, 0)
#         self.layout_vertical.setSpacing(0)
#         self.layout_vertical.setAlignment(Qt.AlignmentFlag.AlignTop)
#         self.layout_vertical.addWidget(self.root_stack_widget)
#         self.setLayout(self.layout_vertical)

#         # warning alert
#         if self.is_tracking_item:
#             self.warning_alert_widget = WarningAlertCameraWidget(self)
#             self.warning_alert_widget.setStyleSheet('background-color: transparent;')
#             self.warning_alert_widget.setObjectName("warning_alert_widget")
#         self.camera_index_widget = QLabel(self)
#         self.camera_index_widget.setAlignment(Qt.AlignmentFlag.AlignTop|Qt.AlignmentFlag.AlignCenter)
#         self.camera_index_widget.setContentsMargins(0, 0, 0, 0)
#         self.camera_index_widget.setObjectName("camera_index_widget")
#         self.camera_index_widget.setStyleSheet(Style.StyleSheet.label_style1)
#         self.camera_index_widget.setVisible(False)
#         self.camera_index_widget.setGeometry(self.root_width/2 -50, self.root_height/2 - 50, 100, 100)
#         self.camera_index_widget.resize(100, 100)
        
#         if not self.is_tracking_item:
#             self.create_header_top_widget_for_camera()
#             self.header_top_widget.setVisible(False)
        
#         self.create_footer_widget_for_camera()
        
#         self.rectangle_widget = RectangleWidget(self, callback_mousePressEvent=self.callback_mousePressEvent,callback_mouseReleaseEvent=self.callback_mouseReleaseEvent,callback_mouseMoveEvent=self.callback_mouseMoveEvent, type='crop_frame')
#         self.rectangle_widget.setVisible(False)
        
#         # Create buffering widget with modern design
#         self.buffering_widget = QWidget(self)
#         self.buffering_widget.setAttribute(Qt.WA_TranslucentBackground)
#         self.buffering_widget.setStyleSheet(f"""
#             QWidget {{
#                 background-color: {main_controller.get_theme_attribute("Color", "buffering_background_80")};
#                 border-radius: 12px;
#                 border: 1px solid {main_controller.get_theme_attribute("Color", "border_color")};
#             }}
#             QLabel {{
#                 background-color: transparent;
#                 color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
#                 font-family: Arial;
#                 border: none;
#             }}
#         """)
#         self.buffering_widget.hide()
        
#         # Create layout for buffering widget
#         buffering_layout = QVBoxLayout()
#         buffering_layout.setSpacing(0)
#         buffering_layout.setContentsMargins(16, 16, 16, 16)
#         buffering_layout.setAlignment(Qt.AlignCenter)
        
#         # Add percentage text with modern style
#         self.percentage_text = QLabel("0%")
#         self.percentage_text.setAlignment(Qt.AlignCenter)
#         self.percentage_text.setStyleSheet(f"""
#             font-size: 28px;
#             font-weight: bold;
#             color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
#             padding: 4px;
#         """)
#         buffering_layout.addWidget(self.percentage_text)
        
#         self.buffering_widget.setLayout(buffering_layout)
#         # Connect theme change signal
#         main_controller.theme_change_signal.connect(self.update_buffering_theme)

#     def update_buffering_theme(self):
#         """Update buffering widget colors when theme changes"""
#         self.buffering_widget.setStyleSheet(f"""
#             QWidget {{
#                 background-color: {main_controller.get_theme_attribute("Color", "buffering_background_80")};
#                 border-radius: 12px;
#                 border: 1px solid {main_controller.get_theme_attribute("Color", "border_color")};
#             }}
#             QLabel {{
#                 background-color: transparent;
#                 color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
#                 font-family: Arial;
#                 border: none;
#             }}
#         """)
        
#         # Update percentage text color based on current progress
#         self._update_percentage_color(int(self.percentage_text.text().strip('%')))

#     def _update_percentage_color(self, percent):
#         """Update percentage text color based on progress"""
#         if percent < 30:
#             color = main_controller.get_theme_attribute("Color", "error_color")  # Red for early progress
#         elif percent < 70:
#             color = main_controller.get_theme_attribute("Color", "warning_color")  # Yellow for mid progress
#         else:
#             color = main_controller.get_theme_attribute("Color", "success_color")  # Green for near completion
            
#         self.percentage_text.setStyleSheet(f"""
#             font-size: 28px;
#             font-weight: bold;
#             color: {color};
#             padding: 4px;
#         """)

#     def on_buffering(self, percent):
#         # if percent < 100:
#         #     if not self.buffering_widget.isVisible():
#         #         # Calculate size and position for the buffering widget
#         #         widget_size = QSize(100, 70)  # Reduced size since we only show percentage
#         #         x = (self.width() - widget_size.width()) // 2
#         #         y = (self.height() - widget_size.height()) // 2
#         #         self.buffering_widget.setGeometry(x, y, widget_size.width(), widget_size.height())
#         #         self.buffering_widget.show()
            
#         #     # Update percentage text and color
#         #     self.percentage_text.setText(f"{int(percent)}%")
#         #     self._update_percentage_color(int(percent))
#         # else:
#         #     self.buffering_widget.hide()
#         if percent < 100:
#             self.show_status_message(f"{int(percent)}%", "red")
#         else:
#             self.hide_status_message()

#     def resizeEvent(self, event):
#         super().resizeEvent(event)
#         if hasattr(self, 'buffering_widget') and self.buffering_widget.isVisible():
#             widget_size = QSize(100, 70)  # Match the size from on_buffering
#             x = (self.width() - widget_size.width()) // 2
#             y = (self.height() - widget_size.height()) // 2
#             self.buffering_widget.setGeometry(x, y, widget_size.width(), widget_size.height())

#     def play(self):
#         self.player.play_video()

#     def create_video_widget_mac(self):
#         # Create a window container to hold the video
#         video_win_id = self.player.get_xwindow()
#         q_window = QtGui.QWindow.fromWinId(video_win_id)
#         container = QWidget.createWindowContainer(q_window, self)
#         container.setMinimumSize(640, 480)  # Adjust as needed
#         container.setMaximumSize(640, 480)
#         container.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
#         return container

#     def create_header_top_widget_for_camera(self):
#         self.header_top_widget = self.create_header_top_widget_base()
#         # self.header_top_widget.setAttribute(Qt.WA_TransparentForMouseEvents)
#         layout:QHBoxLayout  = self.header_top_widget.findChild(QHBoxLayout, "header_top_layout")
        
#         # Store header buttons for dynamic resizing
#         self.header_buttons = []
        
#         if Config.ENABLE_CROP_FRAME:
#             self.btn_crop_frame = CustomIcon(icon= main_controller.get_theme_attribute("Image", "icon_crop_off"), icon_clicked= self.btn_crop_frame_clicked,style=Style.StyleSheet.button_style19,icon_size = 28)
#             # insert self.btn_crop_frame to first index
#             layout.insertWidget(0, self.btn_crop_frame)
#             self.header_buttons.append(self.btn_crop_frame)
#             # layout.addWidget(self.btn_crop_frame)
#         # #########################################
#         self.is_ptz_arrow_configuration, self.is_3d_locate_configuration = self.check_ptz_configuration()
#         # self.is_ptz_arrow_configuration, self.is_3d_locate_configuration = True,True
#         if self.camera_model.data.ptzCap is not None and len(self.camera_model.data.ptzCap) > 0:
#             self.btn_ptz = CustomIcon(icon= main_controller.get_theme_attribute("Image", "icon_ptz_off"), icon_clicked= self.btn_ptz_clicked,style=Style.StyleSheet.button_style19,icon_size = 36)
#             layout.insertWidget(0, self.btn_ptz)
#             self.header_buttons.append(self.btn_ptz)
#         if self.is_3d_locate_configuration:
#             self.btn_3_d_locate = CustomIcon(icon= main_controller.get_theme_attribute("Image", "icon_drag_zoom_off"), icon_clicked= self.btn_3_d_locate_clicked,style=Style.StyleSheet.button_style19,icon_size = 28)
#             layout.insertWidget(0, self.btn_3_d_locate)
#             self.header_buttons.append(self.btn_3_d_locate)
#         if self.is_ptz_arrow_configuration:
#             self.btn_ptz_arrow = CustomIcon(icon= main_controller.get_theme_attribute("Image", "icon_ptz_arrow_off"), icon_clicked= self.btn_ptz_arrow_clicked,style=Style.StyleSheet.button_style19,icon_size = 28)
#             layout.insertWidget(0, self.btn_ptz_arrow)
#             self.header_buttons.append(self.btn_ptz_arrow)
        
#         # Add base buttons to the list
#         self.header_buttons.extend([self.btn_full_screen, self.btn_close])

#     def update_header_icon_sizes(self):
#         """Update header icon sizes dynamically based on widget size with improved linear scaling"""
#         if not hasattr(self, 'header_buttons') or not self.header_buttons:
#             return
            
#         # Enhanced dynamic calculation with multiple scaling factors
#         widget_width = self.width()
#         widget_height = self.height()
#         widget_area = widget_width * widget_height
        
#         # Base scaling factors with reduced growth for larger sizes
#         width_factor = max(0.8, min(1.8, widget_width / 250))  # Reduced max from 2.5 to 1.8, increased baseline
#         height_factor = max(0.8, min(1.8, widget_height / 200))  # Reduced max from 2.5 to 1.8, increased baseline
#         area_factor = max(0.6, min(1.5, (widget_area ** 0.3) / 50))  # Reduced exponent and max for slower growth
        
#         # Progressive scaling - slower growth for larger widgets
#         if widget_width > 400 or widget_height > 300:
#             # Apply logarithmic scaling for large widgets
#             import math
#             width_log_factor = 1.0 + 0.3 * math.log(max(1, widget_width / 400))
#             height_log_factor = 1.0 + 0.3 * math.log(max(1, widget_height / 300))
#             width_factor = min(width_factor, width_log_factor)
#             height_factor = min(height_factor, height_log_factor)
        
#         # Aspect ratio consideration - prevent extreme sizing on very wide/tall widgets
#         aspect_ratio = widget_width / max(widget_height, 1)
#         aspect_adjustment = 1.0
#         if aspect_ratio > 3.0:  # Very wide widget
#             aspect_adjustment = 0.8  # Reduced from 0.85
#         elif aspect_ratio < 0.33:  # Very tall widget
#             aspect_adjustment = 0.85  # Reduced from 0.9
        
#         # Combine factors for optimal scaling with preference for smaller sizes
#         combined_factor = (width_factor * 0.3 + height_factor * 0.3 + area_factor * 0.4) * aspect_adjustment
        
#         # Calculate icon size with tighter bounds and slower progression
#         base_icon_size = max(14, min(32, int(20 * combined_factor)))  # Reduced max from 52 to 32, base from 24 to 20
#         button_size = base_icon_size + max(2, min(6, int(combined_factor * 1.5)))  # Limited button padding growth
        
#         # Update all header buttons with smooth transitions
#         for button in self.header_buttons:
#             if button and hasattr(button, 'button'):
#                 button.button.setFixedSize(button_size, button_size)
#                 button.button.setIconSize(QSize(base_icon_size, base_icon_size))

#     # create header widget, title, record, ai state
#     def create_footer_widget_for_camera(self):
#         self.footer_widget = QWidget(self)
#         self.footer_widget.setObjectName("header_widget")
#         self.footer_widget.setStyleSheet(f'''
#                     QWidget#objHeader {{
#                         background-color: {main_controller.get_theme_attribute("Color", "camera_widget_background")};
#                         border: 2px solid {main_controller.get_theme_attribute("Color", "camera_widget_background")}; 
#                         border-top-left-radius: 4px; 
#                         border-top-right-radius: 4px; 
#                         border-bottom: none;
#                     }}
#                     QWidget#objHeader QLabel {{
#                         border: none;
#                     }}
#                 ''')
#         self.footer_widget.setMaximumHeight(24)

#         self.header_layout = QHBoxLayout(self.footer_widget)
#         self.header_layout.setContentsMargins(8, 0, 8, 0)

#         self.lb_camera_name = QLabel()
#         self.lb_camera_name.setContentsMargins(0, 0, 0, 0)
#         self.lb_camera_state = QLabel()
#         self.lb_camera_state.setContentsMargins(0, 0, 0, 0)
#         self.lb_camera_state.setPixmap(QtGui.QPixmap(Style.PrimaryImage.disconnected_norec_unpin))
#         self.lb_camera_state.setFixedSize(24, 24)
#         # add lb_camera_state red shape to indicate camera state
#         self.lb_camera_name.setText(self.camera_name)
#         self.lb_camera_name.setStyleSheet(
#             f"border: 0; font-weight: bold; color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')}; background-color: transparent; padding: 2px; border-radius: 4px")
#         self.lb_camera_name.adjustSize()

#         self.risk_identification_icon = QLabel()
#         self.risk_identification_icon.hide()
#         self.risk_identification_icon.setPixmap(QtGui.QPixmap(main_controller.get_theme_attribute("Image", "ic_risk_identification")).scaledToHeight(16))

#         self.recognition_security_icon = QLabel()
#         self.recognition_security_icon.hide()
#         self.recognition_security_icon.setPixmap(QtGui.QPixmap(main_controller.get_theme_attribute("Image", "ic_recognition_security")).scaledToHeight(16))

#         state_ai_layout = QHBoxLayout()
#         state_ai_layout.setAlignment(Qt.AlignmentFlag.AlignRight)
#         state_ai_layout.addWidget(self.recognition_security_icon)
#         state_ai_layout.addWidget(self.risk_identification_icon)

#         self.header_layout.addWidget(self.lb_camera_state)
#         self.header_layout.addWidget(self.lb_camera_name)
#         self.header_layout.addLayout(state_ai_layout)

#         self.update_state()

#     def check_ptz_configuration(self):
#         ptz_arrow_supported = False
#         btn_3d_locate_supported = False
#         if self.camera_model.data.ptzCap is not None and len(self.camera_model.data.ptzCap) > 0:
#             if PtzCapType.continuous_pan_tilt in self.camera_model.data.ptzCap and PtzCapType.pan_tilt_limits in self.camera_model.data.ptzCap:
#                 ptz_arrow_supported = True
#             if PtzCapType.relative_pan_tilt in self.camera_model.data.ptzCap and PtzCapType.default_ptz_speed in self.camera_model.data.ptzCap and PtzCapType.pan_tilt_limits in self.camera_model.data.ptzCap: 
#                 btn_3d_locate_supported = True
                                
#         return (ptz_arrow_supported,btn_3d_locate_supported)

#     def on_btn_try_reconnect_clicked(self):
#         """Handle retry button click - immediately attempt reconnection"""
#         # Hide the status message and retry symbol right away
#         self.hide_status_message()
        
#         # Show connecting status immediately
#         self.show_status_message("Connecting...", "yellow")
        
#         # Reset and restart video capture
#         self.reconnect_camera()

#     def reconnect_camera(self):
#         """Reset and restart the video capture connection"""
#         if self.player:
#             # Reset the video capture state
#             self.player.camera_state = CameraState.connecting
#             self.player.connect_status = True
            
#             # Force an immediate reset and restart
#             self.player.start_thread()

#     def share_frame_signal(self, data):
#         if self.flag_init_ui:
#             return
#         if self.camera_status:
#             if self.frame_delay == 0:
#                 self.previous_camera_status = True
#                 self.update_frame_data(data)
#             else:
#                 self.frame_delay -= 1
#         elif not self.previous_camera_status:
#             pass
#         else:
#             if self.preview_camera:
#                 self.frame_delay = 3
#                 self.previous_camera_status = False
#                 self.update_camera_background_state(CameraState.connecting)

#     def next_chunk_signal(self, data):
#         if not self.is_virtual_window:
#             if self.record_data is not None and self.player is not None:
#                 try:
#                     data = self.record_data.get_next_record(self.end_duration, self.camera_id)
#                     if data is not None:
#                         logger.debug(f'next_chunk_signal = {data.data.start_duration}')
#                         video = data
#                         # source = widget.player.get_current_url()
#                         self.video_url = video.data.url
#                         self.seek_time = None
#                         self.start_duration = video.data.start_duration
#                         self.end_duration = video.data.end_duration
#                         old_duration = self.player.current_duration
#                         self.player.stop_video()
#                         self.player.load_media(video.data.url, start_duration=video.data.start_duration,end_duration=video.data.end_duration)
#                         self.player.play_video()
#                         # self.timelinecontroller.scrollbarPositionChanged.emit()
#                         self.timelinecontroller.shift_for_duration(self.start_duration - old_duration)
#                         self.timelinecontroller.scrollbarPositionChanged.emit()
#                         # self.timelinecontroller.isLive = False
#                         # self.timelinecontroller.positionBubble = True
#                     else:
#                         pass
#                         # vào chế độ live
#                         logger.debug(f'next_chunk_signal1')
#                         self.timelinecontroller.isLive = True
#                         self.timelinecontroller.positionBubble = False
#                         self.timelinecontroller.isNextChunk = False
#                         self.timelinecontroller.isNextFrame = False
#                         self.timelinecontroller.nextFrame = SpeedStatus.SpeedEnum.Up1X

#                 except ValueError as e:
#                     logger.error(f"Error parsing position timestamp: {e}")
#                 except Exception as e:
#                     logger.error(f"Error handling position click: {e}")

#     def previous_chunk_signal(self, data):
#         if not self.is_virtual_window:
#             if self.record_data is not None and self.player is not None:
#                 if self.player.stream_type == CommonEnum.StreamType.VIDEO_STREAM:
#                     try:
#                         data = self.record_data.get_previous_record(self.start_duration, self.camera_id)
#                         if data is not None:
#                             video = data
#                             # source = widget.player.get_current_url()
#                             self.video_url = video.data.url
#                             self.seek_time = None
#                             self.start_duration = video.data.start_duration
#                             self.end_duration = video.data.end_duration
#                             self.player.stop_video()
#                             self.player.load_media(video.data.url, start_duration=video.data.start_duration,end_duration=video.data.end_duration)
#                             self.player.play_video()
#                             # self.timelinecontroller.isLive = False
#                             # self.timelinecontroller.positionBubble = True
#                         # else:
#                         #     pass
#                         #     # vào chế độ live
#                         #     logger.debug(f'next_chunk_signal1')
#                         #     self.timelinecontroller.isLive = True
#                         #     self.timelinecontroller.positionBubble = False
#                         #     self.timelinecontroller.isNextChunk = False
#                         #     self.timelinecontroller.isNextFrame = False
#                     except ValueError as e:
#                         logger.error(f"Error parsing position timestamp: {e}")
#                     except Exception as e:
#                         logger.error(f"Error handling position click: {e}")

#     def update_frame_data(self, data):
#         if self.preview_camera is None:
#             return
#         grab, mat_frame_default, pixmap_resized = data
#         if self.use_post_process_image and self.callback_post_process_image is not None:
#             self.callback_post_process_image(mat_frame_default)
#         elif grab and pixmap_resized is not None and self.isVisible():
#             self.camera_state_signal(CameraState.started)
#             pixmap_resized: QPixmap
#             h, w = pixmap_resized.height(), pixmap_resized.width()
#             self.set_widget_size([self.root_width, self.root_height, w, h])
#             try:
#                 self.preview_camera.setPixmap(pixmap_resized)
#             except RuntimeError:
#                 if self.ENABLE_DEBUG:
#                     logger.debug("RuntimeError")

#     def camera_state_signal(self, camera_state):
#         # self.player.camera_state = camera_state
#         # NguyenNH: cập nhật camera state
#         if self.previous_camera_state != camera_state:
#             # logger.debug(f"camera_state_signal: {camera_state}")
#             self.camera_state_changed.emit(camera_state)
#         self.previous_camera_state = camera_state

#     def on_camera_state_changed(self, state):
#         if state == self.previous_camera_state:
#             return
#         self.previous_camera_state = state
#         self.camera_state = state
        
#         # Update the camera state indicator using text instead of pixmaps
#         if state == CameraState.connecting:
#             self.show_status_message(self.tr("Connecting..."), "yellow")
#             self.blur_animation.stop()
#             self.blur_effect.setBlurRadius(15)
#         elif state == CameraState.started:
#             self.hide_status_message()
#             self.blur_animation.stop()
#             self.blur_animation.setStartValue(15)
#             self.blur_animation.setEndValue(0)
#             self.blur_animation.start()
#         elif state == CameraState.stopped:
#             self.show_status_message(self.tr("Disconnect"), "red")
#             self.blur_animation.stop()
#             self.blur_effect.setBlurRadius(15)
#         elif state == CameraState.paused:
#             self.show_status_message(self.tr("Paused"), "blue")
#             self.blur_animation.stop()
#             self.blur_effect.setBlurRadius(15)

#     def show_status_message(self, message, color="white"):
#         """Display a formatted status message over the camera view"""
#         self.hide_status_message()
        
#         # Create status label if it doesn't exist
#         if not hasattr(self, 'status_label'):
#             self.status_label = QLabel(self)
#             self.status_label.setAlignment(Qt.AlignCenter)
        
#         # Create retry label if needed
#         if not hasattr(self, 'retry_label'):
#             self.retry_label = QLabel(self)
#             self.retry_label.setAlignment(Qt.AlignCenter)
#             self.retry_label.setCursor(Qt.PointingHandCursor)  # Show hand cursor on hover
#             # Use direct method reference instead of lambda for immediate execution
#             self.retry_label.mousePressEvent = self.retry_clicked
        
#         # Calculate dynamic font size based on widget size
#         widget_area = self.width() * self.height()
#         base_font_size = max(10, min(24, int((widget_area ** 0.5) / 15)))
        
#         # Set up the label style
#         color_map = {
#             "red": "#FF5555",
#             "yellow": "#FFBB55", 
#             "blue": "#5555FF",
#             "white": "#FFFFFF"
#         }
#         text_color = color_map.get(color, "#FFFFFF")
        
#         self.status_label.setStyleSheet(f"""
#             color: {text_color};
#             font-weight: bold;
#             font-size: {base_font_size}px;
#         """)
        
#         self.retry_label.setStyleSheet(f"""
#             color: {text_color};
#             font-weight: bold;
#             font-size: {base_font_size}px;
#         """)
        
#         self.status_label.setText(message)
#         self.status_label.adjustSize()
        
#         # Position the label in the center of the widget
#         label_x = (self.width() - self.status_label.width()) // 2
#         label_y = (self.height() - self.status_label.height()) // 2
#         self.status_label.move(label_x, label_y)
        
#         # Show retry symbol if disconnected
#         if message == self.tr("Disconnect"):
#             self.retry_label.setText(" ↻")  # Added space before symbol for better spacing
#             self.retry_label.adjustSize()
#             retry_x = label_x + self.status_label.width() + 5  # Reduced spacing
#             retry_y = label_y  # Same height as status label
#             self.retry_label.move(retry_x, retry_y)
#             self.retry_label.show()
#         else:
#             self.retry_label.hide()
        
#         self.status_label.show()

#     def hide_status_message(self):
#         """Hide the status message and retry text if they exist"""
#         if hasattr(self, 'status_label') and self.status_label:
#             self.status_label.hide()
#         if hasattr(self, 'retry_label') and self.retry_label:
#             self.retry_label.hide()

#     def update_camera_background_state(self, camera_state):
#         """Replace background state changes with status messages"""
#         if camera_state == CameraState.started:
#             self.hide_status_message()
#             self.preview_camera.setVisible(True)
#             if self.footer_widget:
#                 self.footer_widget.setVisible(True)
#         elif camera_state == CameraState.connecting:
#             self.show_status_message(self.tr("Connecting..."), "yellow")
            
#         elif camera_state == CameraState.stopped:
#             self.show_status_message(self.tr("Disconnect"), "red")

#         elif camera_state == CameraState.unauthorized:
#             self.show_status_message(self.tr("Unauthorized Access"), "red")
                
#         elif camera_state == CameraState.paused:
#             self.show_status_message(self.tr("Paused"), "blue")
#     def positionClicked(self, position):
#         """Handle timeline position click with time sync"""
#         position = int(position)
#         self.input_queue.put(position)

#     def isPlayChanged(self):
#         logger.debug(f'isPlayChanged = {self.timelinecontroller.isPlay}')
#         if self.timelinecontroller.isPlay:
#             if self.player.stream_type == CommonEnum.StreamType.VIDEO_STREAM:
#                 self.player.start_video()
#             else:
#                 self.player.play_live()
#         else:
#             if self.player.stream_type == CommonEnum.StreamType.VIDEO_STREAM:
#                 self.player.pause_video()
#             else:
#                 self.player.pause_live()

    
#     def isLiveChanged(self):
        
#         if self.timelinecontroller.isLive:
#             self.timelinecontroller.positionBubble = False
#             self.switch_player(stream_type = CommonEnum.StreamType.SUB_STREAM)
#         else:
#             pass
#             # logger.debug(f'isLiveChanged = {self.timelinecontroller.isLive}')

#     def isNextChunkClicked(self):
#         self.next_chunk_signal('')

#     def isPreviousChunkClicked(self):
#         self.previous_chunk_signal('') 

#     def speedStatusChanged(self,data):
#         logger.debug(f'speedStatusChanged = {data}')
#         speed = 0
#         if data == 1:
#             if self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Pause:
#                 speed = SpeedStatus.SpeedEnum.Up1X
#             elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up1X:
#                 speed = SpeedStatus.SpeedEnum.Up2X
#             elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up2X:
#                 speed = SpeedStatus.SpeedEnum.Up4X
#             elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up4X:
#                 speed = SpeedStatus.SpeedEnum.Up8X
#             elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up8X:
#                 speed = SpeedStatus.SpeedEnum.Up8X
#         elif data == -1:
#             if self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up1X:
#                 # speed = SpeedStatus.SpeedEnum.Pause
#                 # self.timelinecontroller.isPlay = False
#                 return
#             elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up2X:
#                 speed = SpeedStatus.SpeedEnum.Up1X
#             elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up4X:
#                 speed = SpeedStatus.SpeedEnum.Up2X
#             elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up8X:
#                 speed = SpeedStatus.SpeedEnum.Up4X
#             else:
#                 speed = SpeedStatus.SpeedEnum.Pause
#                 self.timelinecontroller.isPlay = False
#                 return
#         self.timelinecontroller.isPlay = True
#         if self.player.stream_type == CommonEnum.StreamType.VIDEO_STREAM and self.player.is_playing():
#             try:
#                 self.player.set_speed(speed)
#                 self.timelinecontroller.nextFrame = speed
#             except ValueError as e:
#                 logger.error(f"Error parsing position timestamp: {e}")
#             except Exception as e:
#                 logger.error(f"Error handling position click: {e}")

#     def nextFrameChanged(self):

#         speed = 0
#         if self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Pause:
#             speed = SpeedStatus.SpeedEnum.Up1X
#         elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up1X:
#             speed = SpeedStatus.SpeedEnum.Up1X
#         elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up2X:
#             speed = SpeedStatus.SpeedEnum.Up2X
#         elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up4X:
#             speed = SpeedStatus.SpeedEnum.Up4X
#         elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up8X:
#             speed = SpeedStatus.SpeedEnum.Up8X

#         if self.player.stream_type == StreamCameraType.video_stream and self.player.is_playing():
#             try:
#                 self.player.set_speed(speed)
#                 # self.timelinecontroller.nextFrame = speed
#             except ValueError as e:
#                 logger.error(f"Error parsing position timestamp: {e}")
#             except Exception as e:
#                 logger.error(f"Error handling position click: {e}")

#     def hoverPositionChanged(self, position):
#         pass

#     def show_menu(self, position):
#         logger.debug(f'showMenu = {position, type(position)}')
#         def handle_clear_selection():
#             logger.debug(f'handle_clear_selection')
#             self.timelinecontroller.clearSelectionChanged.emit()
#         def handle_zoom_to_selection():
#             logger.debug(f'handle_zoom_to_selection')
#             self.timelinecontroller.zoomToSelectionChanged.emit()
#         def handle_export_video():
#             logger.debug(f'handle_export_video')
#         menu = QMenu()
#         # menu.setStyleSheet(Style.StyleSheet.context_menu)
#         menu.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
#         clear_selection = menu.addAction(self.tr("Clear Selection"))
#         clear_selection.triggered.connect(handle_clear_selection)
#         zoom_to_selection = menu.addAction(self.tr('Zoom to Selection'))
#         zoom_to_selection.triggered.connect(handle_zoom_to_selection)
#         export_video = menu.addAction(self.tr('Export video'))
#         export_video.setDisabled(True)
#         export_video.triggered.connect(handle_export_video)
#         current_mouse = QCursor.pos()
#         logger.debug(f'current_mouse = {current_mouse}')
#         position_of_mouse = self.mapFromGlobal(current_mouse)
#         logger.debug(f'position_of_mouse = {position_of_mouse}')
#         menu.exec_(self.mapToGlobal(position_of_mouse))
#         self.timelinecontroller.closeMenu()

#     def calculate_ratio(self, image_width, image_height, parent_width, parent_height):
#         scale_factor = 0.16
#         # Calculate width and height ratios
#         width_ratio = parent_width / image_width
#         height_ratio = parent_height / image_height

#         # Use the minimum ratio to maintain the aspect ratio
#         min_ratio = min(width_ratio, height_ratio)

#         # Apply the scaling factor
#         min_ratio *= scale_factor

#         # Calculate new dimensions for the image
#         new_width = int(image_width * min_ratio)
#         new_height = int(image_height * min_ratio)

#         return new_width, new_height

#     def set_widget_size(self, widget_size):
#         self.widget_size = widget_size
#         # NguyenNH: cập nhật widget size
#         if self.previous_widget_size != self.widget_size:
#             self.widget_size_changed.emit(self.widget_size)
#         self.previous_widget_size = self.widget_size

#     def on_widget_size_changed(self, widget_size):
#         root_width, root_height, preview_width, preview_height = widget_size
#         preview_width = root_width
#         preview_height = root_height
#         self.resize(root_width, root_height)
        
#         # Update preview camera size
#         if self.preview_camera is not None:
#             self.preview_camera.setGeometry(0, 0, preview_width, preview_height)
        
#         # Update footer widget position
#         if self.footer_widget is not None:
#             self.footer_widget.setGeometry(0, preview_height - self.HEIGHT_HEADER//2, preview_width, self.HEIGHT_HEADER//2)
            
#             # Update footer font and icon sizes dynamically
#             self.update_footer_sizes()
        
#         # Update header widget and check fullscreen button visibility
#         if self.header_top_widget is not None:
#             self.header_top_widget.setGeometry(0, 0, preview_width, self.HEIGHT_HEADER)
            
#             # Update header icon sizes dynamically
#             self.update_header_icon_sizes()
            
#             # Check grid layout and update fullscreen button visibility
#             current_grid = self.tab_model.data.currentGrid if self.tab_model else None
#             is_1x1_grid = current_grid and current_grid['row'] == 1 and current_grid['column'] == 1
            
#             # Hide fullscreen and close buttons if view only mode is enabled
#             if hasattr(self, 'is_show_header') and not self.is_show_header:
#                 self.btn_full_screen.setVisible(False)
#                 self.btn_close.setVisible(False)
#             else:
#                 self.btn_full_screen.setVisible(not is_1x1_grid)

#         if self.footer_widget is not None:
#             logger.debug(f'is_show_footer = {self.is_show_footer}')
#             if hasattr(self, 'is_show_footer') and not self.is_show_footer:
#                 self.footer_widget.setVisible(False)
#             else:
#                 self.footer_widget.setVisible(True)
#                 # Update footer sizes when showing
#                 self.update_footer_sizes()
        
#         # Update rectangle widget dimensions
#         if self.rectangle_widget is not None:
#             self.rectangle_widget.real_width = self.preview_width
#             self.rectangle_widget.real_height = self.preview_height - self.HEIGHT_HEADER - 24
#             self.rectangle_widget.setGeometry(0, self.HEIGHT_HEADER, self.rectangle_widget.real_width, self.rectangle_widget.real_height)
#         if self.warning_alert_widget is not None:
#             self.warning_alert_widget.resize(preview_width, preview_height)
#             self.warning_alert_widget.setGeometry(0, 0, self.root_width, self.root_height)

#     def show_grid_item_index(self,number):
#         self.camera_index_widget.setVisible(True)
#         # self.item_index_widget.setGeometry(self.frame_width/2 - 25, 0, 50, 50)
#         self.camera_index_widget.setText(str(number))
#         self.camera_index_widget.setStyleSheet(Style.StyleSheet.label_style1)
#         self.camera_index_widget.setGeometry(self.root_width/2 -50, self.root_height/2 -50, 100, 100)
#         timer = QTimer(self)
#         timer.setInterval(5000)
#         timer.setSingleShot(True)
#         timer.timeout.connect(self.hide_grid_item_index)
#         timer.start()
#         key_board_manager.timer_list.append(timer)

#     def hide_grid_item_index(self):
#         self.camera_index_widget.setText('')
#         self.camera_index_widget.setVisible(False)
#         self.camera_index_widget.setStyleSheet(Style.StyleSheet.label_style1)
#         # self.camera_frame.setStyleSheet(Style.StyleSheet.camera_frame_focused)

#     def change_grid_item_index_color(self):
#         self.camera_index_widget.setStyleSheet(Style.StyleSheet.label_style3)


#     def setSizeWidget(self, width, height):
#         self.root_width = width
#         self.root_height = height

#     def update_resize(self, width, height, is_fullscreen = False):
#         logger.debug(f'update_resize = {width,height} - is_fullscreen: {is_fullscreen}')
#         # self.preview_camera.clear() để mắt người không thấy được hiệu ứng resize frame
#         # self.preview_camera.clear()
#         self.root_width = width
#         self.root_height = height
#         self.preview_width = width
#         self.preview_height = height
#         # update widget size immediately when change grid size
#         self.on_widget_size_changed([self.root_width, self.root_height, self.preview_width, self.preview_height])
#         # update camera state background and resize image background
#         self.preview_camera.update_resize(width, height)
#         if self.player is not None:
#             self.update_camera_background_state(self.player.camera_state)
#             self.player.update_resize(width, height, self.uuid, is_fullscreen = is_fullscreen)
#         # self.border_widget.setGeometry(0, 0, self.root_width, self.root_height)
#         # if self.video_play_back_widget is not None:
#         #     self.widget.resize(width, height)

#     def right_mouse_event_triggered(self,text = None):
#         # xu ly kich ban ve border cho camera
#         current_tab = main_controller.current_tab
#         if grid_item_selected.is_tab_index(current_tab) and grid_item_selected.data['tab_index'] is not None:
#             if grid_item_selected.data['camera_id'] != self.camera_id:
#                 self.grid_item_clicked(main_controller.current_tab)
#         else:
#             self.grid_item_clicked(main_controller.current_tab)

#         # xu ly action khi chuot phai
#         if text == Style.Actions.standard:
#             pass
#         elif text == Style.Actions.press_patrol:
#             pass
#         elif text == Style.Actions.full_screen:
#             logger.debug(f'aaaaaaaaaaaaaaaaaaa')
#             self.btn_full_screen_clicked()
#         elif text == Style.Actions.enable_audio:
#             self.enable_audio = True
#         elif text == Style.Actions.disable_audio:
#             self.enable_audio = False
#         elif text == Style.Actions.image_adj:
#             pass

#         self.right_mouse_event.emit([text,self.camera_model.data])

#     def recordSettingChanged(self):
#         if self.camera_model.recordSetting:
#             self.recording_icon_label.show()
#         else:
#             self.recording_icon_label.hide()

#     def update_change_model(self, data):
#         key, value, model = data
#         logger.debug(f'update_change_model ={key,value,model}')
#         if key == 'name':
#             logger.debug(f'value = {value}')
#             self.camera_name = value
#             self.lb_camera_name.setText(f'{value}')
#         elif key == 'aiFlowIds':
#             logger.info(f'update_change_model = {key}')
#             for id in value:
#                 aiflow:AiFlowModel = aiflow_model_manager.get_aiflow_model(id=id)
#                 if aiflow is not None and aiflow.data.is_apply():
#                     self.recognition_security_icon.show() if (aiflow.data.is_recognition_and_protection() and aiflow.data.is_apply()) else self.recognition_security_icon.hide()
#                     self.risk_identification_icon.show() if (aiflow.data.is_risk_identification() and aiflow.data.is_apply()) else self.risk_identification_icon.hide()
#         elif key == 'aiFlowDTOList':
#             logger.info(f'update_change_model = {key}')
#             if value is not None:
#                     for ai_flow_dict in value:
#                         print(f'ai_flow = {ai_flow_dict} - {type(ai_flow_dict)}')
#                         from src.common.model.aiflows_model import AiFlow
#                         # parser ai_flow_dict
#                         aiflow: AiFlow = AiFlow.from_dict(ai_flow_dict)
#                         self.recognition_security_icon.show() if (aiflow.is_recognition_and_protection() and aiflow.is_apply()) else self.recognition_security_icon.hide()
#                         self.risk_identification_icon.show() if (aiflow.is_risk_identification() and aiflow.is_apply()) else self.risk_identification_icon.hide()
#         elif key == 'warning':
#             self.add_alert_to_queue(value)
#         elif key == 'coordinateLat' or key == 'coordinateLat' or key == 'recordingState':
#             icon = CameraModel.get_icon(self.camera_model.state_merged)
#             self.lb_camera_state.setPixmap(QtGui.QPixmap(icon))
#         elif key == 'state':
#             icon = CameraModel.get_icon(self.camera_model.state_merged)
#             self.lb_camera_state.setPixmap(QtGui.QPixmap(icon))
#             logger.info(f'key, value, model = {key, value, model}')
#             if value == 'DISCONNECTED':
#                 # Stop current video capture
#                 if self.player is not None:
#                     self.player.move_to_trash()
#                 # Update UI to show disconnected state
#                 self.update_camera_background_state(CameraState.stopped)
#                 self.show_status_message(self.tr("Disconnected"), "red")
#             elif value == 'UNAUTHORIZED':
#                 # Stop current video capture
#                 if self.player is not None:
#                     self.player.move_to_trash()
#                 # Update UI to show unauthorized state
#                 self.update_camera_background_state(CameraState.unauthorized)
#                 self.show_status_message(self.tr("Unauthorized Access"), "red")
#             elif value == 'CONNECTED':
#                 # Clear any existing status messages
#                 self.hide_status_message()
#                 # Reconnect the camera
#                 self.reconnect_camera()
#             else:
#                 logger.warning(f'Unknown camera state: {value}')

#     def add_alert_to_queue(self, event_ai):
#         self.alert_queue.append(event_ai)
#         if not self.alert_timer.isActive():
#             self.alert_timer.start(100)  # Adjust interval as needed

#     def process_alerts(self):
#         if self.alert_queue:
#             event_ai = self.alert_queue.pop(0)
#             self.trigger_warning_alert(event_ai, True)
#             # self.alert_duration_timer.start(7000)  # Show alert for 7 seconds
#         else:
#             self.alert_timer.stop()

#     def trigger_warning_alert(self, event_ai, is_show):
#         if is_show:
#             if self.warning_alert_widget:
#                 self.warning_alert_widget.show_warning_alert(event_ai)
#             if self.stack_item is not None:
#                 self.stack_item.start_border_animation()
#         else:
#             if self.stack_item is not None:
#                 self.stack_item.stop_border_animation()
#             if self.warning_alert_widget:
#                 self.warning_alert_widget.close_alert_widget()

#     def handle_alert_timeout(self):
#         # This will ensure to stop the border animation and close the alert widget
#         self.trigger_warning_alert(None, False)
#         if self.alert_queue:
#             self.alert_timer.start(100)

#     def prepare_show_next_tracking_animation(self):
#         # if not self.next_tracking_timer.isActive():
#         #     self.next_tracking_timer.start(100)  # Adjust interval as needed
#         self.process_next_tracking()

#     def process_next_tracking(self):
#         self.show_next_camera_animation(True)
#         # self.next_tracking_timer.start(7000)  # Start the timer for 7 seconds

#     def show_next_camera_animation(self, is_show):
#         if self.stack_item is not None:
#             if is_show:
#                 self.stack_item.start_next_border_animation()
#             else:
#                 self.stack_item.stop_border_animation()

#     def handle_next_tracking_timeout(self):
#         self.show_next_camera_animation(False)

#     def stop_all_timer(self):
#         self.alert_timer.stop()
#         self.next_tracking_timer.stop()
#         self.alert_duration_timer.stop()
#         self.handle_next_tracking_timeout()

#     def callback_key(self,direction = None,x = None, y = None,zoom = 0,speed = None):
#         # logger.debug(f'callback_key speed = {speed,self.camera_model.data}')
#         if self.camera_model.data.type == "AVIGILON":
#             data = {
#                 "cameraId": self.camera_model.get_property("id", None),
#                 "endPoint": "/camera/commands/pan-tilt-zoom",
#                 "requestData": {
#                     "continuous": {
#                         "panAmount": x,
#                         "tiltAmount": -y,
#                         "zoomAmount": 0 if zoom == None else zoom,
#                         "action": "START"
#                         }
#                 }
#             }
#             self.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
#         else:
#             if self.camera_model.data.ptzCap is not None and len(self.camera_model.data.ptzCap) > 0:
#                 self.controller.ptz_continuous_move(parent=self,cameraId=self.camera_model.get_property("id", None), x = x, y = y,zoom = zoom)

#     def callback_exit_key(self,direction = None):
#         logger.debug(f'callback_exit_key')
#         if self.camera_model.data.type == "AVIGILON":
#             data = {
#                 "cameraId": self.camera_model.get_property("id", None),
#                 "endPoint": "/camera/commands/pan-tilt-zoom",
#                 "requestData": {
#                     "continuous": {
#                         "panAmount": 0,
#                         "tiltAmount": 0,
#                         "zoomAmount": 0,
#                         "action": "STOP"
#                         }
#                 }
#             }
#             self.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
#         else:
#             if self.camera_model.data.ptzCap is not None and len(self.camera_model.data.ptzCap) > 0:
#                 self.controller.ptz_stop(cameraId=self.camera_model.get_property("id", None))
#             self.previous_direction = None

#     def callback_mousePressEvent(self, start_point: QPoint= None,end_point: QPoint= None, type = 'crop_frame'):
#         if type == 'crop_frame' or type == 'ptz_arrow':
#             if self.is_crop_frame:
#                 self.start_pos = start_point
#             if self.is_ptz_arrow:
#                 # ghi nhận tọa độ và góc ở điểm click đầu tiên đưa vào queue
#                 self.match_start_point = start_point
#                 self.match_end_point = end_point
#                 angle = math.atan2(self.match_end_point.y() - self.match_start_point.y(), self.match_end_point.x() - self.match_start_point.x())
#                 self.angle_deg = math.degrees(angle)
#                 #############################################
#                 if not self.allow_ptz_arrow_queue:
#                     self.allow_ptz_arrow_queue = True
#                     self.gesture_status = True
#                     # trong trường hợp người dùng click xong nhả liên tục cần sử dụng gesture_status và allow_ptz_arrow_queue để đảm bảo đồng bộ tiến trình phải chạy tuần tự start -> continuous -> stop  
#                     coordinate = CalibData.get_coordinate_ptz_arrow(start_point = start_point, end_point = end_point,width = self.rectangle_widget.real_width, height = self.rectangle_widget.real_height,calib_data=self.calib_data)
#                     self.coordinate_queue.put(coordinate)
#                     if not self.process_queue_thread.isRunning():
#                         self.process_queue_thread.start()

#     def match_points(self,start_point: QPoint= None,end_point: QPoint= None, length = 1):
#         # Tính toán chênh lệch độ vị trí point hiện tại so với point được ghi nhận trước đó.
#         if self.match_start_point is None or self.match_end_point is None:
#             return False
#         sub_x =  abs(end_point.x() - self.match_end_point.x())
#         sub_y =  abs(end_point.y() - self.match_end_point.y())
#         if sub_x >=length or sub_y>=length:
#             self.match_end_point = end_point
#             return True
#         return False

#     def callback_mouseMoveEvent(self, start_point: QPoint= None,end_point: QPoint= None, type = 'crop_frame'):
#         current_time = time.time()
#         sub = current_time - self.start_time
#         if type == 'ptz_arrow' and self.match_points(start_point,end_point,3):
#             if self.allow_ptz_arrow_queue:
#                 self.start_time = time.time() 
#                 coordinate = CalibData.get_coordinate_ptz_arrow(start_point = start_point, end_point = end_point,width = self.rectangle_widget.real_width, height = self.rectangle_widget.real_height,calib_data=self.calib_data)
#                 self.coordinate_queue.put(coordinate)

#     def process_joystick_queue(self):
#         while True:
#             current_time = time.time()
#             try:
#                 msg = self.current_joystick_msg
#                 if msg != self.previous_joystick_msg:
#                     # logger.debug(f'run_joystick_thread = {self.previous_joystick_msg}')
#                     self.previous_joystick_msg = msg.copy()
#                     x = 0
#                     y = 0
#                     speed_zoom = 0
#                     if msg['type'] == 'button_zoom':
#                         speed_zoom = msg['speed']
#                         if speed_zoom == 0:
#                             self.controller.ptz_stop(cameraId=self.camera_model.get_property("id", None))
#                             logger.debug(f'stop run_joystick_thread')
#                             time.sleep(0.5)
#                             return
#                         else:
#                             if (current_time - self.previous_time) > 0.5:
#                                 self.controller.ptz_continuous_move(parent=self,cameraId=self.camera_model.get_property("id", None),x = x,y = y,zoom=speed_zoom)
#                                 self.previous_time = time.time()
#                             # sleep(0.02)
#                     elif msg['type'] == 'button_focus':
#                         pass
#                         # speed_focus = msg['speed']
#                         # # logger.debug(f'msg = {msg}')
#                         # if speed_focus == 0:
#                         #     self.stop_focus(profileToken=msg['profileToken'])
#                         #     return
#                         # else:
#                         #     if (current_time - self.previous_time) > 0.1:
#                         #         logger.debug(f'start move = {msg}')
#                         #         self.move_focus_continuous(profileToken=msg['profileToken'],speed=speed_focus,position=0)
#                         #         self.previous_time = time.time()
#                     elif msg['type'] == 'axis':
#                         x = msg['x']
#                         y = msg['y']
#                         if x == 0 and y == 0:
#                             self.controller.ptz_stop(cameraId=self.camera_model.get_property("id", None))
#                             logger.debug(f'stop run_joystick_thread')
#                             return
#                         else:
#                             if (current_time - self.previous_time) > 0.1:
#                                 logger.debug(f'start move = {msg}')
#                                 self.controller.ptz_continuous_move(parent=self,cameraId=self.camera_model.get_property("id", None),x = x,y = y,zoom=speed_zoom)
#                                 self.previous_time = time.time()
#                 else:
#                     time.sleep(0.001)
#             except Exception as e:
#                 logger.error(f'process_joystick_queue fail {e}')

#     def process_queue(self):
#         logger.debug(f'init process_queue')
#         while True:
#             msg = self.coordinate_queue.get()
#             if msg is None:
#                 self.controller.ptz_stop(cameraId=self.camera_model.get_property("id", None))
#                 break
            
#             self.current_time = time.time()
#             if (self.current_time - self.previous_time) > 0.2:
#                 logger.debug(f'process_queue = {msg} -- {self.current_time - self.previous_time}s')
#                 self.previous_time = time.time()
#                 self.controller.ptz_continuous_move(parent=self,cameraId=self.camera_model.get_property("id", None),x = msg['pan'],y = msg['tilt'],zoom=msg['zoom'])
#             self.coordinate_queue.task_done()

#     def callback_mouseReleaseEvent(self, start_point: QPoint= None,end_point: QPoint= None, type = 'crop_frame'):
#         if type == 'crop_frame':
#             # process tính năng crop frame khi đã Draw Rectangle xong
#             self.end_pos = end_point
#             widget_pos = self.mapToGlobal(self.pos())
#             width = self.end_pos.x() - self.start_pos.x()
#             height = self.end_pos.y() - self.start_pos.y()
#             if width > 5 and height > 5:
#                 max = self.preview_width *2/3
#                 if width < max:
#                     ratio = max / width
#                     self.scale_width = self.preview_width *2/3
#                     if (height * ratio) >= self.preview_height:
#                         self.scale_height = self.preview_height
#                     else:
#                         self.scale_height = height * ratio
#                 else:
#                     self.scale_width = width
#                     self.scale_height = height
#                 self.crop_frame_dialog = CropFrameDialog(parent=self,camera_widget= self)
#                 self.crop_frame_dialog.update_resize(self.scale_width,self.scale_height)
#                 self.crop_frame_dialog.move(widget_pos.x(), widget_pos.y())
#                 self.crop_frame_dialog.show()

#         elif type == 'ptz_arrow':
#             if self.allow_ptz_arrow_queue:
#                 self.allow_ptz_arrow_queue = False
#                 self.coordinate_queue.put(None)
#         else:
#             # process tính năng 3D locate khi đã Draw Rectangle xong
#             if abs(end_point.x() - start_point.x()) > 1:
#                 self.controller.set_drag_to_zoom(parent = self, cameraId = self.camera_model.get_property("id", None),start_point = start_point, end_point = end_point,width = self.rectangle_widget.real_width, height = self.rectangle_widget.real_height,calib_data = self.calib_data)

#     def callback_wheelEvent(self, event):
#         end_wheel_time = time.time()
#         if end_wheel_time - self.start_wheel_time < 400:
#             # Nếu 2 lần lăn chuột dưới 400ms thì không callback_exit_key
#             if self.timer is not None:
#                 self.timer.stop()

#         self.start_wheel_time = time.time()
#         current_tab = main_controller.current_tab
#         if grid_item_selected.is_tab_index(current_tab):
#             if grid_item_selected.data['tab_index'] is None:
#                 pass
#             else:
#                 widget = grid_item_selected.data['widget']
#                 if widget == self:
#                     delta = event.angleDelta().y()  
#                     if delta > 0:
#                         if self.previous_direction != 'zoom_in':
#                             self.previous_direction = 'zoom_in'
#                             self.callback_key(direction = 'zoom_in',x = 0, y = 0,zoom = 1,speed = 0.5)
#                         self.zoom_timeout()
#                     else:
#                         if self.previous_direction != 'zoom_out':
#                             self.previous_direction = 'zoom_out'
#                             self.callback_key(direction = 'zoom_out',x = 0, y = 0,zoom = -1,speed = 0.5)
#                         self.zoom_timeout()

#     def zoom_timeout(self):
#         self.timer = QTimer(self)
#         self.timer.setInterval(400)
#         self.timer.setSingleShot(True)
#         self.timer.timeout.connect(self.callback_exit_key)
#         self.timer.start()

#     def close_widget(self):
#         logger.debug("close_widget")
        
#         for _ in range(len(self.threads or [])):
#             self.input_queue.put(None)
#         record_model_manager.unregister_record_data(self)
        
#     def closeEvent(self, event):
#         logger.debug("closeEvent")
#         self.alert_timer.stop()
#         self.next_tracking_timer.stop()
#         self.alert_duration_timer.stop()
#         if self.stack_item is not None:
#             self.stack_item.close()
        
#         for _ in range(len(self.threads or [])):
#             self.input_queue.put(None)

#     def callback_keyPressEvent(self, event):
#         # logger.debug(f'callback_keyPressEvent')
#         current_tab = main_controller.current_tab
#         if grid_item_selected.is_tab_index(current_tab):
#             if grid_item_selected.data['tab_index'] is None:
#                 pass
#             else:
#                 widget = grid_item_selected.data['widget']
#                 if widget == self:
#                     if event.key() == Qt.Key.Key_Right:
#                         if not self.key_pressed:
#                             self.callback_key(direction = 'move_right',x = 0.1,y = 0.001, zoom = None,speed = 0.1)
#                         self.key_pressed = True
#                     elif event.key() == Qt.Key.Key_Left:
#                         if not self.key_pressed:
#                             self.callback_key(direction = 'move_left',x = -0.1,y = 0.001, zoom = None,speed = 0.1)
#                         self.key_pressed = True
#                     elif event.key() == Qt.Key.Key_Up:
#                         if not self.key_pressed:
#                             self.callback_key(direction = 'move_up',x = 0,y = 0.5, zoom = None,speed = 0.1)
#                         self.key_pressed = True
#                     elif event.key() == Qt.Key.Key_Down:
#                         if not self.key_pressed:
#                             self.callback_key(direction = 'move_down',x = 0,y = -0.5, zoom = None,speed = 0.1)
#                         self.key_pressed = True
#                     elif event.key() == Qt.Key.Key_ZoomIn:
#                         self.key_pressed = True
#                     elif event.key() == Qt.Key.Key_ZoomOut:
#                         self.key_pressed = True
#                     elif event.key() == Qt.Key.Key_Plus:
#                         if not self.key_pressed:
#                             self.callback_key(direction = 'zoom_in',x = 0,y = 0, zoom = 0.1,speed = 0.1)
#                         self.key_pressed = True

#                     elif event.key() == Qt.Key.Key_Minus:
#                         if not self.key_pressed:
#                             self.callback_key(direction = 'zoom_out',x = 0,y = 0, zoom = -0.1,speed = 0.1)
#                         self.key_pressed = True
#                     elif event.key() == Qt.Key.Key_R:
#                         camera = CameraModel(camera=replace(self.camera_model.data))
#                         camera.data.recordSetting = False if camera.data.recordSetting else True
#                         camera.data.crowdMethods = []
#                         self.controller.list_aiflows.update_camera_by_put(data=camera.data)
#                     elif event.key() == Qt.Key.Key_P:
#                         self.btn_ptz_clicked()
#                     elif event.key() == Qt.Key.Key_D:
#                         pass
#                         # bật tắt zoom số
#                     elif event.key() == Qt.Key.Key_Delete:
#                         self.btn_close_clicked()
#                     elif event.key() == Qt.Key.Key_I:
#                         # main_controller.get_videos(cameraIds=self.camera_model.get_property("id", None))
#                         camera_screen = main_controller.list_parent['CameraScreen']
#                         camera_screen.show_camera_info_widget(camera_model = self.camera_model)
#                     elif event.key() == Qt.Key.Key_Return or event.key() == Qt.Key.Key_Enter:
#                         # logger.debug(f'event.key() == Qt.Key.Key_Return')
#                         self.btn_full_screen_clicked()
#                     elif event.key() == Qt.Key.Key_F2:
#                         # rename camera
#                         camera_screen = main_controller.list_parent['CameraScreen']
#                         camera_screen.rename_camera_from_preview_trigger(self.camera_name)
#                     elif event.key() == Qt.Key.Key_Escape:
#                         if self.fullscreen_status():
#                             self.btn_full_screen_clicked()
                            
#     def fullscreen_status(self):
#         if self.stack_item is not None:
#             grid_item = self.tab_model.data.listGridData.get(self.stack_item.index, None)
#             logger.debug(f'fullscreen_status: grid_item: {grid_item}')
#             return grid_item.is_fullscreen
#         logger.debug(f'fullscreen_status: self.stack_item is None')
#         return False
    
#     def callback_keyReleaseEvent(self, event):
#         # logger.debug(f'callback_keyReleaseEvent')
#         current_tab = main_controller.current_tab
#         if grid_item_selected.is_tab_index(current_tab):
#             if grid_item_selected.data['tab_index'] is None:
#                 pass
#             else:
#                 # camera_id = grid_item_selected.data['camera_id']
#                 widget = grid_item_selected.data['widget']
#                 if widget == self:
#                     if event.key() == Qt.Key.Key_Right and not event.isAutoRepeat():
#                         self.key_pressed = False
#                         self.callback_exit_key()
#                     elif event.key() == Qt.Key.Key_Left and not event.isAutoRepeat():
#                         self.key_pressed = False
#                         self.callback_exit_key()
#                     elif event.key() == Qt.Key.Key_Up and not event.isAutoRepeat():
#                         self.key_pressed = False
#                         self.callback_exit_key()
#                     elif event.key() == Qt.Key.Key_Down and not event.isAutoRepeat():
#                         self.key_pressed = False
#                         self.callback_exit_key()
#                     elif event.key() == Qt.Key.Key_ZoomIn and not event.isAutoRepeat():
#                         self.key_pressed = False
#                         self.callback_exit_key()
#                     elif event.key() == Qt.Key.Key_ZoomOut and not event.isAutoRepeat():
#                         self.key_pressed = False
#                         self.callback_exit_key()
#                     elif event.key() == Qt.Key.Key_Plus and not event.isAutoRepeat():
#                         self.key_pressed = False
#                         self.callback_exit_key()
#                     elif event.key() == Qt.Key.Key_Minus and not event.isAutoRepeat():
#                         self.key_pressed = False
#                         self.callback_exit_key()

#     def mousePressEvent(self, event):
#         if not self.is_show_header:
#             return
#         if event.button() == Qt.LeftButton:
#             if self.stack_item is None:
#                 return
#             if not self.is_3d_locate and not self.is_crop_frame and not self.is_ptz_dialog:
#                 self.grid_item_clicked(main_controller.current_tab)
#             elif self.is_crop_frame:
#                 self.start_pos = event.pos()

#             row = self.stack_item.position.x()
#             col = self.stack_item.position.y()
#             item_grid = self.childAt(event.position().toPoint())
#             position = (row, col)
#             data = {'id':self.camera_model.get_property("id", None),'tree_type':"Camera", "position":position}
#             data_bytes = pickle.dumps(data)
#             if isinstance(item_grid, QLabel):
#                 # Create and store the drag object
#                 self.drag = QDrag(self)
#                 mime_data = QMimeData()
#                 mime_data.setObjectName("swap_item")
#                 mime_data.setText(self.camera_name)
#                 mime_data.setData("application/position", data_bytes)
#                 self.drag.setMimeData(mime_data)
#                 # Execute the drag operation
#         elif event.button() == Qt.RightButton:
#             if not self.is_virtual_window:
#                 self.grid_item_clicked(main_controller.current_tab)
#                 mouse_position = QCursor.pos()
#                 self.showMenu(mouse_position)

#     def mouseMoveEvent(self, event):
#         if self.drag is not None:
#             # Execute the drag operation
#             self.drag.exec()

#     def mouseReleaseEvent(self, event):
#         pass

#     def dragEnterEvent(self, event):
#         event.acceptProposedAction()

#     def wheelEvent(self, event):
#         self.callback_wheelEvent(event)

#     def keyPressEvent(self, event):
#         pass
#         # logger.debug(f'CameraWidget = {event.key()}')
#         # self.callback_keyPressEvent(event)

#     def keyReleaseEvent(self, event):
#         pass
#         # logger.debug(f'keyReleaseEvent = {event.key()}')
#         # self.callback_keyReleaseEvent(event)

#     def showMenu(self, position):
#         self.main_menu = CustomMenuForCameraRightClick(is_fullscreen=self.fullscreen_status(), controller=self.controller, camera_model=self.camera_model,camera_widget=self, tab_model=self.tab_model, stack_item=self.stack_item)
#         camera_screen = main_controller.list_parent['CameraScreen']
#         self.main_menu.switch_stream_signal.connect(self.switch_stream_signal)
#         self.main_menu.full_screen_signal.connect(self.btn_full_screen_clicked)
#         self.main_menu.remove_from_view_signal.connect(self.btn_close_clicked)
#         self.main_menu.open_setting_signal.connect(lambda: (camera_screen.show_camera_info_widget(camera_model=self.camera_model)))
#         self.main_menu.rename_camera_signal.connect(camera_screen.rename_camera_from_preview_trigger)
#         self.main_menu.exec_(position)
#         pass

#     def switch_stream_signal(self,data):
#         virtual_camera_widget = None
#         index,ai_type,id = data
#         logger.debug(f'switch_stream_signal: {data}')
#         for key, grid_item in self.tab_model.data.listGridData.items():
#             camera_widget = grid_item_selected.data['widget']
#             if camera_widget == grid_item.widget:
#                 virtual_camera_widget = grid_item.virtual_widget
#         if index == 0: # luồng chính
#             if self.player.stream_type != CommonEnum.StreamType.MAIN_STREAM:
#                 # Tạm thời comment chờ có API substream xử lý sau
#                 self.switch_player(StreamCameraType.main_stream)
#         elif index == 1: # luồng phụ
#             if self.player.stream_type != CommonEnum.StreamType.SUB_STREAM:
#                 # Tạm thời comment chờ có API substream xử lý sau
#                 self.switch_player(StreamCameraType.sub_stream)
#         else: # luồng Ai
#             for aiflow_id in self.camera_model.data.aiFlowIds:
#                 aiflow = aiflow_model_manager.get_aiflow_model(id = aiflow_id)
#                 logger.debug(f'ai_flow = {aiflow}')
#                 if aiflow is not None and aiflow.data.type == ai_type:
#                     stream_type = StreamCameraType.get_stream_type(aiflow.data.type)
#                     logger.debug(f'stream_type = {stream_type}')
#                     if self.player.stream_type != stream_type:
#                         self.switch_player(stream_type)
#                     if virtual_camera_widget is not None:
#                         virtual_camera_widget.switch_player(stream_type)
#         main_controller.stream_status_signal.emit((ButtonState.Status.ENABLE,self))

#     def update_dynamic_stylesheet(self):
#         self.footer_widget.setStyleSheet(f'''
#                     QWidget#objHeader {{
#                         background-color: {main_controller.get_theme_attribute("Color", "camera_widget_background")};
#                         border: 2px solid {main_controller.get_theme_attribute("Color", "camera_widget_background")}; 
#                         border-top-left-radius: 4px; 
#                         border-top-right-radius: 4px; 
#                         border-bottom: none;
#                     }}
#                     QWidget#objHeader QLabel {{
#                         border: none;
#                     }}
#                 ''')
#         self.lb_camera_name.setStyleSheet(
#             f"border: 0; font-weight: bold; color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')}; "
#             f"background-color: transparent; padding: 2px; border-radius: 4px")
#         self.root_stack_widget.setStyleSheet(
#             f'''
#                 QStackedWidget {{
#                     margin: 0px;
#                     padding: 0px;
#                     background-color: {main_controller.get_theme_attribute("Color", "camera_widget_background")};
#                     border: 0px solid transparent;
#                     border-bottom-left-radius: 0px;
#                     border-bottom-right-radius: 0px;
#                     border-top-right-radius: 0px;
#                     border-top-right-radius: 0px;
#                 }}
#             '''
#         )

#     def retry_clicked(self, event):
#         """Handle retry symbol click"""
#         # Immediately trigger reconnect
#         self.on_btn_try_reconnect_clicked()

#     def on_process_queue_complete(self, result=None):
#         """Callback for process_queue_thread completion"""
#         if result is not None:
#             logger.debug(f"Process queue completed with result: {result}")
#         else:
#             logger.debug("Process queue completed")

#     def on_process_joystick_queue_complete(self, result=None):
#         """Callback for process_joystick_queue_thread completion"""
#         if result is not None:
#             logger.debug(f"Process joystick queue completed with result: {result}")
#         else:
#             logger.debug("Process joystick queue completed")

#     def process_data(self)-> None:
#         while True:
#             position = self.input_queue.get()
#             if position is None:
#                 logger.debug(f'process_data')
#                 break
#             self.previous_time = None
#             self.current_time = None
#             self.target_time = None
#             # widget = grid_item_selected.data['widget']
#             # if widget is not None and isinstance(widget, CameraWidget):
#             if self.record_data is not None and self.player is not None:
#                 try:
#                     data = self.record_data.get_record(position, self.camera_id)
#                     if data is not None:
#                         video, duration_time_need_seek, duration_to_move = data
#                         source = self.player.get_current_url()
#                         self.video_url = video.data.url
#                         self.seek_time = duration_time_need_seek
#                         # start = datetime.datetime.fromtimestamp(video.data.start_duration / 1000.0)
#                         # seek = datetime.datetime.fromtimestamp((video.data.start_duration+self.seek_time) / 1000.0)
#                         # logger.info(f'start time = {start.isoformat()} seek_time = {seek.isoformat()}')
#                         self.start_duration = video.data.start_duration
#                         self.end_duration = video.data.end_duration
#                         if duration_to_move != -1:
#                             self.timelinecontroller.shift_for_duration(duration_to_move)

#                         if self.player.stream_type != StreamCameraType.video_stream:
#                             if not self.is_virtual_window:
#                                 self.switch_player(stream_type = StreamCameraType.video_stream)
#                             else:
#                                 for key, grid_item in self.tab_model.data.listGridData.items():
#                                     if self == grid_item.virtual_widget:
#                                         widget = grid_item.widget
#                                         if widget is not None:
#                                             widget.switch_player(stream_type = StreamCameraType.video_stream)
#                             self.timelinecontroller.isNextChunk = True
#                             self.timelinecontroller.isNextFrame = True
#                         else:
#                             if (video.data.url is not None and video.data.url != source):
#                                 self.timelinecontroller.isNextChunk = True
#                                 self.timelinecontroller.isNextFrame = True
#                                 self.player.stop_video()
#                                 self.player.load_media(video.data.url, seek_time=duration_time_need_seek,start_duration=video.data.start_duration,end_duration=video.data.end_duration)
#                                 # widget.player.set_position(duration_time_need_seek)
#                                 # end = datetime.datetime.fromtimestamp((video.data.start_duration+self.player.get_length()) / 1000.0)
#                                 # logger.info(f'end time = {end.isoformat()}')
#                                 self.player.play_video()
#                             else:
#                                 if self.player.get_length() != 0 and self.player.is_playing():
#                                     # end = datetime.datetime.fromtimestamp((video.data.start_duration+self.player.get_length()) / 1000.0)
#                                     # logger.info(f'end time = {end.isoformat()}')
#                                     position_seek = float(duration_time_need_seek)/float(self.player.get_length())
#                                     self.player.set_position(position_seek)
#                                 elif self.player.get_length() != 0 and not self.player.is_playing():
#                                     self.player.play_video()
#                                     # end = datetime.datetime.fromtimestamp((video.data.start_duration+self.player.get_length()) / 1000.0)
#                                     # logger.info(f'end time = {end.isoformat()}')
#                                     position_seek = float(duration_time_need_seek)/float(self.player.get_length())
#                                     self.player.set_position(position_seek)

#                         self.timelinecontroller.isLive = False
#                         self.timelinecontroller.positionBubble = True
#                 except ValueError as e:
#                     logger.error(f"Error parsing position timestamp: {e}")
#                 except Exception as e:
#                     logger.error(f"Error handling position click: {e}")
#             self.input_queue.task_done()

#     def start_threads(self,number: int, target: Callable, *args) -> List[threading.Thread]:
#         threads = []
#         for _ in range(number):
#             thread = threading.Thread(target=target, args=args)
#             thread.daemon = True
#             threads.append(thread)
#             thread.start()
#         return threads
    
# class CustomMenuForCameraRightClick(QMenu):
#     switch_stream_signal = Signal(tuple)
#     full_screen_signal = Signal()
#     remove_from_view_signal = Signal()
#     open_setting_signal = Signal()
#     rename_camera_signal = Signal(object)

#     def __init__(self, is_fullscreen=False, controller=None, camera_model=None,camera_widget = None, tab_model = None, stack_item = None):
#         super().__init__()
#         self.stack_item = stack_item
#         self.tab_model = tab_model
#         self.camera_model = camera_model
#         self.is_fullscreen = is_fullscreen
#         self.controller = controller
#         self.camera_widget = camera_widget
#         self.setWindowFlags(
#             Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
#         self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
#         self.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
#         self.init_action_and_menu()
#         self.addActionToMenu()

#     def action_change_ai_single_camera_click(self, data_emitted):
#         index, ai_flow_id_emitted, camera_model = data_emitted
#         list_ai: dict[str, AiFlow] = self.get_list_ai_flow(camera_model.data.aiFlowIds)
#         ai_flows = None
#         if len(list_ai) > 0:
#             for aiFlowId, aiflow_model in list_ai.items():
#                 aiflow = aiflow_model.data
#                 if ai_flow_id_emitted == aiFlowId:
#                     ai_flows = aiflow
#         ai_flows.apply = not ai_flows.apply
#         self.enable_disable_ai_flow(ai_flows)

#     def enable_disable_ai_flow(self, ai_flows):
#         if ai_flows is not None:
#             def enable(data):
#                 result = self.controller.update_aiflows(data=data)
#                 if result is not None:
#                     return True
#                 return False
#             def callback(result):
#                 if not result:
#                     pass
#             WorkerThread(parent=self, target=enable, callback=callback, args=(ai_flows,)).start()

#     def action_aitype_signal(self, data_emitted):
#         logger.info(f'action_aitype_signal data_emitted = {data_emitted}')
#         key, model = data_emitted
#         logger.info(f'action_aitype_signal key = {key}')
#         logger.info(f'action_aitype_signal model = {model}')
#         data = None
#         apply = False
#         ai_flow_ids = model.data.aiFlowIds
#         type = None
#         if key == AIFlowType.RECOGNITION:
#             type = AIFlowType.RECOGNITION.__str__()
#         elif key == AIFlowType.PROTECTION:
#             type = AIFlowType.PROTECTION.__str__()
#         elif key == AIFlowType.FREQUENCY:
#             type = AIFlowType.FREQUENCY.__str__()
#         elif key == AIFlowType.ACCESS:
#             type = AIFlowType.ACCESS.__str__()
#         elif key == AIFlowType.MOTION:
#             type = AIFlowType.MOTION.__str__()
#         elif key == AIFlowType.TRAFFIC:
#             type = AIFlowType.TRAFFIC.__str__()
#         elif key == AIFlowType.WEAPON:
#             type = AIFlowType.WEAPON.__str__()
#         elif key == AIFlowType.UFO:
#             type = AIFlowType.UFO.__str__()
#         if len(ai_flow_ids) > 0:
#             for ai_flow_id in ai_flow_ids:
#                 ai_flow = aiflow_model_manager.get_aiflow_model(id=ai_flow_id)
#                 if ai_flow is not None:
#                     if type == ai_flow.data.type:
#                         apply = not ai_flow.data.apply
#                         data = {
#                             "id": model.get_property("id", None),
#                             "apply": apply,
#                             "type": type
#                         } 
#                         break
#         if data is None:
#             data = {
#                 "id": model.get_property("id", None),
#                 "apply": True,
#                 "type": type
#             } 
#         self.controller.apply_ai_flow_camera(data = data)


#     def get_list_ai_flow(self, aiFlowIds: List = []):
#         list_ai_flow = {}
#         for aiFlowId in aiFlowIds:
#             aiflow = aiflow_model_manager.get_aiflow_model(id=aiFlowId)
#             if aiflow is not None:
#                 list_ai_flow[aiFlowId] = aiflow
#         return list_ai_flow

#     def switch_stream(self, data):
#         logger.info(f'switch_stream = {data}')
#         self.switch_stream_signal.emit(data)

#     def camera_open_in_tab(self, data):
#         model, tab_name, row_col, item_standard, list_camera_selection = data
#         row = row_col[0]
#         col = row_col[1]
#         new_data = (model, tab_name, row, col, item_standard, list_camera_selection)
#         main_controller.open_camera_in_tab_signal.emit(new_data)

#     def remove_from_view_trigger(self):
#         self.remove_from_view_signal.emit()

#     def full_screen_trigger(self):
#         # self.btn_full_screen_clicked()
#         self.full_screen_signal.emit()

#     def open_setting_trigger(self, camera_name):
#         self.open_setting_signal.emit()

#     def rename_camera_trigger(self, camera_model):
#         self.rename_camera_signal.emit(camera_model)

#     def init_action_and_menu(self):
#         list_type_ai_flow = {
#             AIFlowType.RECOGNITION: self.tr("Recognition"),
#             AIFlowType.PROTECTION: self.tr("Protection"),
#             AIFlowType.FREQUENCY: self.tr("Frequency"),
#             AIFlowType.ACCESS: self.tr("Access"),
#             AIFlowType.MOTION: self.tr("Motion"),
#             AIFlowType.TRAFFIC: self.tr("Traffic"),
#             AIFlowType.WEAPON: self.tr("Weapon"),
#             AIFlowType.UFO: self.tr("UFO")
#         }
#         self.sub_menu_ai_flow = CustomMenuWithCheckbox(item_data=TreeType.Camera,
#                                                        list_type_ai_flow=list_type_ai_flow, controller=self.controller,
#                                                        item_model=self.camera_model)
#         self.sub_menu_ai_flow.action_aitype_signal.connect(self.action_aitype_signal)
#         self.sub_menu_video_streams = CustomMenuWithCheckbox(item_data=TreeType.Camera,
#                                                              list_type_video_stream=["Main", "Sub", "AI"],
#                                                              camera_widget=self.camera_widget, controller=self.controller,
#                                                              item_model=self.camera_model)
#         self.sub_menu_video_streams.signal_change_video_stream.connect(self.switch_stream)
#         ###############################################
#         self.menu_list_tab_available = SubMenuOpenCameraInTab(model =self.camera_model,  item_data=TreeType.Camera, camera_name=self.camera_model.data.name, show_grid_position=True)
#         self.menu_list_tab_available.open_in_tab_signal.connect(self.camera_open_in_tab)
#         self.menu_list_tab_available.open_new_tab_signal.connect(self.camera_open_in_tab)

#         self.action_open_camera_to = QAction(self.tr("Open camera to ...    "), self, menu=self.menu_list_tab_available)

#         # self.create_group_action = QAction(text=self.tr("Create group"), parent=self)
#         # self.create_group_action.triggered.connect(lambda: (self.create_group_trigger(item)))

#         self.remove_from_view_action = QAction(text=self.tr("Remove form view\tDel"), parent=self)
#         self.remove_from_view_action.setShortcut(QKeySequence(Qt.Key.Key_Delete))
#         self.remove_from_view_action.triggered.connect(self.remove_from_view_trigger)

#         # self.rename_action = QAction(text=self.tr("Rename\tF2"), parent=self)
#         # self.rename_action.setShortcut(QKeySequence(Qt.Key_F2))
#         # self.rename_action.triggered.connect(lambda: (self.rename_camera_trigger(self.camera_model)))

#         # self.record_action = QAction(text=self.tr("Record"), parent=self.main_menu)
#         # self.record_action.triggered.connect(lambda: (self.edit_virtual_window_triggered(item)))

#         self.choose_ai_flow_action = QAction(text=self.tr("AI flow"), parent=self)
#         self.choose_ai_flow_action.setMenu(self.sub_menu_ai_flow)

#         self.choose_video_stream_action = QAction(text=self.tr("Video streams"), parent=self)
#         self.choose_video_stream_action.setMenu(self.sub_menu_video_streams)

#         self.open_settings_action = QAction(text=self.tr("Setting\tI"), parent=self)
#         self.open_settings_action.setShortcut(QKeySequence(Qt.Key_I))
#         self.open_settings_action.triggered.connect(lambda: (self.open_setting_trigger(self.camera_model.get_property("id", None))))

#         current_grid = self.tab_model.data.currentGrid if self.tab_model else None
#         is_1x1_grid = current_grid and current_grid['row'] == 1 and current_grid['column'] == 1
        
#         if not is_1x1_grid:  # Only create fullscreen action if not 1x1 grid
#             if self.is_fullscreen:
#                 self.full_screen_action = QAction(text=self.tr("Exit full-screen\tEsc"), parent=self)
#                 self.full_screen_action.setShortcut(QKeySequence(Qt.Key.Key_Escape))
#             else:
#                 self.full_screen_action = QAction(text=self.tr("Full-screen\tEnter"), parent=self)
#                 self.full_screen_action.setShortcut(QKeySequence(Qt.Key_Enter))
#             self.full_screen_action.triggered.connect(self.full_screen_trigger)

#     def addActionToMenu(self):
#         self.addAction(self.action_open_camera_to)
#         self.addSeparator()
#         # self.addAction(self.create_group_action)
#         # self.addSeparator()
#         self.addAction(self.remove_from_view_action)
#         self.addSeparator()
#         # self.addAction(self.rename_action)
#         # self.addSeparator()
#         # self.main_menu.addAction(self.record_action)
#         self.addAction(self.choose_ai_flow_action)
#         self.addAction(self.choose_video_stream_action)
#         self.addSeparator()
#         self.addAction(self.open_settings_action)
#         self.addSeparator()
#         # Only add fullscreen action if it exists
#         current_grid = self.tab_model.data.currentGrid if self.tab_model else None
#         is_1x1_grid = current_grid and current_grid['row'] == 1 and current_grid['column'] == 1
        
#         if not is_1x1_grid and hasattr(self, 'full_screen_action'):
#             self.addSeparator()
#             self.addAction(self.full_screen_action)

# class CropFrameDialog(QDialog):
#     def __init__(self, parent=None,camera_widget = None):
#         super().__init__(parent, Qt.FramelessWindowHint)
#         self.setAttribute(Qt.WA_TransparentForMouseEvents)
#         self.setFixedHeight(210)
#         self.setFixedWidth(210)
#         self.setAttribute(Qt.WA_TranslucentBackground)
#         self.camera_widget = camera_widget
#         self.setStyleSheet(f'''
#                                 background-color: rgba(255, 255, 255, 0.2);
#                                 border: 1px solid #B5122E;
#                            ''')
#         self.crop_frame = QLabel()
#         layout_dialog = QVBoxLayout()
#         layout_dialog.setContentsMargins(0,0,0,0)
#         layout_dialog.addWidget(self.crop_frame)
#         self.setLayout(layout_dialog)
#         self.setModal(False)
#         QApplication.instance().installEventFilter(self)

#     def update_frame(self, frame:QPixmap = None):
#         self.crop_frame.setPixmap(frame)

#     def update_resize(self, width = None, height = None):
#         self.setSizeIncrement(width,height)


#     def eventFilter(self, source, event):
#         if event.type() == QEvent.MouseButtonPress:
#             # Kiểm tra xem vị trí của sự kiện chuột có nằm trong vùng của cửa sổ cha hay không

#             global_pos = event.globalPos()
#             if not self.geometry().contains(global_pos):
#                 QApplication.instance().removeEventFilter(self)
#                 self.close()
#                 self.camera_widget.crop_frame_dialog = None
#         return super().eventFilter(source, event)

# class RectangleWidget(QWidget):
#     def __init__(self, parent=None, callback_mousePressEvent = None,callback_mouseReleaseEvent = None,callback_mouseMoveEvent = None, type = 'crop_frame'):
#         super().__init__(parent)
#         self.real_width = None
#         self.real_height = None
#         layout = QVBoxLayout(self)
#         layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
#         self.label = QLabel()
#         self.label.hide()
#         # self.label.setStyleSheet("background-color: #B5122E;border-radius:2px")
#         self.label.setFixedSize(35,35)
#         pixmap = QPixmap(main_controller.get_theme_attribute("Image", "ptz_arrow"))
#         self.label.setPixmap(pixmap)
#         layout.addWidget(self.label)
#         self.setMouseTracking(True)
#         self.type = type
#         self.callback_mousePressEvent = callback_mousePressEvent
#         self.callback_mouseReleaseEvent = callback_mouseReleaseEvent
#         self.callback_mouseMoveEvent = callback_mouseMoveEvent
#         self.is_drawing = False
#         self.start_pos = None
#         self.end_pos = None
#         self.is_enable = False
#         self.is_line = False

#     def paintEvent(self, event):
#         if self.is_enable:
#             painter = QPainter(self)
#             painter.setRenderHint(QPainter.Antialiasing)
#             if self.is_drawing:
#                 if self.type == 'ptz_arrow':
#                     painter.setPen(QPen(QColor('#FFFFFF'), 4))
#                     self.label.show()

#                     if self.is_line:
#                         painter.drawLine(self.start_pos.x(),self.start_pos.y() , self.end_pos.x(), self.end_pos.y())
#                     angle = math.atan2(self.end_pos.y()-self.start_pos.y(),self.end_pos.x()-self.start_pos.x())
#                     # angle_deg = math.degrees(angle)
#                     # logger.debug(f'paintEvent = {angle_deg}')
#                     x = 10 * math.cos(angle)
#                     y = 10 * math.sin(angle)
#                     x1 = self.end_pos.x() - x
#                     y1 = self.end_pos.y() - y
#                     x2 = 5 * math.sin(angle)
#                     y2 = 5 * math.cos(angle)
#                     painter.drawLine(x1+x2,y1-y2 , self.end_pos.x(), self.end_pos.y())
#                     painter.drawLine(x1-x2,y1+y2 , self.end_pos.x(), self.end_pos.y())
#                     painter.end()
#                 else:
#                     painter.setPen(QPen(QColor('#5B5B9F'), 2))
#                     rect = QRect(self.start_pos, self.end_pos)
#                     self.label.hide()
#                     painter.drawRect(rect)
#                 # painter.drawRect(QRect(QPoint(self.start_pos.x()-10,self.start_pos.y()-10), QPoint(self.start_pos.x()+10,self.start_pos.y()+10)))

#     def mousePressEvent(self, event):
#         if self.is_enable:
#             if event.button() == Qt.LeftButton:
#                 if self.type == 'ptz_arrow':
#                     self.is_drawing = True
#                     self.is_line = True
#                     self.update()
#                     if self.callback_mousePressEvent is not None:
#                         self.callback_mousePressEvent(start_point = self.start_pos,end_point = self.end_pos, type = self.type)
#                 elif self.type == 'crop_frame' or self.type == '3d_local':
#                     self.is_drawing = True
#                     self.start_pos = event.pos()
#                     self.end_pos = event.pos()
#                     if self.callback_mousePressEvent is not None:
#                         self.callback_mousePressEvent(start_point = self.start_pos,end_point = self.end_pos, type = self.type)
#                     self.update()
#                 else:
#                     self.update()

#     def mouseReleaseEvent(self, event):
#         if self.is_enable:
#             if event.button() == Qt.LeftButton:
#                 if self.type == 'ptz_arrow':
#                     self.is_drawing = True
#                     self.is_line = False
#                     self.update()
#                     if self.callback_mouseReleaseEvent is not None:
#                         self.callback_mouseReleaseEvent(start_point = self.start_pos,end_point = self.end_pos, type = self.type)
#                 elif self.type == 'crop_frame' or self.type == '3d_local':

#                     self.is_drawing = False
#                     self.update()
#                     if self.callback_mouseReleaseEvent is not None:
#                         self.callback_mouseReleaseEvent(start_point = self.start_pos,end_point = self.end_pos, type = self.type)

#     def mouseMoveEvent(self, event):
#         if self.is_enable:
#             if self.type == 'ptz_arrow':
#                 self.is_drawing = True
#                 self.end_pos = event.pos()
#                 self.update()
#                 if self.callback_mouseMoveEvent is not None:
#                     self.callback_mouseMoveEvent(start_point = self.start_pos,end_point = self.end_pos, type = self.type)
#             else:
#                 if self.is_drawing:
#                     self.end_pos = event.pos()
#                     self.update()

#     def enterEvent(self, event):
#         if self.type == 'ptz_arrow':
#             self.is_drawing = True
#             self.end_pos = event.pos()
#             self.start_pos = QPoint(self.width()/2,self.height()/2)

#     def leaveEvent(self, event):
#         # Xử lý khi con trỏ chuột rời khỏi QWidget
#         if self.type == 'ptz_arrow':
#             self.is_drawing = False
