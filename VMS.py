# This Python file uses the following encoding: utf-8
import math
from PySide6.QtWidgets import QApplication, QMainWindow, Q<PERSON><PERSON>og, QWidget, QSplashScreen
from src.presentation.camera_screen.tracking_camera_grid_widget import TrackingCameraGridWidget
from src.presentation.camera_screen.virtual_camera_grid_widget import VirtualCameraGridWidget
import src.presentation.main_screen.main_screen as Screen
from src.utils.config import Config
from src.utils.auth_qsettings import AuthQSettings
from src.styles.style import Style
from PySide6.QtGui import QIcon, QGuiApplication, QFont, QFontDatabase, QPalette, QColor, Qt, QPainter, QBrush, QCloseEvent, QPixmap
from PySide6.QtCore import QTranslator, Signal, QDateTime, QEvent, QObject, QLoggingCategory
from src.utils.setting_screen_qsetting import SettingScreenQSettings
from src.utils.theme_setting import ThemeSettings
from src.common.controller.main_controller import main_controller
import faulthandler
import traceback
import sys
import json
import os
import logging
logger = logging.getLogger(__name__)

def exception_hook(exctype, value, tb):
    with open("crash_log.txt", "w") as f:
        f.write("".join(traceback.format_exception(exctype, value, tb)))
    print("Ứng dụng bị crash, log đã được lưu vào crash_log.txt")
    sys.__excepthook__(exctype, value, tb)  # Gọi excepthook mặc định nếu cần

sys.excepthook = exception_hook  # Đặt hook để bắt lỗi không mong muốn

class MainWindow(QMainWindow):

    def __init__(self):
        QMainWindow.__init__(self)
        self.setWindowFlag(Qt.WindowType.FramelessWindowHint)
        # create controller
        self.overlay = None
        self.dialog_monitor = None
        self.list_ptz_onvif = []
        self.list_ipcamera = []
        self.auth_qsettings = AuthQSettings.get_instance()
        self.setting_qsettings = SettingScreenQSettings()

        self.theme_settings = ThemeSettings()

        self.load_ui()

    def focusOutEvent(self, event):
        # Ensure the window stays focused
        self.activateWindow()
        self.raise_()
        super().focusOutEvent(event)

    def load_ui(self):
        self.dialog_monitor = DialogMonitor(self)
        self.dialog_monitor.signal_control_overlay.connect(self.show_overlay)
        QApplication.instance().installEventFilter(self.dialog_monitor)

        logger.debug('MainWindow load_ui')
        # update size window
        screen = QGuiApplication.primaryScreen()
        self.desktop_screen_size = screen.availableGeometry()
        # logger.debug("desktop_screen_size", self.desktop_screen_size)
        self.setGeometry(self.desktop_screen_size)
        self.setMinimumSize(self.desktop_screen_size.width()*0.8, self.desktop_screen_size.height()*0.8)

        self.server_screen = self.load_server_screen()
        self.setCentralWidget(self.server_screen)
        self.setContentsMargins(0, 0, 0, 0)
        # Create the overlay
        self.overlay = Overlay(self)
        self.overlay.hide()

    def load_server_screen(self):
        self.main_screen = Screen.MainScreen(window_parent=self)
        self.main_screen.setting_screen.general_setting.language_change_signal.connect(
            self.change_language)
        main_controller.theme_change_signal.connect(self.change_theme)
        return self.main_screen

    def closeEvent(self, event: QCloseEvent) -> None:
        logger.debug("closeEvent MainWindow !!!")
        for window in QApplication.topLevelWidgets():
            if isinstance(window, QDialog):
                window.reject()
            elif isinstance(window, VirtualCameraGridWidget):
                window.close()
            elif isinstance(window, TrackingCameraGridWidget):
                window.close()

        super().closeEvent(event)

    def change_language(self, current_language):
        translator.load(current_language)
        app.installTranslator(translator)
        self.retranslate_Ui_main()

    def change_theme(self):
        self.restyle_Ui_main()

    def retranslate_Ui_main(self):
        if self.main_screen:
            self.main_screen.retranslate_Ui()
        self.update()

    def restyle_Ui_main(self):
        if self.main_screen:
            self.main_screen.restyle_Ui()
        self.update()

    def on_about_to_quit(self):
        logger.debug("Application is about to quit, performing cleanup...")
        # # get all thread running
        # homecsreen
        logger.info("stop_app")
        self.server_screen.stop_app()

        # Kill app immediately
        os._exit(0)  # Force exit with status code 0

        logger.debug(
            f"QUIT APPLICATION at {QDateTime.currentDateTime().toString('yyyy-MM-dd hh:mm:ss')}")

    def changeEvent(self, event):
        if event.type() == QEvent.Type.WindowStateChange:
            if self.server_screen is not None:
                self.server_screen.window_state_changed(self.window().windowState())
        super().changeEvent(event)
        event.accept()

    def show_overlay(self, is_show):
        if self.overlay:
            if is_show:
                if self.overlay.isVisible():
                    pass
                else:
                    self.overlay.show()
                    self.overlay.setVisible(True)
            else:
                if not self.overlay.isVisible():
                    pass
                else:
                    self.overlay.hide()
                    self.overlay.setVisible(False)

    def resizeEvent(self, event):
        if self.overlay is not None:
            self.overlay.resize(self.size())
        main_controller.mainwindow_geometry = self.geometry()
        super().resizeEvent(event)
    
    def moveEvent(self, event):
        main_controller.mainwindow_geometry = self.geometry()
        super().moveEvent(event)

class Overlay(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        palette = QPalette(self.palette())
        # palette.setColor(palette.Background, Qt.transparent)
        self.setPalette(palette)

    def paintEvent(self, event):
        painter = QPainter()
        painter.begin(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.fillRect(event.rect(), QBrush(QColor(0, 0, 0, 178)))
        painter.end()

    def resizeEvent(self, event):
        self.resize(self.parent().size())

class DialogMonitor(QObject):
    signal_control_overlay = Signal(bool)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.dialog_open = False
        self.dialogs = []

    def eventFilter(self, obj, event):
        if isinstance(obj, QDialog) and obj.objectName() != 'PtzDialog':
            if obj.isVisible():
                if obj.objectName() not in self.dialogs:
                    if len(self.dialogs) == 0:
                        self.dialogs.append(obj.objectName())
                    if len(self.dialogs) > 0:
                        self.signal_control_overlay.emit(True)
            else:
                if obj.objectName() in self.dialogs:
                    self.dialogs.remove(obj.objectName())
                    if len(self.dialogs) == 0:
                        self.signal_control_overlay.emit(False)
            # if obj.isVisible():
            #     self.dialog_open = True
            #     self.signal_test.emit(self.dialog_open)
            # else:
            #     self.dialog_open = False
            #     self.signal_test.emit(self.dialog_open)
        return super().eventFilter(obj, event)


if Config.DEBUG:
    faulthandler.enable()

# Load version from JSON file
version_string = "unknown"
try:
    version_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "version.json")
    if os.path.exists(version_file_path):
        with open(version_file_path, 'r') as version_file:
            version_data = json.load(version_file)
            version_string = version_data.get("version_string", "unknown")
            print(version_string)
except Exception as e:
    logger.error(f"Error loading version file: {e}")


if __name__ == "__main__":
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    # QQuickStyle.setStyle("Material")
    logger.debug(
        f'STARTING APPLICATION at {QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss")}')
    
    # Force the style to be the same on all OSs:
    app.setStyle("Fusion")

    # Mặc định là ngôn ngữ Anh
    translator = QTranslator()
    translator.load(SettingScreenQSettings.get_instance().get_current_language())
    # Use QFontDatabase to load the custom font from an asset
    font_id = QFontDatabase.addApplicationFont(
        ":src/assets/fonts/OpenSans-Regular.ttf")
    if font_id != -1:
        # Create a QFont object with the custom font family
        custom_font = QFont(QFontDatabase.applicationFontFamilies(font_id)[0])

        screen = app.primaryScreen()
        screen_size = screen.size()
        screen_dpi = screen.logicalDotsPerInch()

        # Calculate the physical size of the screen in inches
        screen_width_inches = screen_size.width() / screen_dpi
        screen_height_inches = screen_size.height() / screen_dpi
        screen_diagonal_inches = math.sqrt(screen_width_inches ** 2 + screen_height_inches ** 2)

        # Determine the font size based on the screen resolution or DPI
        if screen_diagonal_inches > 26:
            custom_font.setPixelSize(14)  # Use a larger font size for large screens
        else:
            custom_font.setPixelSize(12)
        app.setFont(custom_font)
    logger.debug(f'translator: {translator}')
    app.installTranslator(translator)
    app.setWindowIcon(QIcon(Style.PrimaryImage.icon128))
    app.setApplicationName(Style.Text.app_name)
    app.setApplicationDisplayName(Style.Text.app_name)
    app.setApplicationVersion(Style.Text.app_version)

    widget = MainWindow()
    widget.showFullScreen()
    widget.show()


    app.aboutToQuit.connect(widget.on_about_to_quit)

    sys.exit(app.exec())
