
from PySide6.QtCore import QObject, Property, Signal
import logging
logger = logging.getLogger(__name__)

class Model(QObject):
    def __init__(self,data: dict = {}):
        super().__init__()
        self.data = data

    def get_property(self, key, default=None):
        """Lấy giá trị từ self.data, nếu không có thì trả về default."""
        return self.data.get(key, default)

    def set_property(self, key, value):
        """Cập nhật giá trị trong self.data."""
        self.data[key] = value
