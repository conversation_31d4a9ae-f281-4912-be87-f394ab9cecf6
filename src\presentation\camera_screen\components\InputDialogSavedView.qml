import QtQuick 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: newSavedViewDialog
    width: 340
    height: 180
    radius: 12
    color: "#fff"
    border.color: "#e0e0e0"
    border.width: 1.5
    visible: false
    anchors.centerIn: parent
    signal accepted(string inputText)
    signal rejected()
    property alias inputText: nameInput.text

    Column {
        anchors.centerIn: parent
        spacing: 18
        width: parent.width - 40

        // // Header with icon and title
        // Row {
        //     spacing: 8
        //     anchors.horizontalCenter: parent.horizontalCenter
        //     Image {
        //         source: "qrc:/icons/warning.svg" // Đổi thành icon bạn có
        //         width: 24; height: 24
        //     }
        //     Text {
        //         text: "Điền tê"
        //         font.bold: true
        //         font.pixelSize: 18
        //         color: "#222"
        //     }
        // }

        // Nội dung
        TextField {
            id: nameInput
            placeholderText: "Nhậ<PERSON> tên màn hình để lưu..."
            width: parent.width
            font.pixelSize: 15
            focus: true
            background: Rectangle {
                color: "#f8f8fa"
                radius: 8
                border.color: "#d0d0e0"
                border.width: 1
            }
            padding: 8
            KeyNavigation.tab: createBtn
        }
        Keys.onReturnPressed: {
            // Chỉ gọi khi dialog đang visible
            if (newSavedViewDialog.visible) {
                newSavedViewDialog.visible = false
                newSavedViewDialog.accepted(nameInput.text)
            }
        }
        // Nếu muốn hỗ trợ cả phím Enter trên numpad:
        Keys.onEnterPressed: {
            if (newSavedViewDialog.visible) {
                newSavedViewDialog.visible = false
                newSavedViewDialog.accepted(nameInput.text)
            }
        }
        // Nút
        Row {
            spacing: 16
            anchors.horizontalCenter: parent.horizontalCenter

            Button {
                id: createBtn
                text: "Create"
                width: 120
                height: 40
                font.bold: true
                background: Rectangle {
                    color: createBtn.down ? "#16a085"
                          : (createBtn.hovered || createBtn.activeFocus ? "#48e1c1" : "#1abc9c")
                    border.color: "transparent"
                    border.width: 0
                    radius: 8
                }
                contentItem: Text {
                    text: createBtn.text
                    color: "white"
                    font.bold: true
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                    anchors.fill: parent
                }
                focusPolicy: Qt.StrongFocus
                KeyNavigation.tab: cancelBtn
                KeyNavigation.backtab: nameInput
                onClicked: {
                    newSavedViewDialog.visible = false
                    newSavedViewDialog.accepted(nameInput.text)
                }
            }
            Button {
                id: cancelBtn
                text: "Cancel"
                width: 120
                height: 40
                font.bold: true
                background: Rectangle {
                    color: cancelBtn.down ? "#ffeaea"
                          : (cancelBtn.hovered || cancelBtn.activeFocus ? "#fff5f5" : "white")
                    border.color: cancelBtn.activeFocus ? "#e74c3c" : "#ffb3b3"
                    border.width: cancelBtn.activeFocus ? 2 : 1
                    radius: 8
                }
                contentItem: Text {
                    text: qsTr("Cancel")
                    color: "#e74c3c"
                    font.bold: true
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                focusPolicy: Qt.StrongFocus
                KeyNavigation.tab: nameInput
                KeyNavigation.backtab: createBtn
                onClicked: {
                    newSavedViewDialog.visible = false
                    newSavedViewDialog.rejected()
                }
            }
        }
    }
}
