import QtQuick 2.15
import "../constants/ZIndexConstants.js" as ZIndex

/**
 * GridLinesOverlay.qml - Grid Lines Management Component
 */
Item {
    id: root
    anchors.fill: parent

    property var gridModel: null
    property bool isDarkTheme: true
    property bool showGridLines: false
    property real gridLinesOpacity: 0.3
    property real cellSpacing: 0
    property bool hasFullscreenItem: false
    property bool hasActiveModal: false
    property bool hasPtzActive: false
    property bool hasContextMenu: false
    property bool isCtrlWheelActive: false
    property bool isDragActive: false
    property bool isManualToggle: false

    Timer {
        id: ctrlWheelHideTimer
        interval: 1500
        repeat: false
        onTriggered: {
            root.isCtrlWheelActive = false
            updateVisibility()
        }
    }

    Timer {
        id: dragHideTimer
        interval: 1000
        repeat: false
        onTriggered: {
            root.isDragActive = false
            updateVisibility()
        }
    }

    // ✅ CANVAS: High-performance grid lines rendering
    Canvas {
        id: gridLinesCanvas
        anchors.fill: parent
        visible: root.showGridLines
        opacity: root.gridLinesOpacity
        z: ZIndex.contentGridLines

        // Performance optimization properties
        property int cachedColumns: 1
        property int cachedRows: 1
        property bool needsRepaint: false

        // Throttled repaint timer (60 FPS max)
        Timer {
            id: repaintTimer
            interval: 16
            repeat: false
            onTriggered: {
                if (gridLinesCanvas.needsRepaint) {
                    gridLinesCanvas.requestPaint()
                    gridLinesCanvas.needsRepaint = false
                }
            }
        }

        function scheduleRepaint() {
            needsRepaint = true
            if (!repaintTimer.running) {
                repaintTimer.start()
            }
        }

        onPaint: {
            var ctx = getContext("2d")
            ctx.clearRect(0, 0, width, height)

            // Early exit for invalid dimensions
            if (width <= 0 || height <= 0) return

            // Get grid dimensions from GridModel
            var columns = gridModel ? gridModel.columns : 1
            var rows = gridModel ? gridModel.rows : 1

            // Cache dimensions and skip if unchanged
            if (columns === cachedColumns && rows === cachedRows && !needsRepaint) {
                return
            }
            cachedColumns = columns
            cachedRows = rows
            // Early exit for 1x1 grid (no lines needed)
            if (columns <= 1 && rows <= 1) return

            var cellWidth = (width - (columns - 1) * cellSpacing) / columns
            var cellHeight = (height - (rows - 1) * cellSpacing) / rows

            // Dynamic color based on theme
            ctx.strokeStyle = root.isDarkTheme ? "#404040" : "#e0e0e0"
            ctx.lineWidth = 1

            // Batch drawing operations
            ctx.beginPath()

            // Draw vertical lines (spacing kiểu 'around')
            for (var i = 1; i < columns; i++) {
                var x = i * (cellWidth + cellSpacing) + cellSpacing / 2
                ctx.moveTo(x, 0)
                ctx.lineTo(x, height)
            }

            // Draw horizontal lines (spacing kiểu 'around')
            for (var j = 1; j < rows; j++) {
                var y = j * (cellHeight + cellSpacing) + cellSpacing / 2
                ctx.moveTo(0, y)
                ctx.lineTo(width, y)
            }

            // Single stroke call for all lines
            ctx.stroke()
        }

        // Auto-repaint on size changes
        onWidthChanged: scheduleRepaint()
        onHeightChanged: scheduleRepaint()
    }

    // ✅ CONNECTIONS: Listen to gridModel changes
    Connections {
        target: gridModel
        function onColumnsChanged() {
            gridLinesCanvas.scheduleRepaint()
        }
        function onRowsChanged() {
            gridLinesCanvas.scheduleRepaint()
        }
    }

    // ✅ CORE FUNCTIONS: Priority-based visibility calculation
    function updateVisibility() {
        var shouldShow = false
        var reason = "default_hidden"
        
        // Priority 1: FORCE_HIDE (highest priority - always hide)
        if (root.hasFullscreenItem) {
            shouldShow = false
            reason = "fullscreen_active"
        } else if (root.hasActiveModal) {
            shouldShow = false
            reason = "modal_active"
        } else if (root.hasPtzActive) {
            shouldShow = false
            reason = "ptz_active"
        } else if (root.hasContextMenu) {
            shouldShow = false
            reason = "context_menu_active"
        }
        // Priority 2: SHOW_TEMPORARY (medium priority - show during interactions)
        else if (root.isCtrlWheelActive) {
            shouldShow = true
            reason = "ctrl_wheel_active"
        } else if (root.isDragActive) {
            shouldShow = true
            reason = "drag_active"
        }
        // Priority 3: MANUAL (low priority - manual toggle)
        else if (root.isManualToggle) {
            shouldShow = true
            reason = "manual_toggle"
        }
        
        root.showGridLines = shouldShow
        console.log("🎯 [GRID_LINES] Visibility:", shouldShow ? "SHOW" : "HIDE", "- Reason:", reason)
    }

    // ✅ FORCE_HIDE FUNCTIONS: Highest priority states
    function setFullscreenState(active) {
        console.log("🎯 [GRID_LINES] Fullscreen state:", active ? "ACTIVE" : "INACTIVE")
        root.hasFullscreenItem = active
        if (active) {
            // Clear all temporary states when entering fullscreen
            stopAllTimers()
            resetTemporaryStates()
            clearManualToggle()
            console.log("🎯 [GRID_LINES] Cleared all temporary states for fullscreen")
        }
        updateVisibility()
    }
    
    function setModalState(active) {
        console.log("🎯 [GRID_LINES] Modal state:", active ? "ACTIVE" : "INACTIVE")
        root.hasActiveModal = active
        if (active) {
            stopAllTimers()
        }
        updateVisibility()
    }
    
    function setPtzState(active) {
        console.log("🎯 [GRID_LINES] PTZ state:", active ? "ACTIVE" : "INACTIVE")
        root.hasPtzActive = active
        if (active) {
            stopAllTimers()
        }
        updateVisibility()
    }
    
    function setContextMenuState(active) {
        console.log("🎯 [GRID_LINES] Context menu state:", active ? "ACTIVE" : "INACTIVE")
        root.hasContextMenu = active
        if (active) {
            stopAllTimers()
        }
        updateVisibility()
    }

    // ✅ SHOW_TEMPORARY FUNCTIONS: Medium priority states
    function startCtrlWheelMode() {
        console.log("🎯 [GRID_LINES] Starting Ctrl+Wheel mode")
        clearManualToggle()
        root.isCtrlWheelActive = true
        updateVisibility()
        ctrlWheelHideTimer.restart()
    }

    function extendCtrlWheelMode() {
        if (!root.isCtrlWheelActive) return
        console.log("🎯 [GRID_LINES] Extending Ctrl+Wheel mode")
        ctrlWheelHideTimer.restart()
    }

    function startDragMode() {
        console.log("🎯 [GRID_LINES] Starting drag mode")
        clearManualToggle()
        root.isDragActive = true
        updateVisibility()
    }

    function endDragMode() {
        console.log("🎯 [GRID_LINES] Ending drag mode - will hide in 1s")
        dragHideTimer.restart()
    }

    // ✅ MANUAL FUNCTIONS: Low priority state
    function toggleManualMode() {
        stopAllTimers()
        resetTemporaryStates()
        root.isManualToggle = !root.isManualToggle
        updateVisibility()
        console.log("🎯 [GRID_LINES] Manual toggle:", root.isManualToggle ? "ON" : "OFF")
    }

    // ✅ UTILITY FUNCTIONS: Helper functions
    function stopAllTimers() {
        ctrlWheelHideTimer.stop()
        dragHideTimer.stop()
    }

    function resetTemporaryStates() {
        root.isCtrlWheelActive = false
        root.isDragActive = false
    }

    function clearManualToggle() {
        root.isManualToggle = false
    }

    // ✅ CONVENIENCE FUNCTIONS: For external components
    function hideForModal() { setModalState(true) }
    function showAfterModal() { setModalState(false) }
    function hideForPtz() { setPtzState(true) }
    function showAfterPtz() { setPtzState(false) }
    function hideForContextMenu() { setContextMenuState(true) }
    function showAfterContextMenu() { setContextMenuState(false) }

    // ✅ DEBUG FUNCTION: Print current state
    function debugState() {
        console.log("🎯 [GRID_LINES_DEBUG] Current state:")
        console.log("  - showGridLines:", root.showGridLines)
        console.log("  - hasFullscreenItem:", root.hasFullscreenItem)
        console.log("  - hasActiveModal:", root.hasActiveModal)
        console.log("  - hasPtzActive:", root.hasPtzActive)
        console.log("  - hasContextMenu:", root.hasContextMenu)
        console.log("  - isCtrlWheelActive:", root.isCtrlWheelActive)
        console.log("  - isDragActive:", root.isDragActive)
        console.log("  - isManualToggle:", root.isManualToggle)
    }
}
