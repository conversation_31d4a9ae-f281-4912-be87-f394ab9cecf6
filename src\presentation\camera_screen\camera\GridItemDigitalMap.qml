/**
 * GridItemCamera.qml - Specialized camera component kế thừa từ GridItemBase
 *
 * Ch<PERSON><PERSON> năng chính:
 * - <PERSON><PERSON> thừa từ GridItemBase.qml (OOP inheritance)
 * - Video stream rendering và playback thông qua FrameModel
 * - PTZ controls support (pan, tilt, zoom)
 * - Digital zoom functionality với mouse wheel
 * - Camera state indicators và connection status
 * - Camera info overlay với responsive design
 * - Rotation support với snap-to-cardinal-angles
 * - Camera-specific interactions và context menu
 *
 * Architecture:
 * - Inherits: GridItemBase properties, functions, signals
 * - Extends: Camera-specific functionality (PTZ, zoom, rotation)
 * - Overrides: Camera-specific behaviors (fullscreen, selection)
 * - Integration: FrameModel cho video rendering, ConnectionStateOverlay cho status
 */

import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import models 1.0
import "../controls"
import "../base"
import "../components"
import "."
import "../constants/ZIndexConstants.js" as ZIndex
import '../../../common/qml/map'


GridItemBase {
    id: root
    itemType: "map"

    signal cameraSelected()
    signal zoomChanged(real factor)
    signal fullscreenToggled(bool isFullscreen)

    Component.onCompleted: {
    }

    Component.onDestruction: {

    }

    onItemDataChanged: {
        if (itemData) {
        }
    }

    Connections {
        target: itemData
        function onFullscreenChanged() {
        }
    }

    onIsMaximizedChanged: {
        if (isMaximized) {
            fullscreenToggled(true)
        } else {
            resetZoom()
            fullscreenToggled(false)
        }
    }
    property real zoomFactor: 1.0
    property point zoomCenter: Qt.point(0, 0)
    property bool isZooming: false
    property bool isFocusMap: false
    function resetZoom() {
        zoomFactor = 1.0
        zoomCenter = Qt.point(root.width / 2, root.height / 2)
        isZooming = false
        zoomChanged(zoomFactor)
    }
    function selectCamera() {
        cameraSelected()
    }

    MapState{
        id: _mapState
        editMode: false
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 8
        spacing: 4

        // Video display area
        Rectangle {
            id: contentArea
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "black"

            // Placeholder when no video
            Rectangle {
                anchors.fill: parent
                color: isDarkTheme ? "#2d2d2d" : "#e5e7eb"
                radius: 4
                visible: !root.itemData
            }

            MapOnGrid {
                id: mapOnGrid
                anchors.fill: parent
                thisMapState: (function(){
                    console.log("thisMapState ",_mapState)
                    return _mapState
                })()
                thisMapModel: (function(){
                    console.log("thisMapModel ",root.itemData)
                    return root.itemData ? root.itemData.mapModel : null
                })()
                // Connections {
                //     target: root
                //     function onIsMapMoveModeChanged() {
                //         mapOnGrid.setMapMoveMode(root.isMapMoveMode)
                //     }
                // }

                // // Initialize map move mode
                // Component.onCompleted: {
                //     mapOnGrid.setMapMoveMode(root.isMapMoveMode)
                // }
            }
        }
    }
    


    Loader {
        id: buttonControlsLoader
        // ✅ CONTENT BOUNDS: Position within content bounds area
        x: root.contentBoundsX
        y: root.contentBoundsY
        width: root.contentBoundsWidth
        height: root.contentBoundsHeight
        z: ZIndex.gridItemControls
        active: shouldShowButtonControls
        asynchronous: true

        // ✅ VISIBILITY LOGIC: Show when hovered/selected but hide during fullscreen animations
        property bool shouldShowButtonControls: {
            // Hide during fullscreen transitions (like border canvas)
            if (root.itemData && root.itemData.isAnimating) return false
            // Show when hovered or selected (even in fullscreen)
            return root.isHovered || root.isSelected || root.isFocusMap
        }

        onActiveChanged: {
        }

        sourceComponent: Component {
            GridItemButtonControls {
                gridItem: root
                itemType: "map"
                anchors.fill: parent

                // ✅ CONTENT BOUNDS: Already positioned within content bounds by loader
                contentBoundsX: 0
                contentBoundsY: 0
                contentBoundsWidth: parent.width
                contentBoundsHeight: parent.height

                onCloseButtonClicked: function(item) {
                    if (root.gridModel) {
                        root.gridModel.isSave = false
                        root.gridModel.removeItemAt(root.gridRow, root.gridCol)
                    }
                }

                onMaximizeButtonClicked: function(item) {
                    // ✅ CENTRALIZED: Use reusable fullscreen handler
                    if (root.itemData && root.animationManager) {
                        var targetState = !root.itemData.fullscreen
                        root.animationManager.handleFullscreenTransition(
                            root, targetState, "DIGITAL_MAP_MAXIMIZE"
                        )
                    }
                }

                onChangeFocused: function(){
                    root.isFocusMap = !root.isFocusMap
                    controlHandler.visible = !root.isFocusMap
                    _mapState.lockMode = !root.isFocusMap
                }
            }
        }
    }
    // ConnectionStateOverlay {
    //     id: connectionOverlay
    //     itemData: root.itemData
    //     isDarkTheme: root.isDarkTheme
    //     baseZIndex: root.itemData ? root.itemData.zIndex + 5 : 20
    // }

}
