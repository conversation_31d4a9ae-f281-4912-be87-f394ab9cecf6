#!/bin/sh
pyside6-lupdate VMS.py src/common/widget/camera_widget.py src/common/widget/custom_button_icon.py src/common/widget/custom_calendar.py src/common/widget/custom_change_mode.py src/common/widget/custom_filter.py src/common/widget/custom_side_menu.py src/common/widget/event_bar.py src/common/widget/image_widget.py src/common/widget/preset_listview.py src/common/widget/ptz_widget.py src/common/widget/stack_camera_frame.py src/common/widget/tree_view_widget.py src/common/widget/warning_alert_camera_widget.py src/common/widget/custom_tab_widget/new_custom_tab_widget.py src/common/widget/custom_titlebar/actionable_title_bar.py src/common/widget/custom_titlebar/button_titlebar.py src/common/widget/custom_titlebar/custom_titlebar_with_tab.py src/common/widget/custom_titlebar/custom_component/login_title_bar.py src/common/widget/custom_titlebar/custom_component/widget_button_system.py src/common/widget/custom_titlebar/custom_component/widget_search_title.py src/common/widget/custom_titlebar/custom_component/widget_tab_title.py src/common/widget/dialogs/camera_info_dialog.py src/common/widget/dialogs/add_camera_dialog.py src/common/widget/dialogs/add_group_dialog.py src/common/widget/dialogs/warning_dialog.py src/common/qml/models/device_controller.py src/common/widget/dialogs/base_dialog.py src/common/widget/dialogs/custom_tree_view_for_dialogs.py src/common/widget/dialogs/dialogs.py src/common/widget/dialogs/footer_widget.py src/common/widget/menus/custom_menus.py src/common/widget/search_widget/search_bar.py src/common/widget/tab_widget/tabWidget.py src/common/widget/toggle/custom_toggle.py src/common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py src/common/widget/widget_for_custom_grid/grid_editable_widget.py src/common/widget/widget_for_custom_grid/list_item_grid_custom.py src/presentation/auth_screen/login_screen.py src/presentation/camera_screen/camera_bottom_toolbar.py src/presentation/camera_screen/camera_grid_widget.py src/presentation/camera_screen/camera_screen.py src/presentation/camera_screen/main_tree_view_widget.py src/presentation/device_management_screen/device_screen.py src/presentation/device_management_screen/widget/ai_camera_zone_widget.py src/presentation/device_management_screen/widget/ai_item_script_widget.py src/presentation/device_management_screen/widget/ai_zone_dropdown_draw.py src/presentation/device_management_screen/widget/content_add_script_ai_dialog.py src/presentation/device_management_screen/widget/list_custom_widgets.py src/presentation/device_management_screen/widget/multidropdown.py src/presentation/device_management_screen/widget/tableview_base.py src/presentation/map_screen/map_screen.py src/presentation/setting_screen/setting_screen.py src/presentation/setting_screen/widget/general_setting_tab.py src/presentation/setting_screen/widget/widget_alert_tab.py src/common/widget/event/event_widget.py src/presentation/device_management_screen/widget/ai_zone_dropdown_draw.py src/presentation/user_permissions_screen/user_permissions_screen.py src/presentation/user_permissions_screen/widgets/user_group_tableview.py src/presentation/user_permissions_screen/widgets/users_tableview.py src/common/widget/dialogs/dialogs_permission_screen.py src/common/widget/dialogs/child_widgets/widget_config_list_users.py src/common/widget/dialogs/child_widgets/widget_config_permissions.py src/presentation/server_screen/server_item.py src/presentation/main_screen/main_screen.py src/common/widget/dialogs/filter_event_dialog.py src/common/widget/event/calendar_combobox.py src/common/widget/custom_calendar.py src/presentation/server_screen/login_dialog.py src/common/qml/device_table/DeviceTable.qml src/common/qml/device_table/DeviceGroupTable.qml src/common/widget/notifications/listen_message_notifications.py src/common/widget/widget_for_custom_grid/map_grid_item_widget.py src/common/qml/models/frame_model.py src/common/qml/models/recording_schedule.py src/presentation/setting_screen/widget/tracking_setting_tab.py src/common/qml/videoplayback/ScheduleUI.qml src/common/qml/map/ConfigCameraFovDialog.qml src/common/qml/map/MapItem.qml src/common/qml/map/Map2DCameraItem.qml src/common/qml/map/SelectTabWidget.qml src/common/qml/map/PreviewItem.qml src/presentation/camera_screen/map/map_widget_item.py src/common/widget/dialogs/add_floor_dialog.py src/presentation/camera_screen/managers/grid_manager.py src/common/qml/videoplayback/Search.qml src/common/qml/map/CustomVideoOutput.qml src/common/qml/videoplayback/SelectCamerasDialog.qml -ts src/languages/qt_en_US.ts
pyside6-lupdate VMS.py src/common/widget/camera_widget.py src/common/widget/custom_button_icon.py src/common/widget/custom_calendar.py src/common/widget/custom_change_mode.py src/common/widget/custom_filter.py src/common/widget/custom_side_menu.py src/common/widget/event_bar.py src/common/widget/image_widget.py src/common/widget/preset_listview.py src/common/widget/ptz_widget.py src/common/widget/stack_camera_frame.py src/common/widget/tree_view_widget.py src/common/widget/warning_alert_camera_widget.py src/common/widget/custom_tab_widget/new_custom_tab_widget.py src/common/widget/custom_titlebar/actionable_title_bar.py src/common/widget/custom_titlebar/button_titlebar.py src/common/widget/custom_titlebar/custom_titlebar_with_tab.py src/common/widget/custom_titlebar/custom_component/login_title_bar.py src/common/widget/custom_titlebar/custom_component/widget_button_system.py src/common/widget/custom_titlebar/custom_component/widget_search_title.py src/common/widget/custom_titlebar/custom_component/widget_tab_title.py src/common/widget/dialogs/camera_info_dialog.py src/common/widget/dialogs/add_camera_dialog.py src/common/widget/dialogs/add_group_dialog.py src/common/widget/dialogs/warning_dialog.py src/common/qml/models/device_controller.py src/common/widget/dialogs/base_dialog.py src/common/widget/dialogs/custom_tree_view_for_dialogs.py src/common/widget/dialogs/dialogs.py src/common/widget/dialogs/footer_widget.py src/common/widget/menus/custom_menus.py src/common/widget/search_widget/search_bar.py src/common/widget/tab_widget/tabWidget.py src/common/widget/toggle/custom_toggle.py src/common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py src/common/widget/widget_for_custom_grid/grid_editable_widget.py src/common/widget/widget_for_custom_grid/list_item_grid_custom.py src/presentation/auth_screen/login_screen.py src/presentation/camera_screen/camera_bottom_toolbar.py src/presentation/camera_screen/camera_grid_widget.py src/presentation/camera_screen/camera_screen.py src/presentation/camera_screen/main_tree_view_widget.py src/presentation/device_management_screen/device_screen.py src/presentation/device_management_screen/widget/ai_camera_zone_widget.py src/presentation/device_management_screen/widget/ai_item_script_widget.py src/presentation/device_management_screen/widget/ai_zone_dropdown_draw.py src/presentation/device_management_screen/widget/content_add_script_ai_dialog.py src/presentation/device_management_screen/widget/list_custom_widgets.py src/presentation/device_management_screen/widget/multidropdown.py src/presentation/device_management_screen/widget/tableview_base.py src/presentation/map_screen/map_screen.py src/presentation/setting_screen/setting_screen.py src/presentation/setting_screen/widget/general_setting_tab.py src/presentation/setting_screen/widget/widget_alert_tab.py src/common/widget/event/event_widget.py src/presentation/device_management_screen/widget/ai_zone_dropdown_draw.py src/presentation/user_permissions_screen/user_permissions_screen.py src/presentation/user_permissions_screen/widgets/user_group_tableview.py src/presentation/user_permissions_screen/widgets/users_tableview.py src/common/widget/dialogs/dialogs_permission_screen.py src/common/widget/dialogs/child_widgets/widget_config_list_users.py src/common/widget/dialogs/child_widgets/widget_config_permissions.py src/presentation/server_screen/server_item.py src/presentation/main_screen/main_screen.py src/common/widget/dialogs/filter_event_dialog.py src/common/widget/event/calendar_combobox.py src/common/widget/custom_calendar.py src/presentation/server_screen/login_dialog.py src/common/qml/device_table/DeviceTable.qml src/common/qml/device_table/DeviceGroupTable.qml src/common/widget/notifications/listen_message_notifications.py src/common/widget/widget_for_custom_grid/map_grid_item_widget.py src/common/qml/models/frame_model.py src/common/qml/models/recording_schedule.py src/presentation/setting_screen/widget/tracking_setting_tab.py src/common/qml/videoplayback/ScheduleUI.qml src/common/qml/map/ConfigCameraFovDialog.qml src/common/qml/map/MapItem.qml src/common/qml/map/Map2DCameraItem.qml src/common/qml/map/SelectTabWidget.qml src/common/qml/map/PreviewItem.qml src/presentation/camera_screen/map/map_widget_item.py src/common/widget/dialogs/add_floor_dialog.py src/presentation/camera_screen/managers/grid_manager.py src/common/qml/videoplayback/Search.qml src/common/qml/map/CustomVideoOutput.qml src/common/qml/videoplayback/SelectCamerasDialog.qml -ts src/languages/qt_vi_VN.ts
pyside6-lupdate VMS.py src/common/widget/camera_widget.py src/common/widget/custom_button_icon.py src/common/widget/custom_calendar.py src/common/widget/custom_change_mode.py src/common/widget/custom_filter.py src/common/widget/custom_side_menu.py src/common/widget/event_bar.py src/common/widget/image_widget.py src/common/widget/preset_listview.py src/common/widget/ptz_widget.py src/common/widget/stack_camera_frame.py src/common/widget/tree_view_widget.py src/common/widget/warning_alert_camera_widget.py src/common/widget/custom_tab_widget/new_custom_tab_widget.py src/common/widget/custom_titlebar/actionable_title_bar.py src/common/widget/custom_titlebar/button_titlebar.py src/common/widget/custom_titlebar/custom_titlebar_with_tab.py src/common/widget/custom_titlebar/custom_component/login_title_bar.py src/common/widget/custom_titlebar/custom_component/widget_button_system.py src/common/widget/custom_titlebar/custom_component/widget_search_title.py src/common/widget/custom_titlebar/custom_component/widget_tab_title.py src/common/widget/dialogs/camera_info_dialog.py src/common/widget/dialogs/add_camera_dialog.py src/common/widget/dialogs/add_group_dialog.py src/common/widget/dialogs/warning_dialog.py src/common/qml/models/device_controller.py src/common/widget/dialogs/base_dialog.py src/common/widget/dialogs/custom_tree_view_for_dialogs.py src/common/widget/dialogs/dialogs.py src/common/widget/dialogs/footer_widget.py src/common/widget/menus/custom_menus.py src/common/widget/search_widget/search_bar.py src/common/widget/tab_widget/tabWidget.py src/common/widget/toggle/custom_toggle.py src/common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py src/common/widget/widget_for_custom_grid/grid_editable_widget.py src/common/widget/widget_for_custom_grid/list_item_grid_custom.py src/presentation/auth_screen/login_screen.py src/presentation/camera_screen/camera_bottom_toolbar.py src/presentation/camera_screen/camera_grid_widget.py src/presentation/camera_screen/camera_screen.py src/presentation/camera_screen/main_tree_view_widget.py src/presentation/device_management_screen/device_screen.py src/presentation/device_management_screen/widget/ai_camera_zone_widget.py src/presentation/device_management_screen/widget/ai_item_script_widget.py src/presentation/device_management_screen/widget/ai_zone_dropdown_draw.py src/presentation/device_management_screen/widget/content_add_script_ai_dialog.py src/presentation/device_management_screen/widget/list_custom_widgets.py src/presentation/device_management_screen/widget/multidropdown.py src/presentation/device_management_screen/widget/tableview_base.py src/presentation/map_screen/map_screen.py src/presentation/setting_screen/setting_screen.py src/presentation/setting_screen/widget/general_setting_tab.py src/presentation/setting_screen/widget/widget_alert_tab.py src/common/widget/event/event_widget.py src/presentation/device_management_screen/widget/ai_zone_dropdown_draw.py src/presentation/user_permissions_screen/user_permissions_screen.py src/presentation/user_permissions_screen/widgets/user_group_tableview.py src/presentation/user_permissions_screen/widgets/users_tableview.py src/common/widget/dialogs/dialogs_permission_screen.py src/common/widget/dialogs/child_widgets/widget_config_list_users.py src/common/widget/dialogs/child_widgets/widget_config_permissions.py src/presentation/server_screen/server_item.py src/presentation/main_screen/main_screen.py src/common/widget/dialogs/filter_event_dialog.py src/common/widget/event/calendar_combobox.py src/common/widget/custom_calendar.py src/presentation/server_screen/login_dialog.py src/common/qml/device_table/DeviceTable.qml src/common/qml/device_table/DeviceGroupTable.qml src/common/widget/notifications/listen_message_notifications.py src/common/widget/widget_for_custom_grid/map_grid_item_widget.py src/common/qml/models/frame_model.py src/common/qml/models/recording_schedule.py src/presentation/setting_screen/widget/tracking_setting_tab.py src/common/qml/videoplayback/ScheduleUI.qml src/common/qml/map/ConfigCameraFovDialog.qml src/common/qml/map/MapItem.qml src/common/qml/map/Map2DCameraItem.qml src/common/qml/map/SelectTabWidget.qml src/common/qml/map/PreviewItem.qml src/presentation/camera_screen/map/map_widget_item.py src/common/widget/dialogs/add_floor_dialog.py src/presentation/camera_screen/managers/grid_manager.py src/common/qml/videoplayback/Search.qml src/common/qml/map/CustomVideoOutput.qml src/common/qml/videoplayback/SelectCamerasDialog.qml -ts src/languages/qt_ru_RU.ts

pyside6-lrelease src/languages/qt_en_US.ts
pyside6-lrelease src/languages/qt_vi_VN.ts
pyside6-lrelease src/languages/qt_ru_RU.ts
pyside6-rcc resource.qrc -o resources_rc.py

python VMS.py