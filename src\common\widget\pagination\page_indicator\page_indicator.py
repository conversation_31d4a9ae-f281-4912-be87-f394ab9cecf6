from PySide6.QtWidgets import (QWidget, QVBoxLayout, QPushButton, QHBoxLayout, QLineEdit, QSizePolicy)
from PySide6.QtCore import Qt, Signal

from src.common.controller.main_controller import main_controller

class Pagination(QWidget):
    signal_update_table = Signal(tuple)  # (current_page, rows_per_page)

    def __init__(self, total_rows=None, rows_per_page=10):
        super().__init__()
        self.total_rows = total_rows
        self.rows_per_page = rows_per_page
        self.current_page = 1
        self.total_pages = None
        # self.total_pages = (self.total_rows + self.rows_per_page - 1) // self.rows_per_page
        self.load_ui()
        self.set_dynamic_stylesheet()

    def load_ui(self):
        # Layouts
        self.setStyleSheet("border: none;")
        self.main_layout = QVBoxLayout(self)
        self.pagination_layout = QHBoxLayout()
        self.pagination_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.pagination_layout.setSpacing(2)
        self.widget_indicator_numer = QWidget()
        self.widget_indicator_numer.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        # self.widget_indicator_numer.setFixedWidth(180)
        self.layout_indicator_numer = QHBoxLayout(self.widget_indicator_numer)
        self.layout_indicator_numer.setSpacing(2)
        self.layout_indicator_numer.setContentsMargins(0, 0, 0, 0)
        self.layout_indicator_numer.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # # Style sheet
        self.btn_active = f'''
            QPushButton{{
                background-color: #5B5B9F;
                color: white;
                border-radius: 4px;
            }}
        '''
        self.btn_deactive = f'''
            QPushButton{{
                background-color: #2B2A3A;
                color: #656475;
                border-radius: 4px;
            }}
        '''

        # Pagination Controls
        self.first_page_button = QPushButton("<<")
        self.first_page_button.setFixedSize(24, 24)
        self.last_page_button = QPushButton(">>")
        self.last_page_button.setFixedSize(24, 24)
        self.prev_button = QPushButton("<")
        self.prev_button.setFixedSize(24, 24)
        self.next_button = QPushButton(">")
        self.next_button.setFixedSize(24, 24)
        self.page_input = QLineEdit()
        self.page_input.setFixedWidth(30)
        self.page_input.setText(str(self.current_page))

        # Page number buttons
        self.page_buttons = []

        # Setup Stylesheet
        self.first_page_button.setStyleSheet(self.btn_deactive)
        self.last_page_button.setStyleSheet(self.btn_deactive)
        self.prev_button.setStyleSheet(self.btn_deactive)
        self.next_button.setStyleSheet(self.btn_deactive)

        # Setup UI
        self.setup_pagination()
        self.main_layout.addLayout(self.pagination_layout)

        # Connect events
        self.prev_button.clicked.connect(self.prev_page)
        self.next_button.clicked.connect(self.next_page)
        self.first_page_button.clicked.connect(self.go_to_first_page)
        self.last_page_button.clicked.connect(self.go_to_last_page)
        self.page_input.returnPressed.connect(self.jump_to_page)

        if self.total_pages is not None:
            # Show the first page
            self.update_table()

    def set_total_rows_and_total_pages(self, total_rows):
        if total_rows == 0:
            self.total_rows = 1
        else:
            self.total_rows = total_rows
        self.total_pages = (self.total_rows + self.rows_per_page - 1) // self.rows_per_page
        self.generate_page_buttons()

    def setup_pagination(self):
        """Setup pagination layout."""
        self.pagination_layout.addWidget(QWidget(), 4)
        layout_test = QHBoxLayout()
        layout_test.setContentsMargins(0, 0, 0, 0)
        layout_test.setSpacing(2)
        layout_test.setAlignment(Qt.AlignmentFlag.AlignRight)
        layout_test.addWidget(self.first_page_button)
        layout_test.addWidget(self.prev_button)
        self.pagination_layout.addLayout(layout_test, 1)

        # Page buttons dynamically
        # self.generate_page_buttons()
        self.pagination_layout.addWidget(self.widget_indicator_numer, 1)
        layout_test2 = QHBoxLayout()
        layout_test2.setContentsMargins(0, 0, 0, 0)
        layout_test2.setSpacing(2)
        layout_test2.setAlignment(Qt.AlignmentFlag.AlignLeft)
        layout_test2.addWidget(self.next_button)
        layout_test2.addWidget(self.last_page_button)
        self.pagination_layout.addLayout(layout_test2, 1)

        self.pagination_layout.addWidget(QWidget(), 4)
        # self.pagination_layout.addWidget(self.page_input)

    def generate_page_buttons(self):
        """Dynamically create page number buttons for large datasets."""
        for btn_indicator in self.page_buttons:
            btn_indicator.deleteLater()

        self.page_buttons.clear()

        # Always show 7 items: "< 1 ... 5 6 7 ... 20 >"
        max_visible_pages = 7

        if self.total_pages <= max_visible_pages:
            # Show all pages if there are less than or equal to 7 pages
            pages = range(1, self.total_pages + 1)
        else:
            # Ensure 7 items always shown
            if self.current_page <= 4:
                # Near the beginning, show first 5 pages, then "..." and last page
                pages = list(range(1, 6)) + ["..."] + [self.total_pages]
            elif self.current_page >= self.total_pages - 3:
                # Near the end, show first page, then "...", then last 5 pages
                pages = [1, "..."] + list(range(self.total_pages - 4, self.total_pages + 1))
            else:
                # In the middle, show first page, "...", current page and neighbors, "...", and last page
                pages = [1, "..."] + list(range(self.current_page - 1, self.current_page + 2)) + ["..."] + [
                    self.total_pages]
        for page in pages:
            if page == "...":
                btn_undefined = QPushButton("...")
                btn_undefined.setFixedSize(24, 24)
                btn_undefined.setStyleSheet(self.btn_deactive)
                self.page_buttons.append(btn_undefined)
                self.layout_indicator_numer.addWidget(btn_undefined)
            else:
                btn_indicator = QPushButton(str(page))
                btn_indicator.setFixedSize(24, 24)
                btn_indicator.clicked.connect(self.page_button_clicked)
                if btn_indicator.text() == str(self.current_page):
                    btn_indicator.setStyleSheet(self.btn_active)
                else:
                    btn_indicator.setStyleSheet(self.btn_deactive)
                self.page_buttons.append(btn_indicator)
                self.layout_indicator_numer.addWidget(btn_indicator)

    def page_button_clicked(self):
        """Handle page button clicks."""
        page = int(self.sender().text())
        self.current_page = page
        self.update_table()

    def jump_to_page(self):
        """Jump to the page entered by the user."""
        page = int(self.page_input.text())
        if 1 <= page <= self.total_pages:
            self.current_page = page
            self.update_table()

    def prev_page(self):
        """Go to the previous page."""
        if self.current_page > 1:
            self.current_page -= 1
            self.update_table()

    def next_page(self):
        """Go to the next page."""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.update_table()

    def go_to_first_page(self):
        """Go to the first page."""
        self.current_page = 1
        self.update_table()

    def go_to_last_page(self):
        """Go to the last page."""
        self.current_page = self.total_pages
        self.update_table()

    def update_table(self):
        # Update page number buttons
        self.generate_page_buttons()
        # Update page input field
        self.page_input.setText(str(self.current_page))
        self.signal_update_table.emit((self.current_page, self.rows_per_page))

    def set_dynamic_stylesheet(self):
        # Style sheet
        self.btn_active = f'''
            QPushButton{{
                background-color: {main_controller.get_theme_attribute("Color", "primary", 0.2)};
                color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                border-radius: 4px;
                border: 1px solid {main_controller.get_theme_attribute("Color", "primary")};
                font-weight: bold;
            }}
        '''
        self.btn_deactive = f'''
            QPushButton{{
                background-color: {main_controller.get_theme_attribute("Color", "filter_background")};
                color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                border-radius: 4px;
                border: None;
                font-weight: bold;
            }}
        '''

        self.first_page_button.setStyleSheet(self.btn_deactive)
        self.last_page_button.setStyleSheet(self.btn_deactive)
        self.prev_button.setStyleSheet(self.btn_deactive)
        self.next_button.setStyleSheet(self.btn_deactive)

        if self.total_pages is not None:
            # Show the first page
            self.update_table()

# if __name__ == "__main__":
#     app = QApplication(sys.argv)
#     window = Pagination(total_rows=200, rows_per_page=10)
#     window.show()
#     sys.exit(app.exec())
