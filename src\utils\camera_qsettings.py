import src.utils.log_utils as LogUtils
import logging

from src.common.model.item_grid_model import ItemGridModel
from src.styles.style import Style

logger = logging.getLogger(__name__)
from PySide6.QtCore import QSettings,Qt
import json
import math
from src.common.widget.button_state import ButtonState, GridButtonModel,original_list_data_grid
from src.common.model.device_models import TabType
import copy

class DeleteType:
    AllTabIndex = "AllTabIndex"
    TabIndex = "TabIndex"
    TabName = "TabName"
    AllCamera =  "AllCamera"
    OneCamera = "OneCamera"

class UpdateType:
    Invalid = 'Invalid'
    GroupChange = 'GroupChange' 
    PositionChange = 'PositionChange' 
    GridChange = 'GridChange'
    NameChange = 'NameChange'

class Camera_Qsettings:
    __instance = None

    def __init__(self):
        self.settings = QSettings("GPS", "Camera Settings")
        # self.get_camera_optimize()
        # self.settings.remove("map")
        # self.settings.remove("building")
        # self.settings.remove("floor")
        # self.settings.remove("camera")
        # self.settings.remove("shortcut_ids")
        # self.settings.remove("servers")
        # self.settings.remove("screens")
        self.json_original_list_data_grid = [item.to_dict() for item in original_list_data_grid]
        self.servers = {}
        self.json_status_tab = {}
        self.json_shortcut_ids = {}
        self.json_patrols = []
        self.json_cameras = []
        self.json_floors = []
        self.json_buildings = []
        self.json_maps = []
        self.json_screens = {}
        self.get_servers()
        self.get_status_tab()
        self.get_shortcut_ids()
        self.get_cameras()
        self.getFloors()
        self.getBuildings()
        self.get_maps()
        self.get_screens()

    @staticmethod
    def get_instance():
        if Camera_Qsettings.__instance is None:
            Camera_Qsettings.__instance = Camera_Qsettings()
        return Camera_Qsettings.__instance
    
    def get_servers(self):
        if self.settings.contains("servers"):
            json_string = self.settings.value("servers")
            self.json_servers = json.loads(json_string)
        else:
            self.json_servers = {}
        # logger.debug(f"get_servers = {self.json_servers}")
        return self.json_servers 
    
    def set_server(self,json_server= None):
        # logger.debug(f'json_status_tab = {json_status_tab}')
        self.json_servers[str(json_server['id'])] = json_server
        new_json_string = json.dumps(self.json_servers)
        self.settings.setValue("servers", new_json_string)  

    def delete_server(self,json_server):
        temp =  self.json_servers.get(str(json_server['id']),None)
        if temp is not None:
            del self.json_servers[str(json_server['id'])]
        new_json_string = json.dumps(self.json_servers)
        self.settings.setValue("servers", new_json_string)  


    def create_camera(self):
        if self.settings.contains("camera"):
            json_string = self.settings.value("camera")
            self.json_cameras = json.loads(json_string)
        else:
            
            self.json_cameras = {}
            
    def set_camera(self,json_cameras = None):
        self.json_cameras = json_cameras

        new_json_string = json.dumps(self.json_cameras)
        self.settings.setValue("camera", new_json_string)  

    def get_cameras(self):
        if self.settings.contains("camera"):
            json_string = self.settings.value("camera")
            self.json_cameras = json.loads(json_string)
        else:
            # Trích tạm khởi tạo map khi người dùng mới đăng nhập app mà chưa có dữ liệu map
            self.json_cameras = {}

        return self.json_cameras
    

    def create_floor(self):
        if self.settings.contains("floor"):
            json_string = self.settings.value("floor")
            self.json_floors = json.loads(json_string)
        else:
            
            self.json_floors = {}

    def set_floor(self,json_floors = None):
        self.json_floors = json_floors

        new_json_string = json.dumps(self.json_floors)
        self.settings.setValue("floor", new_json_string)  

    def getFloors(self):
        if self.settings.contains("floor"):
            json_string = self.settings.value("floor")
            self.json_floors = json.loads(json_string)
        else:
            # Trích tạm khởi tạo map khi người dùng mới đăng nhập app mà chưa có dữ liệu map
            self.json_floors = {}

        return self.json_floors
    
    def add_camera_to_floor(self,floor_id = None, camera_data = None):
        for server_ip,item in self.json_floors.items():
            if floor_id in item:
                item[floor_id]["listCameras"].append(camera_data)

        new_json_string = json.dumps(self.json_floors)
        self.settings.setValue("floor", new_json_string)

    def replace_camera_list_in_floor(self,floor_id = None, camera_data = None):
        for server_ip,item in self.json_floors.items():
            if floor_id in item:
                item[floor_id]["listCameras"] = camera_data

        new_json_string = json.dumps(self.json_floors)
        self.settings.setValue("floor", new_json_string)

    def create_screen(self):
        if self.settings.contains("screens"):
            json_string = self.settings.value("screens")
            self.json_screens = json.loads(json_string)
        else:
            self.json_screens = {}

    def get_screens(self):
        if self.settings.contains("screens"):
            json_string = self.settings.value("screens")
            self.json_screens = json.loads(json_string)
        else:
            # Trích tạm khởi tạo map khi người dùng mới đăng nhập app mà chưa có dữ liệu map
            self.json_screens = {}

        return self.json_screens   
    
    def set_screen(self,json_screens = None):
        self.json_screens = json_screens
        new_json_string = json.dumps(self.json_screens)
        self.settings.setValue("screens", new_json_string)  

    def create_building(self):
        if self.settings.contains("building"):
            json_string = self.settings.value("building")
            self.json_buildings = json.loads(json_string)
        else:
            self.json_buildings = {}

    def getBuildings(self):
        if self.settings.contains("building"):
            json_string = self.settings.value("building")
            self.json_buildings = json.loads(json_string)
        else:
            # Trích tạm khởi tạo map khi người dùng mới đăng nhập app mà chưa có dữ liệu map
            self.json_buildings = {}

        return self.json_buildings   
    
    def set_building(self,json_buildings = None):
        self.json_buildings = json_buildings

        new_json_string = json.dumps(self.json_buildings)
        self.settings.setValue("building", new_json_string)  

    def create_map(self):
        if self.settings.contains("map"):
            json_string = self.settings.value("map")
            self.json_maps = json.loads(json_string)
        else:
            
            self.json_maps = {}

    def get_maps(self):
        if self.settings.contains("map"):
            json_string = self.settings.value("map")
            self.json_maps = json.loads(json_string)
        else:
            self.json_maps = {}
        return self.json_maps  
    
    def set_map(self,json_maps = None):
        self.json_maps = json_maps
        # for key, item in json_maps.items():
        #     if key == str(Qt.Key.Key_Alt):
        #         self.json_maps[key] = item.copy()
        #         self.json_maps[key]['func_start_key'] = None
        #     else:
        #         self.json_maps[key] = item.copy()
        #         self.json_maps[key]['func'] = None
        new_json_string = json.dumps(self.json_maps)
        self.settings.setValue("map", new_json_string)  

    def create_shortcut_ids(self):
        if self.settings.contains("shortcut_ids"):
            json_string = self.settings.value("shortcut_ids")
            self.json_shortcut_ids = json.loads(json_string)
        else:
            
            self.json_shortcut_ids = {}

    def get_shortcut_ids(self):
        if self.settings.contains("shortcut_ids"):
            json_string = self.settings.value("shortcut_ids")
            self.json_shortcut_ids = json.loads(json_string)
        else:
            self.json_shortcut_ids = {}
        return self.json_shortcut_ids   
    
    def set_shortcut_ids(self,json_shortcut_ids = None):
        self.json_shortcut_ids = {}
        for key, item in json_shortcut_ids.items():
            if key == str(Qt.Key.Key_Alt):
                self.json_shortcut_ids[key] = item.copy()
                self.json_shortcut_ids[key]['func_start_key'] = None
            else:
                self.json_shortcut_ids[key] = item.copy()
                self.json_shortcut_ids[key]['func'] = None
        new_json_string = json.dumps(self.json_shortcut_ids)
        self.settings.setValue("shortcut_ids", new_json_string)  
    
    def set_status_tab(self,json_status_tab= None):
        # logger.debug(f'json_status_tab = {json_status_tab}')
        self.json_status_tab[json_status_tab['id']] = json_status_tab
        new_json_string = json.dumps(self.json_status_tab)
        self.settings.setValue("status_tab", new_json_string)  
        
    def delete_status_tab(self,json_status_tab):
        temp =  self.json_status_tab.get(str(json_status_tab['id']),None)
        # logger.debug(f'delete_status_tab = {self.json_status_tab}')
        if temp is not None:
            del self.json_status_tab[str(json_status_tab['id'])]
        new_json_string = json.dumps(self.json_status_tab)
        self.settings.setValue("status_tab", new_json_string)  

    def get_status_tab(self):
        if self.settings.contains("status_tab"):
            json_string = self.settings.value("status_tab")
            self.json_status_tab = json.loads(json_string)
        else:
            self.json_status_tab = {}
        # logger.debug(f'get_status_tab = {self.json_status_tab}')
        return self.json_status_tab

    def get_patrols(self,camera_id = None):
        if self.settings.contains("patrols"):
            json_string = self.settings.value("patrols")
            self.json_patrols = json.loads(json_string)
        else:
            self.json_patrols = []
        if len(self.json_patrols) == 0:
            patrol = {'camera_id': camera_id, 'list_patrol':[{'name': 'patrol1','list_preset': None},{'name': 'patrol2','list_preset': None},{'name': 'patrol3','list_preset': None},{'name': 'patrol4','list_preset': None},{'name': 'patrol5','list_preset': None},{'name': 'patrol6','list_preset': None},{'name': 'patrol7','list_preset': None},{'name': 'patrol8','list_preset': None}]}
            self.json_patrols.append(patrol)
            new_json_string = json.dumps(self.json_patrols)
            self.settings.setValue("patrols", new_json_string)
            return patrol['list_patrol']
        else:
            temp = False
            for item in self.json_patrols:
                if item['camera_id'] == camera_id:
                    temp = True
                    return item['list_patrol']
            if not temp:
                patrol = {'camera_id': camera_id, 'list_patrol':[{'name': 'patrol1','list_preset': None},{'name': 'patrol2','list_preset': None},{'name': 'patrol3','list_preset': None},{'name': 'patrol4','list_preset': None},{'name': 'patrol5','list_preset': None},{'name': 'patrol6','list_preset': None},{'name': 'patrol7','list_preset': None},{'name': 'patrol8','list_preset': None}]}
                self.json_patrols.append(patrol)
                new_json_string = json.dumps(self.json_patrols)
                self.settings.setValue("patrols", new_json_string)
                return patrol['list_patrol']
    def add_list_preset(self,camera_id = None,index = None,list_preset = []):
        if self.settings.contains("patrols"):
            json_string = self.settings.value("patrols")
            self.json_patrols = json.loads(json_string)
            
        else:
            self.json_patrols = []

        for item in self.json_patrols:
            if camera_id == item['camera_id']:
                
                list_patrol = item['list_patrol']
                for idx, patrol in enumerate(list_patrol):
                    if index == idx + 1:
                        patrol['list_preset'] = list_preset
                        break
                #logger.debug(f'self.json_patrols = {self.json_patrols} ')
                new_json_string = json.dumps(self.json_patrols)
                self.settings.setValue("patrols", new_json_string)
                break

    def get_camera_optimize(self):
        # TungVD: Chưa sử dụng
        # if self.settings.contains("camera_optimize"):
        #     self.camera_optimize = bool(self.settings.value("camera_optimize"))
        # else:
        #     self.camera_optimize = False
        return False

    def set_camera_optimize(self, camera_optimize):
        self.settings.setValue("camera_optimize", camera_optimize)
        self.get_camera_optimize()

    def delete_local_data(self):
        self.settings.remove("list_camera_local")
        # self.settings.remove("list_config_server")
        # self.settings.remove("list_link_camera")
        self.settings.remove("list_camera_location")
        self.get_list_camera_location()
        self.get_list_camera()

    def get_list_camera(self):
        if self.settings.contains("list_camera_local"):
            json_string = self.settings.value("list_camera_local")
            self.json_list_camera = json.loads(json_string)
            # logger.debug(self.json_list_camera)
            # self.json_list_camera.clear()
            # new_json_string = json.dumps(self.json_list_camera)
            # self.settings.setValue("list_camera", new_json_string)
        else:
            self.json_list_camera = []

    def get_list_camera_location(self):
        if self.settings.contains("list_camera_location"):
            json_string = self.settings.value("list_camera_location")
            self.json_list_camera_location = json.loads(json_string)
            # logger.debug(self.json_list_camera_location)
        else:
            self.json_list_camera_location = []

    def add_list_camera_location(self, location_name=None, location_coordinate=None):
        if self.settings.contains("list_camera_location"):
            json_string = self.settings.value("list_camera_location")
            self.json_list_camera_location = json.loads(json_string)
        else:
            self.json_list_camera_location = []

        current_index = len(self.json_list_camera_location)
        item = {"location_id_local": current_index, "location_name": location_name,
                "location_coordinate": location_coordinate}
        self.json_list_camera_location.append(item)
        new_json_string = json.dumps(self.json_list_camera_location)
        self.settings.setValue("list_camera_location", new_json_string)
        return item

    def get_list_config_server(self):
        if self.settings.contains("list_config_server"):
            self.list_config_server = self.settings.value("list_config_server", defaultValue=[])
        # else:
        #     self.list_config_server = []
        # logger.debug(type(self.list_config_server))
        # for item in self.list_config_server:
        #     logger.debug(item)
        return self.list_config_server

    def get_list_link_camera(self):
        if self.settings.contains("list_link_camera"):
            self.list_link_camera = self.settings.value("list_link_camera", defaultValue=[])
        return self.list_link_camera

    def add_config_server(self, link):
        if self.settings.contains("list_config_server"):
            self.list_config_server = self.settings.value("list_config_server", defaultValue=[])
        # else:
        #     self.list_config_server = []
        if link not in self.list_config_server:
            self.list_config_server.append(link)
            self.settings.setValue("list_config_server", self.list_config_server)
        else:
            self.list_config_server.remove(link)
            self.list_config_server.append(link)
            self.settings.setValue("list_config_server", self.list_config_server)

    def add_link_camera(self, link):
        if self.settings.contains("list_link_camera"):
            self.list_link_camera = self.settings.value("list_link_camera", defaultValue=[])
        if link not in self.list_link_camera:
            self.list_link_camera.append(link)
            self.settings.setValue("list_link_camera", self.list_link_camera)

    def add_list_camera(self, camera_name=None, camera_url=None, camera_location=None, camera_location_l=None,
                        camera_ai_type=None, camera_monitoring=None):
        if self.settings.contains("list_camera_local"):
            json_string = self.settings.value("list_camera_local")
            self.json_list_camera = json.loads(json_string)
        else:
            self.json_list_camera = []

        current_index = len(self.json_list_camera)
        item = {"id": current_index, "camera_url": camera_url, "camera_name": camera_name,
                "camera_location": camera_location, "camera_location_l": camera_location_l,
                "camera_ai_type": camera_ai_type, "camera_monitoring": camera_monitoring}
        self.json_list_camera.append(item)
        new_json_string = json.dumps(self.json_list_camera)
        self.settings.setValue("list_camera_local", new_json_string)
        return item

    def delete_list_camera(self, id=None, camera_name=None, camera_url=None, camera_location=None, camera_ai_type=None,
                           camera_monitoring=True):
        if self.settings.contains("list_camera_local"):
            json_string = self.settings.value("list_camera_local")
            self.json_list_camera = json.loads(json_string)
        else:
            self.json_list_camera = []

        for i in range(len(self.json_list_camera)):
            if self.json_list_camera[i]["id"] == id:
                temp = self.json_list_camera[i]
                del self.json_list_camera[i]
                new_json_string = json.dumps(self.json_list_camera)
                self.settings.setValue("list_camera_local", new_json_string)
                return temp
        return 'delete_fail'

    def update_list_camera(self, id=None, camera_name=None, camera_url=None, camera_location=None,
                           camera_location_l=None, camera_ai_type=None, camera_monitoring=None):
        if self.settings.contains("list_camera_local"):
            json_string = self.settings.value("list_camera_local")
            self.json_list_camera = json.loads(json_string)
        else:
            self.json_list_camera = []

        for item in self.json_list_camera:
            if item["id"] == id:
                item['camera_url'] = camera_url
                item['camera_name'] = camera_name
                item['camera_location'] = camera_location
                item['camera_location_l'] = camera_location_l
                item['camera_ai_type'] = camera_ai_type
                item['camera_monitoring'] = camera_monitoring
                new_json_string = json.dumps(self.json_list_camera)
                self.settings.setValue("list_camera_local", new_json_string)
                return item
    
    def set_camera_map_list(self, camera_map_list):
        self.settings.setValue("camera_map_list", camera_map_list)

    def get_camera_map_list(self):
        if self.settings.contains("camera_map_list"):
            camera_map_list = self.settings.value("camera_map_list")
            return camera_map_list
        else:
            return ''

    def clear_all(self):
        self.settings.clear()
        self.get_status_tab()

camera_qsettings = Camera_Qsettings.get_instance()
