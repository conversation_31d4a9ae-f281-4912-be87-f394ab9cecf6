from PySide6.QtQuick import QQuickPaintedItem
from PySide6.QtCore import Property, Signal, Slot, Qt,QObject,QEvent
from PySide6.QtQml import QJSValue
from PySide6.QtGui import QPainter, QPixmap
from src.common.camera.video_player_manager import video_player_manager
from src.common.camera.player import Player,CameraState
from src.common.controller.controller_manager import controller_manager,Controller
from src.common.model.camera_model import Camera, CameraModel, camera_model_manager
from src.common.threads.sub_thread import SubThread
from src.common.qml.models.common_enum import CommonEnum
import uuid
import logging
logger = logging.getLogger(__name__)

class VideoModel(QQuickPaintedItem):
    clicked = Signal()
    doubleClicked = Signal()
    rightClicked = Signal()
    frameCountChanged = Signal(int)
    cameraStateChanged = Signal(str)
    visibilityChanged = Signal(bool)  # New signal for visibility changes
    process_player_signal = Signal(QObject)
    def __init__(self, parent=None):
        super().__init__(parent)
        self.uuid = uuid.uuid4()
        self.process_player_signal.connect(self.process_player_slot)
        self._model = None
        self._pixmap = QPixmap()
        self.player = None
        self.controller = None
        
        self._position = -1
        self._is_playing = False
        self._frame_count = 0
        self._state = self.tr("No Data")
        self._is_actually_visible = False  # Track actual visibility state
        # Enable mouse events
        self.setAcceptedMouseButtons(Qt.LeftButton | Qt.RightButton)
        self.setAcceptHoverEvents(True)
        # Install event filter for visibility tracking
        self.installEventFilter(self)

    def eventFilter(self, obj, event):
        if obj == self:
            if event.type() in [QEvent.Show, QEvent.Hide, QEvent.WindowStateChange]:
                self.check_visibility()
        return super().eventFilter(obj, event)

    def check_visibility(self):
        """Check if widget is actually visible and emit signal if changed"""
        new_visibility = self.is_actually_visible()
        if new_visibility != self._is_actually_visible:
            self._is_actually_visible = new_visibility
            self.visibilityChanged.emit(new_visibility)

    def get_is_actually_visible(self):
        """Get current visibility state"""
        return True

    def is_actually_visible(self):
        """Check if widget is actually visible to user"""
        # 1. Check if widget itself is visible
        if not self.isVisible():
            return False
            
        # 2. Check if visible to parent
        if not self.isVisibleTo(self.parentWidget()):
            return False
            
        # 3. Check if window is minimized
        if self.window().windowState() & Qt.WindowMinimized:
            return False
            
        # # 4. Check if in stacked widget and is current widget
        # if isinstance(self.parentWidget(), QStackedWidget):
        #     stacked_widget = self.parentWidget()
        #     if stacked_widget.currentWidget() != self:
        #         return False
                
        return True

    def showEvent(self, event):
        super().showEvent(event)
        self.check_visibility()

    def hideEvent(self, event):
        super().hideEvent(event)
        self.check_visibility()

    def changeEvent(self, event):
        super().changeEvent(event)
        if event.type() == QEvent.WindowStateChange:
            self.check_visibility()

    @Slot("QVariant")
    def register_player(self,model):
        if model is not None:
            if isinstance(model,CameraModel):
                logger.info(f"register_player1")
                self.controller:Controller = controller_manager.get_controller(server_ip=model.get_property("server_ip"))
                video_player_manager.register_player(self,model,CommonEnum.StreamType.MAIN_STREAM)
            elif isinstance(model, QJSValue):
                logger.info(f"register_player2")
                camera_id = model.toVariant().get("id")
                camera_model = camera_model_manager.get_camera_model(id = camera_id)
                if camera_model is not None:
                    self.controller:Controller = controller_manager.get_controller(server_ip=camera_model.get_property("server_ip"))
                    video_player_manager.register_player(self,camera_model,CommonEnum.StreamType.MAIN_STREAM)
            elif isinstance(model, str):
                logger.info(f"register_player3")
                camera_model = camera_model_manager.get_camera_model(id = model)
                if camera_model is not None:
                    self.controller:Controller = controller_manager.get_controller(server_ip=camera_model.get_property("server_ip"))
                    video_player_manager.register_player(self,camera_model,CommonEnum.StreamType.SUB_STREAM)
    @Slot()
    def unregister_player(self):
        video_player_manager.unregister_player(self)
        logger.info(f'unregister_player')     

    def paint(self, painter: QPainter):
        if self._pixmap.isNull():
            return
            
        # Scale pixmap to fit while maintaining aspect ratio
        target_rect = self.boundingRect()
        scaled_pixmap = self._pixmap.scaled(
            int(target_rect.width()),
            int(target_rect.height()),
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )
        
        # Center the pixmap
        x = (target_rect.width() - scaled_pixmap.width()) / 2
        y = (target_rect.height() - scaled_pixmap.height()) / 2
        painter.drawPixmap(x, y, scaled_pixmap)

    @Slot(QPixmap)
    def updateFrame(self, pixmap):
        self._pixmap = pixmap
        self._frame_count += 1
        self.frameCountChanged.emit(self._frame_count)
        self.update()

    def mousePressEvent(self, event):
        if event.button() == Qt.RightButton:
            self.rightClicked.emit()
        else:
            self.clicked.emit()

    def mouseDoubleClickEvent(self, event):
        self.doubleClicked.emit()

    @Property(int)
    def position(self):
        return self._position

    @position.setter
    def position(self, pos):
        print(f"CustomVideo position changed from {self._position} to {pos}")
        self._position = pos

    @Property(str,notify=cameraStateChanged)
    def state(self):
        return self._state
    
    @state.setter
    def state(self, value:str):
        if self._state != value:
            self._state = value
            self.cameraStateChanged.emit(value)


    # @Property(bool,notify=viewModeChanged)
    # def viewMode(self):
    #     return self._viewMode
    
    # @viewMode.setter
    # def viewMode(self, value: bool):
    #     if self._viewMode != value:
    #         self._viewMode = value
    #         self.viewModeChanged.emit()

    @Property("QVariant")
    def model(self):
        return self._model
    
    @model.setter
    def model(self, value):
        if self._model != value:
            self._model = value

    @Property(bool)
    def isPlaying(self):
        return self._is_playing

    @isPlaying.setter
    def isPlaying(self, playing):
        if self._is_playing != playing:
            self._is_playing = playing
            if not playing:
                self._pixmap = QPixmap()
                self._frame_count = 0
                self.update()

    @Property(int, notify=frameCountChanged)
    def frameCount(self):
        return self._frame_count
    
    def process_player_slot(self, player):
        def callback(data):
            if data is not None:
                pass

        subThread = SubThread(parent=self,target=self.process_player,args=(player,),callback=callback)
        subThread.start() 

    def process_player(self,player:Player):
        self.player = player
        self.player.connect_status = True
        # self.player.set_send_mat_frame(self.use_post_process_image and self.callback_post_process_image is not None)
        self.player.register_signal(self)
        self.player.update_resize(width = 1280, height = 720,uuid = self.uuid)
        logger.info(f'player ========== START = {self.player.stream_type}')
        if not self.player.isRunning():
            logger.info(f'player ========== START')
            if self.player.stream_type != CommonEnum.StreamType.VIDEO_STREAM:
                self.player.start_thread()

        def update_stream_url(reponse, streamIndex):
            if self.player is not None:
                data = reponse.json()
                logger.info(f'update_stream_url data = {data}')
                # Lọc chỉ các luồng có trạng thái CONNECTED or None
                connected_streams = [item for item in data if item["state"] == "CONNECTED" or item["state"] is None]
                
                if connected_streams:
                    # Đầu tiên tìm luồng phù hợp với chỉ số được yêu cầu
                    target_stream = next((stream for stream in connected_streams if stream["index"] == streamIndex), None)
                    
                    # Nếu không tìm thấy luồng phù hợp với chỉ số, sử dụng luồng kết nối đầu tiên có sẵn
                    if target_stream is None:
                        target_stream = connected_streams[0]
                        
                    self.player.on_stream_link_changed(target_stream["url"])
                    logger.info(f'update_stream_url: {target_stream["url"]}')
                else:
                    logger.info(f'update_stream_url: Không có url nào được lấy')
                        
        self.controller.get_stream_url_thread(cameraId=self.player.camera_model.get_property("id"), streamIndex=0, callback=update_stream_url)

    def share_frame_signal(self, data):
        grab, mat_frame_default, pixmap_resized = data
        logger.info(f'share_frame_signal = {grab,pixmap_resized,self.get_is_actually_visible()}')
        if grab and pixmap_resized is not None and self.get_is_actually_visible():
            # self.camera_state_signal(CameraState.started)
            pixmap_resized: QPixmap
            mat_frame_default: QPixmap
            h, w = pixmap_resized.height(), pixmap_resized.width()
            # self.set_widget_size([self.root_width, self.root_height, w, h])
            # self.updateFrame(pixmap_resized)
            try:
                self.updateFrame(pixmap_resized)
            except RuntimeError:
                logger.info("RuntimeError")

    def camera_state_signal(self, camera_state):
        logger.info(f'camera_state_signal = {camera_state}')
        if camera_state == CameraState.connecting:
            self.state = "connecting"
        elif camera_state == CameraState.stopped:
            self.state = "stopped"
        elif camera_state == CameraState.started:
            self.state = "started"
        elif camera_state == CameraState.paused:
            self.state = "paused"

        # logger.info(f'camera_state_signal = {self.state}')
