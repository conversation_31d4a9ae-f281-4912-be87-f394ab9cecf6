"""
PyAV Wrapper - <PERSON>ớ<PERSON> bọc cho các thao tác PyAV trong video streaming.
"""

import sys
import av
import cv2
import time
import logging
from av.video.reformatter import VideoReformatter
from av.codec.context import Flags, Flags2
logger = logging.getLogger(__name__)

class PyAVWrapper:
    """
    Lớ<PERSON> bọc cho các thao tác PyAV.
    Lớp này cung cấp giao diện đơn giản để làm việc với video streams
    sử dụng PyAV, với hỗ trợ tăng tốc phần cứng.
    """
    
    def __init__(self):
        """
        Khởi tạo PyAVWrapper.
        
        Args:
            use_hw_accel: C<PERSON> sử dụng tăng tốc phần cứng nếu có sẵn hay không
        """
        logger.debug("Khởi tạo PyAVWrapper")
        self.container = None
        self.stream = None
        self.decoder_ctx = None
        self.stream_info = self._create_default_stream_info()
        self._max_frame_size = 1280  # Giớ<PERSON> hạn kích thước khung hình tối đa
        self._reformatter = None  # Đối tượng VideoReformatter để thay đổi kích thước hiệu quả
        self._target_width = None  # Chiều rộng mục tiêu cho việc thay đổi kích thước
        self._target_height = None  # Chiều cao mục tiêu cho việc thay đổi kích thước
        self._target_format = 'bgr24'  # Định dạng mục tiêu cho việc thay đổi kích thước
        self._interpolation = av.video.reformatter.Interpolation.AREA  # Phương pháp nội suy mặc định
        self.is_running = True  # Biến điều khiển việc lấy khung hình

        # Error handling and decoder fallback management
        self.current_decoder_type = None  # 'hardware' or 'software'
        self.has_attempted_fallback = False  # Track if fallback was attempted
        self.consecutive_decode_errors = 0  # Count consecutive decode errors
        self.last_decode_error = None  # Store last decode error message
        self.should_force_software_decoder = False  # Force software decoder flag

    @staticmethod
    def _create_default_stream_info():
        """Tạo thông tin stream mặc định."""
        logger.debug("Tạo thông tin stream mặc định")
        return {
            'width': 0,
            'height': 0,
            'fps': 0,
            'codec': None,
            'hw_accel': None
        }

    @staticmethod
    def _get_stream_options(url):
        """
        Lấy các tùy chọn streaming mặc định dựa trên giao thức URL.
        
        Args:
            url: URL của video stream
            
        Returns:
            dict: Các tùy chọn container cho stream
        """
        options = {}
        
        if url.startswith('rtsp://'):
            options = {
                'rtsp_transport': 'tcp',
                'rtsp_flags': 'prefer_tcp',
                'stimeout': '5000000',  # Timeout 5 giây
                'rtsp_tcp_timeout': '5000000',
                'buffer_size': '10240000',  # Buffer 10MB
                'max_delay': '500000',  # Độ trễ tối đa 0.5 giây
                'analyzeduration': '100',
            }
        elif url.startswith('http://') or url.startswith('https://'):
            options = {
                'analyzeduration': '100',
                'http_transport': 'tcp',
                'http_timeout': '5000000',  # Timeout 5 giây
                'buffer_size': '10240000',  # Buffer 10MB
                'max_delay': '500000',  # Độ trễ tối đa 0.5 giây
            }
            
        return options

    def _setup_decoder_context(self, codec_context, hw_type=None):
        """
        Thiết lập ngữ cảnh decoder với các tham số chung.
        
        Args:
            codec: Codec để sử dụng
            codec_context: Ngữ cảnh codec gốc cho các tham số
            hw_type: Loại tăng tốc phần cứng (tùy chọn)
            hw_codec: Codec phần cứng (tùy chọn)
            
        Returns:
            tuple: (decoder_context, success)
        """
        logger.debug("Thiết lập ngữ cảnh decoder với hw_type=%s", hw_type)
        try:
            if hw_type:
                # Tạo hwaccel từ hw_type
                try:
                    from av.codec.hwaccel import HWAccel

                    hwaccel = HWAccel(device_type=hw_type, allow_software_fallback=False)
                    # Tạo decoder context
                    decoder_ctx = codec_context.create(codec=codec_context.codec, mode='r',hwaccel=hwaccel)
                except ImportError:
                    logger.debug("Không thể nhập HWAccel hoặc hwdevices_available, sử dụng phần mềm thay thế")
                    # Nếu không có hwaccel, sử dụng phần mềm
                    decoder_ctx = codec_context.create(codec=codec_context.codec, mode='r')
            else:
                # Tạo decoder context
                decoder_ctx = codec_context.create(codec=codec_context.codec, mode='r')
            
            decoder_ctx.thread_type = "AUTO"
            decoder_ctx.skip_frame = 'BIDIR'
            decoder_ctx.flags2 |= Flags2.fast  # Cờ FAST
            decoder_ctx.flags2 |= Flags2.no_output  # Cờ NO_OUTPUT
            decoder_ctx.flags |= Flags.low_delay  # Cờ LOW_DELAY
            decoder_ctx.flags |= Flags.psnr  # Cờ PSNR
            decoder_ctx.flags |= Flags.gray
            decoder_ctx.thread_count = 1  # Số luồng tối đa
            decoder_ctx.extradata = codec_context.extradata
            logger.debug("Thiết lập ngữ cảnh decoder thành công với %s", hw_type)
            return decoder_ctx, True
            
        except Exception as e:
            logger.debug("Không thể thiết lập ngữ cảnh decoder: %s", str(e))
            return None, False

    def _initialize_hardware_decoder(self, codec_context):
        """
        Khởi tạo decoder được tăng tốc bằng phần cứng.
        
        Args:
            codec_context: Ngữ cảnh codec gốc
            
        Returns:
            tuple: (decoder_context, hw_type)
        """
        logger.debug("Khởi tạo decoder phần cứng")
        try:
            # Kiểm tra trực tiếp các thiết bị phần cứng có sẵn
            try:
                from av.hwdevice import hwdevices_available
                available_hw_devices = hwdevices_available()
            except ImportError:
                logger.debug("Không thể nhập hwdevices_available, sử dụng phần mềm thay thế")
                available_hw_devices = None
            
            if not available_hw_devices:
                logger.debug("Không có tăng tốc phần cứng khả dụng")
                return None, None

            # Chọn thiết bị phù hợp dựa trên hệ điều hành
            # Sắp xếp thứ tự ưu tiên sử dụng để tăng tốc phần cứng
            if sys.platform == 'win32':
                preferred_devices = ['cuda', 'd3d12va', 'd3d11va', 'dxva2']
            elif sys.platform == 'darwin':
                preferred_devices = ['videotoolbox']
            else:  # Linux
                preferred_devices = ['cuda','vaapi']

            # Thử từng thiết bị trong danh sách ưu tiên cho đến khi thành công
            for hw_type in preferred_devices:
                if hw_type not in available_hw_devices:
                    continue
                    
                logger.debug("Thử sử dụng thiết bị phần cứng: %s cho codec: %s", hw_type, codec_context.name)
                
                decoder_ctx, success = self._setup_decoder_context(
                    codec_context, 
                    hw_type=hw_type,
                )
                
                if success:
                    logger.debug("Khởi tạo decoder phần cứng thành công với %s", hw_type)
                    return decoder_ctx, hw_type
                else:
                    logger.warning("Không thể khởi tạo decoder phần cứng với %s, thử thiết bị tiếp theo", hw_type)
            
            logger.debug("Đã thử tất cả thiết bị phần cứng nhưng không thành công")
            return None, None
            
        except Exception as e:
            logger.debug("Lỗi khởi tạo decoder phần cứng: %s", str(e))
            return None, None

    def _initialize_software_decoder(self, codec_context):
        """
        Khởi tạo decoder phần mềm.
        
        Args:
            codec_context: Ngữ cảnh codec gốc
            
        Returns:
            decoder_context hoặc None
        """
        logger.debug("Khởi tạo decoder phần mềm")
        try:
            decoder_ctx, success = self._setup_decoder_context(codec_context)
            if success:
                logger.debug("Khởi tạo decoder phần mềm thành công")
            else:
                logger.warning("Không thể khởi tạo decoder phần mềm")
            return decoder_ctx if success else None
        except Exception as e:
            logger.debug("Lỗi khởi tạo decoder phần mềm: %s", str(e))
            return None

    def open_stream(self, url, options=None):
        """
        Mở một video stream với fallback mechanism.

        Args:
            url: URL của video stream
            options: Các tùy chọn bổ sung cho stream
        Returns:
            bool: True nếu thành công, False nếu không
        """
        start_time = time.time()
        logger.debug("Đang mở stream từ URL: %s", url)

        try:
            # Reset error tracking for new connection
            self.consecutive_decode_errors = 0
            self.last_decode_error = None
            # Lấy các tùy chọn streaming phù hợp dựa trên URL
            if options is None:
                container_options = self._get_stream_options(url)
            else:
                # Có thể crash nếu options không hợp lệ
                container_options = options
            logger.debug("Sử dụng các tùy chọn container: %s", container_options)
            
            # Mở container với các tùy chọn phù hợp và timeout 20 giây
            self.container = av.open(url, container_options=container_options, timeout=20)
            self.stream = next(
                (s for s in self.container.streams if s.type == 'video'), None)
            codec_context = self.stream.codec_context
            # Lấy chiều rộng và chiều cao của stream
            self.width = self.stream.width
            self.height = self.stream.height
            logger.debug(f"Kích thước stream: {self.width}x{self.height}")
            
            # Initialize decoder with fallback logic
            hw_type = None
            should_use_hardware = (self.width > 5120 or self.height > 2880)

            # Force software decoder if flag is set
            if self.should_force_software_decoder:
                should_use_hardware = False
                logger.debug("Forcing software decoder")

            if should_use_hardware and sys.platform == 'win32':
                logger.debug("Attempting hardware decoder initialization")
                self.decoder_ctx, hw_type = self._initialize_hardware_decoder(codec_context)

                if self.decoder_ctx:
                    self.current_decoder_type = 'hardware'
                else:
                    logger.debug("Hardware decoder failed, falling back to software")
                    self.decoder_ctx = self._initialize_software_decoder(codec_context)
                    if self.decoder_ctx:
                        self.current_decoder_type = 'software'
                        self.has_attempted_fallback = True
            else:
                logger.debug("Using software decoder")
                self.decoder_ctx = self._initialize_software_decoder(codec_context)
                if self.decoder_ctx:
                    self.current_decoder_type = 'software'
                
            if not self.decoder_ctx:
                logger.debug("Không thể khởi tạo decoder")
                raise RuntimeError("Không thể khởi tạo decoder")
                
            # Cập nhật thông tin stream
            self.stream_info.update({
                'width': codec_context.width,
                'height': codec_context.height,
                'fps': int(self.stream.average_rate) if self.stream.average_rate is not None else 0.0,
                'codec': codec_context.name,
                'hw_accel': hw_type
            })
            
            end_time = time.time()
            duration = end_time - start_time
            logger.debug("Mở stream thành công: %s (thời gian: %.2f giây)", self.stream_info, duration)
            
            # Lưu thời gian bắt đầu để tính thời gian chờ frame đầu tiên
            self._stream_start_time = time.time()
            return True
        except Exception as e:
            self.last_decode_error = str(e)
            end_time = time.time()
            duration = end_time - start_time
            logger.debug("Không thể mở stream: %s (thời gian: %.2f giây)", str(e), duration)
            return False

    def close_stream(self):
        """Đóng video stream và dọn dẹp tài nguyên."""
        logger.debug("Đang đóng stream")
        self.is_running = False  # Đặt cờ dừng để không lấy khung hình nữa

    def close_container(self):
        """Đóng container và xóa tham chiếu."""
        if self.container:
            try:
                self.container.close()
                logger.debug("Đã đóng container")
            except Exception as e:
                logger.debug("Lỗi khi đóng container: %s", str(e))
            finally:
                self.container = None  # Xóa tham chiếu đến container
                del self.container
                self.stream = None  # Xóa tham chiếu đến stream
                del self.stream
                self.decoder_ctx = None  # Xóa tham chiếu đến decoder context
                del self.decoder_ctx
                logger.debug("Đã đóng container và xóa tham chiếu")
                # re initialize 3 variables
                self.container = None
                self.stream = None
                self.decoder_ctx = None

    def can_attempt_software_fallback(self):
        """Check if software fallback is possible"""
        return (self.current_decoder_type == 'hardware' and not self.has_attempted_fallback)

    def enable_software_decoder_mode(self):
        """Enable software decoder for next connection"""
        self.should_force_software_decoder = True
        logger.info("Software decoder mode enabled for next connection")

    def reset_decoder_error_tracking(self):
        """Reset all decoder error tracking counters"""
        self.consecutive_decode_errors = 0
        self.last_decode_error = None
        self.has_attempted_fallback = False

    def _calculate_target_dimensions(self, width=None, height=None):
        """
        Tính toán kích thước đích dựa trên tỷ lệ khung hình gốc.
        Tự động detect và convert về aspect ratio chuẩn gần nhất.

        Supported aspect ratios: 4:3, 16:9, 9:16, 3:2, 5:4, 1:1, 32:9, 2:1

        Args:
            width: Chiều rộng mong muốn cho khung hình đầu ra
            height: Chiều cao mong muốn cho khung hình đầu ra

        Returns:
            tuple: (target_width, target_height)
        """
        # Đảm bảo stream_info có thông tin kích thước
        if not hasattr(self, 'stream_info'):
            self.stream_info = {}
            if self.stream:
                self.stream_info['width'] = self.stream.width
                self.stream_info['height'] = self.stream.height

        # Nếu không có thông tin kích thước gốc hoặc không cần thay đổi kích thước
        if not self.stream_info or 'width' not in self.stream_info or 'height' not in self.stream_info:
            return width, height

        # Lấy kích thước gốc của stream
        original_width = self.stream_info.get('width', 0)
        original_height = self.stream_info.get('height', 0)

        if original_width <= 0 or original_height <= 0:
            return width, height

        # Tính tỷ lệ khung hình gốc
        original_aspect_ratio = original_width / float(original_height)

        # Định nghĩa các aspect ratio chuẩn
        standard_aspect_ratios = {
            '4:3': 4.0 / 3.0,      # ≈ 1.333 (classic TV)
            '16:9': 16.0 / 9.0,    # ≈ 1.778 (widescreen)
            '9:16': 9.0 / 16.0,    # ≈ 0.563 (vertical/portrait)
            # '3:2': 3.0 / 2.0,      # = 1.500 (photography)
            # '5:4': 5.0 / 4.0,      # = 1.250 (monitor)
            '1:1': 1.0 / 1.0,      # = 1.000 (square)
            '32:9': 32.0 / 9.0,    # ≈ 3.556 (ultrawide)
            '2:1': 2.0 / 1.0,       # = 2.000 (cinema)
            '1:2': 1.0 / 2.0,      # = 0.500 (vertical/portrait)
        }

        # Tìm aspect ratio gần nhất
        closest_ratio_name = None
        closest_distance = float('inf')

        for ratio_name, ratio_value in standard_aspect_ratios.items():
            distance = abs(original_aspect_ratio - ratio_value)
            if distance < closest_distance:
                closest_distance = distance
                closest_ratio_name = ratio_name

        # Sử dụng aspect ratio gần nhất
        aspect_ratio = standard_aspect_ratios[closest_ratio_name]

        # Log để debug
        logger.debug(f"Original aspect ratio: {original_aspect_ratio:.3f} ({original_width}x{original_height})")
        logger.debug(f"Closest standard ratio: {closest_ratio_name} ({aspect_ratio:.3f})")
        logger.debug(f"Distance: {closest_distance:.3f}")

        # Nếu không cần thay đổi kích thước
        if width is None and height is None:
            return None, None

        # Tính toán kích thước mới giữ nguyên tỷ lệ đã được chuẩn hóa
        if width is not None and height is not None:
            # Cả hai kích thước được chỉ định - duy trì tỷ lệ chuẩn
            target_ratio = width / float(height)
            if target_ratio > aspect_ratio:
                # Chiều rộng quá rộng, tính toán dựa trên chiều cao
                target_height = height
                target_width = int(height * aspect_ratio)
            else:
                # Chiều cao quá cao, tính toán dựa trên chiều rộng
                target_width = width
                target_height = int(width / aspect_ratio)
        elif width is not None:
            # Chỉ có chiều rộng được chỉ định - tính toán chiều cao
            target_width = width
            target_height = int(width / aspect_ratio)
        else:
            # Chỉ có chiều cao được chỉ định - tính toán chiều rộng
            target_height = height
            target_width = int(height * aspect_ratio)

        return target_width, target_height

    def get_frame(self, width=None, height=None, format='bgr24', interpolation=None, callback=None):
        """
        Lấy khung hình tiếp theo từ stream với enhanced error handling.

        Args:
            width: Chiều rộng mong muốn cho khung hình đầu ra
            height: Chiều cao mong muốn cho khung hình đầu ra
            format: Định dạng mong muốn cho khung hình đầu ra
            interpolation: Phương pháp nội suy để sử dụng khi thay đổi kích thước
            callback: Hàm callback để xử lý khung hình

        Returns:
            bool: True nếu exit get_frame thành công, False nếu muốn chạy lại open_stream -> cần phía bên get_frame reconnect lại
        """
        if not self.is_running:
            logger.debug("Dừng lấy khung hình do is_running=False")
            self.close_container()
            return True
            
        if not self.container or not self.decoder_ctx:
            logger.warning("Không thể lấy khung hình - container hoặc decoder chưa được khởi tạo")
            return False
        
        # Existing dimension calculation (giữ nguyên)
        target_width, target_height = self._calculate_target_dimensions(width, height)

        if target_width is not None:
            self._target_width = target_width
        if target_height is not None:
            self._target_height = target_height
        if format is not None:
            self._target_format = format
        if interpolation is not None:
            self._interpolation = interpolation

        try:
            for packet in self.container.demux(self.stream):
                if not self.is_running:
                    logger.debug("Dừng lấy khung hình trong demux loop do is_running=False")
                    self.close_container()
                    return True

                try:
                    for frame in self.decoder_ctx.decode(packet):
                        if not self.is_running:
                            logger.debug("Dừng lấy khung hình trong decode loop do is_running=False")
                            self.close_container()
                            return True

                        # Existing frame processing logic (giữ nguyên)
                        if hasattr(self, '_stream_start_time') and not hasattr(self, '_first_frame_time'):
                            self._first_frame_time = time.time()
                            time_to_first_frame = self._first_frame_time - self._stream_start_time
                            logger.debug("Thời gian từ khi mở stream đến frame đầu tiên: %.2f giây", time_to_first_frame)

                        # Existing reformat logic (giữ nguyên)
                        if (self._target_width is not None or self._target_height is not None):
                            if self._reformatter is None:
                                self._reformatter = VideoReformatter()

                            reformatted_frame = self._reformatter.reformat(
                                frame,
                                width=self._target_width,
                                height=self._target_height,
                                format=self._target_format,
                                interpolation=self._interpolation
                            )
                            frame_array = reformatted_frame.to_ndarray()
                        else:
                            frame_array = frame.to_ndarray()

                        # Call callback if provided
                        if callback is not None:
                            callback(frame_array, frame.time)
                            # Reset decode error count on successful frame processing
                            self.consecutive_decode_errors = 0
                        else:
                            logger.warning("Không có callback được cung cấp!")
                            return True
                except Exception as decode_error:
                    # Handle decode errors with fallback logic
                    self.consecutive_decode_errors += 1
                    self.last_decode_error = str(decode_error)
                    logger.warning(f"Decode error #{self.consecutive_decode_errors}: {decode_error}")

                    # Suggest software fallback if too many consecutive errors
                    if self.consecutive_decode_errors >= 5 and self.can_attempt_software_fallback():
                        logger.error("Too many consecutive decode errors, recommending software fallback")
                        self.close_container()
                        return False

                    # Continue processing other packets to recover from decode errors
                    continue

            # If demux loop ends without is_running=False, it means connection issue
            logger.info("Stream connection lost, triggering reconnect")
            self.close_container()
            return False

        except Exception as e:
            self.last_decode_error = str(e)
            logger.debug("Lỗi khi lấy khung hình: %s", str(e))
            self.close_container()
            return False

    def update_size(self, width=None, height=None, format=None):
        """
        Cập nhật kích thước và định dạng cho khung hình đầu ra.

        Args:
            width: Chiều rộng mong muốn cho khung hình đầu ra
            height: Chiều cao mong muốn cho khung hình đầu ra
            format: Định dạng mong muốn cho khung hình đầu ra
        """
        # logger.debug(f"Cập nhật kích thước: width={width}, height={height}, format={format}")

        # Tính toán kích thước mới dựa trên tỷ lệ khung hình
        target_width, target_height = self._calculate_target_dimensions(width, height)
        
        # Cập nhật kích thước mới
        if target_width is not None:
            self._target_width = target_width
        if target_height is not None:
            self._target_height = target_height

        # Cập nhật định dạng
        if format is not None:
            self._target_format = format

        # Tạo reformatter mới nếu chưa có
        if self._reformatter is None and (self._target_width is not None or self._target_height is not None):
            self._reformatter = VideoReformatter()

    def __del__(self):
        """Dọn dẹp tài nguyên khi đối tượng bị hủy."""
        logger.debug("Đang hủy instance PyAVWrapper")
        self.close_stream() 