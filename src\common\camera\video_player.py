import sys
import subprocess
import numpy as np
import time
import av
import cv2
import ctypes
import subprocess
import sys
import threading
import time
import xml.etree.ElementTree as ET
from pydantic import AnyUrl
from src.common.model.camera_model import CameraModel
from src.common.camera.vlc_instance import get_vlc_instance, get_vlc_module
from src.common.camera.player import Player,StreamCameraType,CameraState,VideoLockCB,VideoUnlockCB,VideoDisplayCB
from src.common.qml.models.common_enum import CommonEnum
import logging
logger = logging.getLogger(__name__)
logging.basicConfig()
logging.getLogger('libav').setLevel(level=logging.CRITICAL)
av.logging.set_level(av.logging.PANIC)

class VideoPlayer(Player):
    def __init__(self, camera_id=None, camera_model: CameraModel = None, stream_type=CommonEnum.StreamType.VIDEO_STREAM, height=0, width=0, uuid = ''):
        super().__init__(camera_id = camera_id,camera_model = camera_model,height = height,width = width,uuid = uuid)
        logger.debug(f'Khởi tạo Player: Camera ID={camera_id}, Stream={stream_type}')

        self.stream_type = stream_type
        self.item_uuid = None  # Will be set by video_player_manager for sharing logic
        ############################ Media Player #############################
        vlc_instance = get_vlc_instance()
        logger.debug(f'vlc_instance: {vlc_instance}')
        vlc = get_vlc_module()
        if vlc_instance is not None and vlc is not None:
            self.media_list_player = vlc_instance.media_list_player_new()
            self.media_player = self.media_list_player.get_media_player()
            self.media_player.audio_set_mute(True)
            self.media_player.video_set_mouse_input(False)
            self.media_player.video_set_key_input(False)
            self.event_manager = self.media_player.event_manager()
            self.event_manager.event_attach(vlc.EventType.MediaPlayerPositionChanged, self.position_changed)
            self.event_manager.event_attach(vlc.EventType.MediaPlayerOpening, self.media_player_opening)
            self.event_manager.event_attach(vlc.EventType.MediaPlayerBuffering, self.media_player_buffering)
            self.event_manager.event_attach(vlc.EventType.MediaPlayerPlaying, self.media_player_started)
            self.event_manager.event_attach(vlc.EventType.MediaPlayerPaused, self.media_player_paused)
            self.event_manager.event_attach(vlc.EventType.MediaPlayerStopped, self.media_player_stopped)
            self.event_manager.event_attach(vlc.EventType.MediaPlayerEncounteredError, self.media_player_error)
        else:
            logger.error("Không thể khởi tạo VLC instance")
            self.media_list_player = None
            self.media_player = None
            self.event_manager = None
        self.media_path = None
        self.media_width = None
        self.media_height = None
        self.time_start = None
        self.seek_time = None
        self.start_duration = None
        self.end_duration = None
        self.current_duration = 0
        self.timelinecontroller = None

        # Cấp phát bộ đệm cho các khung hình video
        self.frame_buffer = None
        self.original_width_video_playback = None
        self.original_height_video_playback = None
        self.frame_size = None

        self.video_lock_callback_wrapped = VideoLockCB(self.video_lock_callback)
        self.video_unlock_callback_wrapped = VideoUnlockCB(self.video_unlock_callback)
        self.video_display_callback_wrapped = VideoDisplayCB(self.video_display_callback)
        ##########################################################

        self.media_loaded = False
        self.pending_seek = None
        self.initial_position_received = False  # Thêm cờ này
        self.last_frame_time = time.time()
        self.target_fps = 10
        self._cleanup_lock = threading.Lock()  # Add thread-safe cleanup lock
        self._stop_thread = False  # To allow clean exit if needed

    def unregister_signal(self,widget = None):
        """Safely unregister widget signals and cleanup if needed"""
        super().unregister_signal(widget)
        if widget is not None:
            try:               
                # Handle widget list cleanup
                if len(self.registered_widgets) > 1:
                    for item in self.registered_widgets:
                        if item == widget:
                            self.registered_widgets.remove(item)
                            break
                elif len(self.registered_widgets) == 1:
                    logger.info(f'Last widget being unregistered - cleanup everything {self.stream_type}')
                    self.registered_widgets.remove(widget)
                    self.is_pause_video = False
                    threading.Thread(target=self.media_list_player.stop).start()
                    return True
                
                # Clear any widget-specific cached data
                if hasattr(self, 'list_resize_frame'):
                    self.list_resize_frame.pop(widget.uuid, None)
                    
            except Exception as e:
                logger.error(f'Error during widget unregistration: {e}')
                
        return False

    def stop_live(self):
        pass
    
    def stop_capture(self):
        """Safely stop capture and cleanup resources"""
        self.is_pause_video = False
        self._stop_thread = True
        threading.Thread(target=self.media_list_player.stop).start()
        logger.info(f'stop_capture: {self.stream_type}')

    ###################### Media Player #############################
    def video_lock_callback(self, opaque, planes):
        # Ensure planes[0] points to the allocated buffer
        planes[0] = ctypes.cast(ctypes.addressof(self.frame_buffer), ctypes.POINTER(ctypes.c_uint8))
        return opaque

    def video_unlock_callback(self, opaque, picture, planes):
        # Optional: Handle unlocking if needed
        pass

    def video_display_callback(self, opaque, picture):
        if self.is_pause_video:
            now = time.time()
            if now - self.last_frame_time < 1 / self.target_fps:
                return  # Bỏ qua frame nếu xử lý quá nhanh
            else:
                self.last_frame_time = now
                # Convert the frame buffer to a NumPy array
                frame_array = np.ctypeslib.as_array(self.frame_buffer, shape=(self.original_height_video_playback, self.original_width_video_playback, 3))
                frame_rgb = frame_array.reshape((self.original_height_video_playback, self.original_width_video_playback, 3))  # Reshape to image dimensions

                # resize with aspect ratio and max_width_preview_available and max_height_preview_available
                target_width, target_height = self.calculate_target_dimensions(self.original_width_video_playback, self.original_height_video_playback, self.max_width_preview_available, self.max_height_preview_available)
                i_target_width = int(target_width)
                i_target_height = int(target_height)
                frame_rgb = cv2.resize(frame_rgb, (i_target_width, i_target_height))

                # Convert RGB to BGR for OpenCV display
                frame_bgr = cv2.cvtColor(frame_rgb, cv2.COLOR_RGB2BGR)
                pixmap_resized = self.mat_to_q_pixmap(frame_bgr)
                frame_bgr = None
                data = (True, frame_bgr, pixmap_resized)
                self.share_frame_signal.emit(data)

    def media_player_opening(self, event):
        logger.debug(f"media_player_opening = {event.u.new_status}")
        self.camera_state = CameraState.connecting
        self.camera_state_signal.emit(
            self.camera_state)
        # self.media_loaded = False
        # logger.debug(f'media_player_opening: duration {self.get_duration()} - self.get_time() {self.get_time()}')

    def media_player_buffering(self, event):
        buffering_percent = event.u.new_cache
        # if buffering_percent == 0.0:
        #     logger.debug(f'media_player_buffering: {buffering_percent}% buffered - duration {self.get_duration()} - time {self.get_time()}')
        
        # if buffering_percent == 100.0:
        #     self.media_loaded = True
        #     self.media_loaded_signal.emit()

        if hasattr(self, 'buffering_signal'):
            self.buffering_signal.emit(buffering_percent)

    def media_player_started(self, event):
        logger.debug(f"media_player_started START = {event.u.new_status}")
        self.camera_state = CameraState.started
        self.camera_state_signal.emit(
            self.camera_state)
        if self.seek_time is not None and self.start_duration is not None:
            duration = self.get_duration()
            logger.debug(f'Seeking to position {self.seek_time}ms / {duration}ms')
            if 0 < self.seek_time < duration:
                # Calculate position and seek
                pos = self.seek_time / duration
                logger.debug(f'Seeking to position {pos} ({self.seek_time}ms / {duration}ms)')
                self.set_position(pos)
        logger.debug(f'media_player_started:')

    def position_changed(self, event):
        # new_position = event.u.new_position
        current_time = self.get_time()
        # duration = self.get_duration()
        try:
            if self.timelinecontroller is not None:
                self.current_duration = self.start_duration + current_time
                self.timelinecontroller.onPositionChanged(self.current_duration)

        except Exception as e:
            logger.debug(f'position_changed = {e}')

    def media_player_paused(self, event):
        logger.debug(f"media_player_paused = {event.u.new_status}")
        self.camera_state = CameraState.paused
        self.camera_state_signal.emit(self.camera_state)

    def media_player_stopped(self, event):
        logger.debug(f"media_player_stopped = {event.type}-{self.media_list_player.get_state()}")
        if len(self.registered_widgets) > 0:
            self.camera_state = CameraState.stopped
            self.camera_state_signal.emit(self.camera_state)
        # if event.u.new_status != 0:
        #     self.next_chunk_signal.emit('ahihi')
    def media_player_error(self, event):
        logger.debug(f"media_player_error = {event.type}-{self.media_list_player.get_state()}")

    def load_media(self, media_path, vlc_options=None, seek_time=None,start_duration = None,end_duration = None):
        logger.debug(f'load_media ===== {media_path} - seek_time: {seek_time}')
        self.seek_time = seek_time
        self.start_duration = start_duration
        self.current_duration = start_duration
        self.end_duration = end_duration
        self.media_path = media_path

        # self.media_loaded = False
        # self.pending_seek = None
        # self.initial_position_received = False  # Reset flag when loading new media

        url = AnyUrl(media_path)
        if url.scheme == 'rtsp':
            vlc_options = 'rtsp-tcp'

        # Enhanced caching and buffering options
        default_options = [
            '--network-caching=1000',
            '--file-caching=1000',
            '--live-caching=1000',
            '--sout-mux-caching=1000',
            '--clock-jitter=0',
            '--clock-synchro=0'
        ]
        
        if vlc_options:
            vlc_options = f"{vlc_options} {' '.join(default_options)}"
        else:
            vlc_options = ' '.join(default_options)

        vlc_instance = get_vlc_instance()
        if vlc_instance is not None:
            media = vlc_instance.media_new(media_path, vlc_options)
            media_list = vlc_instance.media_list_new([media])
            self.media_list_player.set_media_list(media_list)
            self.media_list_player.play_item_at_index(0)
        else:
            logger.error("VLC instance không khả dụng để load media")

    def get_current_url(self):
        return self.media_path

    def get_xwindow(self):
        return self.media_player.get_xwindow()

    def get_size_from_curl(self, url: str) -> tuple[int, int]:
        """
        Get video dimensions from an MPD manifest file.
        
        Args:
            url (str): URL of the MPD manifest file
            
        Returns:
            tuple[int, int]: Width and height of the video. Returns (0, 0) if dimensions cannot be determined.
        """
        try:
            result = subprocess.run(['curl', url], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            if result.returncode != 0:
                logger.error(f'Failed to fetch MPD file: {result.stderr.decode()}')
                return 0, 0
            
            xml_content = result.stdout.decode('utf-8')
            root = ET.fromstring(xml_content)
            
            # First try to get dimensions from AdaptationSet
            adaptation_sets = root.findall('.//{urn:mpeg:dash:schema:mpd:2011}AdaptationSet[@contentType="video"]')
            if adaptation_sets:
                adapt_set = adaptation_sets[0]
                width = int(adapt_set.get('maxWidth', 0))
                height = int(adapt_set.get('maxHeight', 0))
                if width > 0 and height > 0:
                    return width, height
                
            # If not found in AdaptationSet, try Representation
            representations = root.findall('.//{urn:mpeg:dash:schema:mpd:2011}Representation')
            if representations:
                rep = representations[0]
                width = int(rep.get('width', 0))
                height = int(rep.get('height', 0))
                return width, height
            
            logger.warning(f'No video dimensions found in MPD file')
            return 0, 0
        
        except ET.ParseError as e:
            logger.error(f'Failed to parse MPD XML: {e}')
            return 0, 0
        except Exception as e:
            logger.error(f'Error getting video dimensions: {e}')
            return 0, 0

    def play_video(self):
        self.is_pause_video = True
        logger.debug(f'play_video:')
        if self.get_current_url() is not None:
            size = self.get_size_from_curl(self.get_current_url())
            # div by 2 to scale down resolution -> increase performance
            target_width, target_height = self.calculate_target_dimensions(size[0] / 2, size[1] / 2, self.MAX_WIDTH_VIDEO_DECODE, self.MAX_HEIGHT_VIDEO_DECODE)
            
            # update target_width and target_height did aspect ratio to original_width_video_playback and original_height_video_playback
            self.original_width_video_playback = target_width
            self.original_height_video_playback = target_height

            self.update_frame_buffer(target_width, target_height)
            self.media_player.video_set_callbacks(self.video_lock_callback_wrapped, self.video_unlock_callback_wrapped,
                                                self.video_display_callback_wrapped, None)
            logger.debug(f'play_video: target_width: {target_width} - target_height: {target_height}')
            self.media_player.video_set_format("RV24", target_width, target_height, target_width * 3)
            self.media_list_player.play()

    def start_video(self):
        logger.debug("play")
        self.is_pause_video = True
        self.media_list_player.play()

    def pause_video(self):
        # play: 0
        # pause: 1
        self.is_pause_video = False
        logger.debug("pause_video: pause")
        # self.media_list_player.set_pause(1)
        self.media_list_player.pause()

    def stop_video(self):
        logger.debug(f'Camera {self.camera_id}: Stopping video')
        self.media_list_player.stop()

    def set_volume(self, volume: int):
        """Set video volume (0-100)"""
        if hasattr(self, 'media_player'):
            # Convert 0-100 range to 0-200 for VLC
            vlc_volume = min(200, volume * 2)
            self.media_player.audio_set_volume(vlc_volume)

    def get_volume(self) -> int:
        """Get current volume (0-100)"""
        if hasattr(self, 'media_player'):
            # Convert VLC's 0-200 range to 0-100
            return self.media_player.audio_get_volume() // 2
        return 0

    def mute(self):
        """Mute audio"""
        if hasattr(self, 'media_player'):
            self.media_player.audio_set_mute(True)

    def unmute(self):
        """Unmute audio"""
        if hasattr(self, 'media_player'):
            self.media_player.audio_set_mute(False)

    def set_position(self, position):
        self.media_player.set_position(position)

    def get_position(self):
        return self.media_player.get_position()

    def get_time(self):
        return self.media_player.get_time()

    def set_time(self, time):
        self.media_player.set_time(time)

    def get_duration(self):
        return self.media_player.get_media().get_duration()

    def is_playing(self):
        return self.media_player.is_playing()

    def get_length(self):
        return self.media_player.get_length()

    def set_window_id(self, videoframe):
        platform_setters = {
            'linux': lambda: self.media_player.set_xwindow(videoframe.winId()),
            'win32': lambda: self.media_player.set_hwnd(videoframe.winId()),
            'darwin': lambda: self.media_player.set_nsobject(int(videoframe.winId()))
        }
        platform_setters.get(sys.platform, lambda: None)()

    def set_speed(self, speed):
        self.media_player.set_rate(speed)

    def get_speed(self):
        return self.media_player.get_rate()

    # def __del__(self):
    #     """Destructor to ensure cleanup when object is destroyed"""
    #     try:
    #         logger.debug(f'Camera {self.camera_id}: Destructor called')
    #         self.stop_capture()
    #     except Exception as e:
    #         logger.error(f'Error during Player destruction: {e}')

    def update_frame_buffer(self, target_width, target_height):
        """Cập nhật frame buffer nếu cần thiết"""
        if not (target_width and target_height and self.stream_type == CommonEnum.StreamType.VIDEO_STREAM):
            return

        logger.debug(f'update_frame_buffer: target_width: {target_width} - target_height: {target_height}')
        # Tính toán frame size với padding phù hợp
        frame_size = target_width * target_height * 3  # RGB (3 byte mỗi pixel)
        logger.debug(f'update_frame_buffer: frame_size: {frame_size}')
        # Đảm bảo kích thước frame chia hết cho 4
        self.frame_size = self.make_divisible_by_four(frame_size)
        logger.debug(f'update_frame_buffer: frame_size after make_divisible_by_four: {self.frame_size}')
        
        # if self.frame_size is None:
        self.frame_buffer = (ctypes.c_uint8 * self.frame_size)()
        logger.debug(f'Khởi tạo frame buffer mới - Kích thước: {self.frame_size}')
