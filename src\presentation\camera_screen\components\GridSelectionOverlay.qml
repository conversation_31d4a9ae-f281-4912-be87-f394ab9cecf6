import QtQuick 2.15
import "../constants/ZIndexConstants.js" as ZIndex

Item {
    id: selectionOverlay
    anchors.fill: parent
    visible: false
    z: ZIndex.selectionOverlay || 1000

    property point startPos: Qt.point(0, 0)
    property point endPos: Qt.point(0, 0)
    property bool isDragging: false
    property var gridModel: null
    property int selectedItemCount: 0

    property color fillColor: "#75FE8C33"
    property color borderColor: "#75FE8C"
    property int borderWidth: 2
    property real currentOpacity: 0.8

    signal selectionRectangleChanged(real minX, real minY, real maxX, real maxY)
    signal selectionFinalized(point startPos, point endPos)
    signal itemCountChanged(int count)

    Canvas {
        id: selectionCanvas
        anchors.fill: parent
        visible: selectionOverlay.isDragging

        onPaint: {
            var ctx = getContext("2d")
            ctx.clearRect(0, 0, width, height)

            if (!selectionOverlay.isDragging) return

            var minX = Math.min(selectionOverlay.startPos.x, selectionOverlay.endPos.x)
            var minY = Math.min(selectionOverlay.startPos.y, selectionOverlay.endPos.y)
            var maxX = Math.max(selectionOverlay.startPos.x, selectionOverlay.endPos.x)
            var maxY = Math.max(selectionOverlay.startPos.y, selectionOverlay.endPos.y)

            var rectWidth = maxX - minX
            var rectHeight = maxY - minY

            if (rectWidth > 2 && rectHeight > 2) {
                ctx.fillStyle = selectionOverlay.fillColor
                ctx.fillRect(minX, minY, rectWidth, rectHeight)

                ctx.strokeStyle = selectionOverlay.borderColor
                ctx.lineWidth = selectionOverlay.borderWidth
                ctx.strokeRect(minX, minY, rectWidth, rectHeight)
            }
        }

        Connections {
            target: selectionOverlay
            function onStartPosChanged() { selectionCanvas.requestPaint() }
            function onEndPosChanged() { selectionCanvas.requestPaint() }
            function onIsDraggingChanged() { selectionCanvas.requestPaint() }
        }
    }

    function startDragSelection(startX, startY) {
        startPos = Qt.point(startX, startY)
        endPos = startPos
        visible = true
        isDragging = true
        
        currentOpacity = 0
        fadeInAnimation.start()
        
        selectionCanvas.requestPaint()
    }

    function updateDragSelection(currentX, currentY) {
        if (!isDragging) return

        endPos = Qt.point(currentX, currentY)

        var minX = Math.min(startPos.x, currentX)
        var minY = Math.min(startPos.y, currentY)
        var maxX = Math.max(startPos.x, currentX)
        var maxY = Math.max(startPos.y, currentY)

        selectionRectangleChanged(minX, minY, maxX, maxY)
    }

    function updateSelectedItemCount(count) {
        selectedItemCount = count
        itemCountChanged(count)
    }

    function endDragSelection() {
        isDragging = false
        
        selectionCanvas.requestPaint()
        
        fadeOutAnimation.start()
        
        selectionFinalized(startPos, endPos)
    }

    function cancelDragSelection() {
        isDragging = false
        
        selectionCanvas.requestPaint()
        
        visible = false
        currentOpacity = 0
    }

    NumberAnimation {
        id: fadeInAnimation
        target: selectionOverlay
        property: "currentOpacity"
        from: 0
        to: 0.8
        duration: 100
        easing.type: Easing.OutQuad
        
        onRunningChanged: {
            if (running) {
                selectionCanvas.opacity = currentOpacity
            }
        }
    }

    NumberAnimation {
        id: fadeOutAnimation
        target: selectionOverlay
        property: "currentOpacity"
        from: currentOpacity
        to: 0
        duration: 150
        easing.type: Easing.OutQuad
        
        onRunningChanged: {
            if (running) {
                selectionCanvas.opacity = currentOpacity
            }
        }
        
        onFinished: {
            selectionOverlay.visible = false
            selectionCanvas.opacity = 0
        }
    }

    SequentialAnimation {
        id: pulseAnimation
        running: isDragging
        loops: Animation.Infinite
        
        NumberAnimation {
            target: selectionOverlay
            property: "borderWidth"
            from: 2
            to: 3
            duration: 800
            easing.type: Easing.InOutQuad
            
            onRunningChanged: {
                if (running) {
                    selectionCanvas.requestPaint()
                }
            }
        }
        
        NumberAnimation {
            target: selectionOverlay
            property: "borderWidth"
            from: 3
            to: 2
            duration: 800
            easing.type: Easing.InOutQuad
            
            onRunningChanged: {
                if (running) {
                    selectionCanvas.requestPaint()
                }
            }
        }
    }

    Text {
        id: debugText
        anchors.centerIn: parent
        text: {
            if (!isDragging) return ""
            
            if (selectedItemCount > 0) {
                return "Selecting: " + selectedItemCount + " item" + (selectedItemCount !== 1 ? "s" : "")
            } else {
                return "Selecting..."
            }
        }
        color: "#ffffff"
        font.pixelSize: 12
        font.weight: Font.Medium
        visible: isDragging && Math.abs(endPos.x - startPos.x) > 30 && Math.abs(endPos.y - startPos.y) > 20
        z: selectionCanvas.z + 1
        
        Rectangle {
            anchors.centerIn: parent
            width: parent.implicitWidth + 8
            height: parent.implicitHeight + 4
            color: "#000000aa"
            radius: 3
            z: -1
        }
    }

    Binding {
        target: selectionCanvas
        property: "opacity"
        value: selectionOverlay.currentOpacity
        when: selectionOverlay.isDragging || fadeInAnimation.running || fadeOutAnimation.running
    }
}
