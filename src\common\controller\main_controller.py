
from src.common.model.item_grid_model import ItemGridModel
from PySide6.QtCore import Qt,Signal,QObject

from typing import List
import logging
import gc
from src.common.onvif_api.worker_thread import WorkerThread
from src.utils.theme_setting import theme_setting
from src.styles.style import Theme
from src.styles.dark_theme_color import DarkThemeColor
from src.styles.light_theme_color import LightThemeColor
logger = logging.getLogger(__name__)

def connect_slot(*data):
    for signal, slot in data:
        signal.connect(slot)
class MainController(QObject):
    # Server signal
    search_server_signal = Signal(str)
    # Controller signal
    complete_fetching_data = Signal(tuple)
    complete_ui_update = Signal(tuple)
    discovery_in_group_signal = Signal(tuple)

    # MainTreeViewWidget signal  
    open_camera_in_tab_signal = Signal(tuple)
    open_floor_in_tab_signal = Signal(tuple)
    open_map_in_tab_signal = Signal(tuple)
    camera_clicked_signal = Signal(int, str)
    stop_live_camera_signal = Signal(str)
    stop_live_group_signal = Signal(str)
    open_camera_position_signal = Signal(object)
    open_map_position_signal = Signal(object)
    switch_stream_signal = Signal(tuple)
    edit_map_2D_signal = Signal(tuple)
    edit_floor_signal = Signal(tuple)
    edit_map_signal = Signal(tuple)

    open_map_2D_signal = Signal(tuple)
    save_map_2D_signal = Signal(tuple)

    # DeviceScreen signal 
    delete_groups_signal = Signal(bool)
    listen_ws_add_camera_signal = Signal(tuple)
    scan_result = Signal(tuple)

    # AddGroupDialog signal
    group_selected_signal = Signal(tuple)
    number_group_signal = Signal(int)

    # Animation fetch data signal
    signal_load_animation = Signal(bool)

    # Check user information signal
    signal_check_username = Signal(tuple)
    signal_check_email = Signal(object)

    # Add/Remove tab
    add_all_tab_signal = Signal()
    remove_all_tab_signal = Signal()

    # Change theme signal
    theme_change_signal = Signal()
    
    # CameraBottomToolbarWidget signal
    stream_status_signal = Signal(tuple)
    
    __instance = None
    def __init__(self):
        super().__init__()
        self.mainwindow_geometry = None
        self.update_list_tree_group_data = None
        self.show_message_dialog = None
        self.update_list_setting_warning_alert = None
        self.current_tab = 0
        self.current_tab_name = ""
        self.current_grid_model: ItemGridModel = None
        self.audio_thread = None
        self.stop_update_ai_event = False
        self.change_server = None
        self.update_profiles = None
        self.list_camera_ids = []
        self.list_parent = {}
        self.open_camera_in_tab = None
        self.has_intel_gpu_availabe = False
        self.message_log = None
        self.progress_dialog = None
        self.total_camera_items = 20
        self.total_group_items = 20
        self.event_selected = None
        self.key_filter = None
        self.id_response_search_camera = None
        self.current_controller = None
        self.tracing_windows_dict = {}
        self.current_theme = theme_setting.get_theme_from_setting()
        self.clientId = None
        self.startTime = 0
        self.theme_dict = {
            Theme.DARK: DarkThemeColor,
            Theme.LIGHT: LightThemeColor
        }

    @staticmethod
    def get_instance():
        if MainController.__instance is None:
            MainController.__instance = MainController()
        return MainController.__instance

    def disconnect_slot(self,*data):
        for signal, slot in data:
            signal.connect(slot)

    def gc_collect(self, parent):
        # use QThread
        WorkerThread(parent=parent, target=self.gc_collect_thread).start()

    def gc_collect_thread(self):
        gc.collect()

    def get_theme_attribute(self, attribute, key, alpha=1.0):
        current_color = getattr(getattr(self.theme_dict[self.current_theme], attribute), key)
        return self.hex_to_rgba(current_color, alpha)
    
    def hex_to_rgba(self, hex_color, alpha):
        if alpha == 1.0:
            return hex_color
        hex_color = hex_color.lstrip('#')
        r, g, b = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        return f"rgba({r}, {g}, {b}, {alpha})"

    def change_theme(self, theme):
        self.current_theme = theme
        self.theme_change_signal.emit()

    def is_dark_theme(self):
        return self.current_theme == Theme.DARK

   
main_controller = MainController.get_instance()
