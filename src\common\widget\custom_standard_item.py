from PySide6.QtGui import QStandardItem, QIcon,QBrush,QColor
from PySide6.QtWidgets import QTreeWidgetItem

from src.common.model.camera_model import CameraModel
from src.common.qml.models.common_enum import CommonEnum
from src.common.model.group_model import GroupModel
from src.common.model.tab_model import TabModel
from src.styles.style import Style
from src.common.qml.models.map_controller import BuildingModel
from src.common.controller.main_controller import main_controller
import logging

logger = logging.getLogger(__name__)
class CustomStandardItem(QStandardItem):
    def __init__(self, text=None, item_model=None, server_ip=None):
        super().__init__(text)
        self.server_ip = server_ip
        self.item_model = item_model
        if self.item_model is not None and (isinstance(self.item_model,CameraModel) or isinstance(self.item_model,GroupModel)):
            self.item_model.register_standard_item(self)
            self.item_model.change_model.connect(self.update_change_model)
        elif self.item_model is not None and isinstance(self.item_model,TabModel):
            self.item_model.register_standard_item(self)
            self.item_model.tab_name_changed.connect(self.tab_name_changed)
        elif self.item_model is not None and isinstance(self.item_model,BuildingModel):
            self.item_model.latitudeChanged.connect(self.latitudeChanged)
            self.item_model.nameChanged.connect(self.nameChanged)

    def disconnect_slot(self):
        if self.item_model is not None and (isinstance(self.item_model,CameraModel) or isinstance(self.item_model,GroupModel)):
            self.item_model.change_model.disconnect(self.update_change_model)
        elif self.item_model is not None and isinstance(self.item_model,TabModel):
            self.item_model.tab_name_changed.disconnect(self.tab_name_changed)

    def tab_name_changed(self, new_name):
        self.setText(new_name)

    def update_change_model(self, data):
        key, value, model = data
        if key == 'name':
            self.setText(value)
        elif key == 'state' or key == 'coordinateLat' or key == 'coordinateLong' or key == 'recordingState':
            try:
                icon = CameraModel.get_icon(model.state_merged)
                self.setIcon(QIcon(icon))
                if model.state_merged == CommonEnum.CameraState.DISCONNECTED_REC_PIN or \
                    model.state_merged == CommonEnum.CameraState.DISCONNECTED_REC_UNPIN or \
                    model.state_merged == CommonEnum.CameraState.DISCONNECTED_NOREC_PIN or \
                    model.state_merged == CommonEnum.CameraState.DISCONNECTED_NOREC_UNPIN:
                    self.setForeground(QBrush(QColor(169, 169, 169)))
                else:
                    self.setForeground(QBrush(QColor(main_controller.get_theme_attribute('Color', 'text_color_all_app'))))
            except Exception as e:
                logger.info(f'update_change_model error: {e}')

    def latitudeChanged(self):
        if self.item_model.latitude is not None:
            self.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'building_on')))
        else:
            self.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'building_off')))

    def nameChanged(self):
        self.setText(self.item_model.name)

class CustomQTreeWidgetItem(QTreeWidgetItem):
    def __init__(self, parent, item_model=None, server_ip=None):
        super().__init__(parent)
        self.server_ip = server_ip
        self.item_model = item_model

