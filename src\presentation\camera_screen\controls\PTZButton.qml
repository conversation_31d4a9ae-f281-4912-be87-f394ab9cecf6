import QtQuick
import QtQuick.Controls

/**
 * PTZButton.qml - Nút điều khiển PTZ
 *
 * CHỨC NĂNG CHÍNH:
 * - Hi<PERSON>n thị nút điều khiển PTZ
 * - <PERSON><PERSON> lý sự kiện click và release
 * - Tùy chỉnh giao diện theo theme
 */
Rectangle {
    id: ptzButton
    width: 32
    height: 32
    color: ptzMouseArea.pressed ? "#4fd1c5" : (isDarkTheme ? "#2d2d2d" : "#f0f0f0")
    radius: 4

    property bool isDarkTheme: true
    property string text: "•"
    property alias font: buttonText.font

    // Custom signals to avoid conflicts
    signal buttonClicked()
    signal buttonReleased()

    Text {
        id: buttonText
        text: ptzButton.text
        color: isDarkTheme ? "#ffffff" : "#000000"
        font.pixelSize: 16
        anchors.centerIn: parent
    }

    MouseArea {
        id: ptzMouseArea
        anchors.fill: parent

        onPressed: ptzButton.buttonClicked()
        onReleased: ptzButton.buttonReleased()
    }
}