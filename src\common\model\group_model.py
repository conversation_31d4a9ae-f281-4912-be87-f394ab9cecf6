from dataclasses import dataclass, field
from typing import List

from src.common.model.base_model import BaseModel
from PySide6.QtCore import QObject, Signal
from dataclasses import dataclass, asdict, fields
from src.common.model.model import Model
from PySide6.QtCore import QObject, Property, Signal
# Không được thay đổi các thuộc tính của object này trừ khi Backend API thay đổi

@dataclass
class Group:
    id: str = None
    createdDate: str = None
    name: str = None
    description: str = None
    type: str = None
    childGroupIds: List[str] = None
    childGroupDTOList: List[dict] = None
    parentGroupId: str = None
    cameraIds: List[str] = None
    cameraDTOs: List[dict] = None
    doorIds: List[str] = None
    doorDTOs: List[dict] = None
    aiFlowIds: List[str] = None
    serviceIds: List[str] = None
    
    # Human-related counts
    humanIntrusionCount: int = None
    humanFlowCount: int = None
    humanAccessControlCount: int = None
    humanWeaponDetectCount: int = None
    humanFeatureCount: int = None
    humanCount: int = None
    
    # Vehicle-related counts
    vehicleIntrusionCount: int = None
    vehicleFlowCount: int = None
    vehicleAccessControlCount: int = None
    vehicleFeatureCount: int = None
    vehicleCount: int = None

    # Other counts
    recognitionCount: int = None
    protectionCount: int = None
    frequencyCount: int = None
    accessCount: int = None
    motionCount: int = None
    trafficCount: int = None
    weaponCount: int = None
    ufoCount: int = None

    # Risk identification counts
    threatDetectionCount: int = None
    protectionRecognitionCount: int = None
    
    # User groups
    userGroupIdAltList: List[str] = None
    userGroupDTOs: List[dict] = None
    
    # Additional fields
    activate: bool = None
    features: List[str] = None
    
    # UI-specific fields (not in JSON but needed for UI)
    active: bool = None
    check_box: bool = None
    btn_edit: bool = None
    btn_trash: bool = None
    
    # Server-related fields (not in JSON but needed for functionality)
    server_ip: str = None
    mac: str = None
    ip: str = None
    partnerId: str = None
    serverPort: int = None
    serverIp: str = None
    serverUsername: str = None
    serverPassword: str = None
    clientName: str = None
    serverType: str = None

    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        filtered_dict = {key: value for key, value in data_dict.items() if key in field_names}
        return cls(**filtered_dict)

    def to_dict(self):
        return asdict(self)
    
    def aiApply(self):
        return (False,False)
    
class GroupModel(Model):
    change_name_signal = Signal(str)
    change_ai_flow_signal = Signal(int)
    change_model = Signal(tuple)
    change_active_signal = Signal(tuple)
    apply_ai_flow_signal = Signal(str)

    idChanged = Signal()

    def __init__(self,data:dict = {}):
        super().__init__(data = data)
        self.list_parent_tree = []
        self.list_standard_item = []

    def register_standard_item(self, standard_item):
        self.list_standard_item.append(standard_item)

    def unregister_standard_item(self,standard_item):
        try:
            standard_item.disconnect_slot()
            self.list_standard_item.remove(standard_item)
        except Exception as e:
            print(f'unregister_standard_item = {e}')

    def clear_list_standard_item(self):
        for standard_item in self.list_standard_item:
            standard_item.disconnect_slot()
        self.list_standard_item = []

    def set_name(self,name):
        self.set_property("name",name)
        self.change_model.emit(('name',name,self))

    def set_ai_flow(self,ai_flow_id = None):
        self.set_property("aiFlowIds",ai_flow_id)
        self.change_model.emit(('aiFlowIds',ai_flow_id,self))

    def set_camera_ids(self,camera_ids:List = []):
        self.set_property("cameraIds",camera_ids)
        self.change_model.emit(('cameraIds',camera_ids,self))

    def set_child_group_ids(self,childGroupIds:List = []):
        self.set_property("childGroupIds",childGroupIds)
        self.change_model.emit(('childGroupIds',childGroupIds,self))

    def apply_ai_flow(self, group = None):
        self.data = group
        self.apply_ai_flow_signal.emit('apply_ai_flow')

    def set_aiflow_count(self, key, value):
        if key == 'recognitionCount':
            self.set_property("recognitionCount",value)
        elif key == 'protectionCount':
            self.set_property("protectionCount",value)
        elif key == 'frequencyCount':
            self.set_property("frequencyCount",value)
        elif key == 'accessCount':
            self.set_property("accessCount",value)
        elif key == 'motionCount':
            self.set_property("motionCount",value)
        elif key == 'trafficCount':
            self.set_property("trafficCount",value)
        elif key == 'weaponCount':
            self.set_property("weaponCount",value)
        elif key == 'ufoCount':
            self.set_property("ufoCount",value)
        else:
            return False
        return True

    def diff_group_model(self, group:dict = None):
        dict1 = self.data
        dict2 = group
        diff = []
        for field,value in dict2.items():
            if dict1[field] != value:
                if field == 'name':
                    diff.append(field)
                    self.set_name(value)
                elif field == 'cameraIds':
                    diff.append(field)
                    self.set_camera_ids(value)
                elif field == 'aiFlowIds':
                    diff.append(field)
                    self.set_ai_flow(value)
                elif field == 'childGroupIds':
                    diff.append(field)
                    self.set_child_group_ids(value)
                elif self.set_aiflow_count(field,value):
                    self.change_model.emit((field,value,self))
                else:
                    diff.append(field)
                    if field != 'active' and field != 'check_box' and field != 'btn_edit' and field != 'btn_trash' and field != 'server_ip':
                        self.data[field] = value
        return diff
    
    @Property(str, notify=idChanged)
    def id(self)->str:
        return self.get_property("id",None)
    
class GroupModelManager(QObject):
    add_group_list_signal = Signal(tuple)
    delete_group_model_signal = Signal(list)
    add_group_signal = Signal(tuple)
    __instance = None
    def __init__(self):
        super().__init__()
        self.group_list = {}

    @staticmethod
    def get_instance():
        if GroupModelManager.__instance is None:
            GroupModelManager.__instance = GroupModelManager()
        return GroupModelManager.__instance

    # NORMAL GROUP
    def add_group_list(self, controller = None,group_list: List[GroupModel] = [],server_ip = None):
        output = {}
        for group_model in group_list:
            group_model.set_property("server_ip",controller.server.data.server_ip)
            output[group_model.get_property('id')] = group_model
        self.group_list[controller.server.data.server_ip] = output
        self.add_group_list_signal.emit((controller,group_list))

    def add_aibox_list(self, controller = None,aibox_list: List[GroupModel] = []):
        output = {}
        for group_model in aibox_list:
            group_model.set_property("server_ip",controller.server.data.server_ip)
            output[group_model.get_property('id')] = group_model
        self.group_list[controller.server.data.server_ip].update(output)

    def add_group(self, group: GroupModel = None, is_insert_group=None):
        group_result = self.get_group_model(group.get_property('id'))
        if group_result is not None:
            return
        data = (group, is_insert_group)
        self.group_list[group.get_property('server_ip')][group.get_property('id')] = group
        self.add_group_signal.emit(data)

    def update_group_list(self,controller = None, group_list:List[GroupModel] = []):
        for group in group_list:
            for group_model in self.group_list[controller.server.data.server_ip].values():
                if group.get_property('id') == group_model.get_property('id'):
                    group_model.diff_group_model(group=group.data)
                    break
                
    def find_group_deleted(self,controller = None, group_list:List[GroupModel] = []):
        temp = []
        temp_group_list = []
        # for group_model in group_list:
        #     if group_model.data.id in self.group_list[group_model.data.server_ip]:
        #         del self.group_list[group_model.data.server_ip][group_model.data.id]
        for group_model in self.group_list[controller.server.data.server_ip].values():
            check = False
            for group in group_list:
                if group_model.get_property('id') == group.get_property('id'):
                    check = True
            if not check:
                temp.append(group_model)

        for group_model in temp:
            del self.group_list[group_model.get_property('server_ip')][group_model.get_property('id')]
        self.delete_group_model_signal.emit(temp)
        
    def get_group_model(self,id = None,name = None):
        if id is not None:
            for group_list in self.group_list.values():  
                if id in group_list:
                    return group_list[id]
        if name is not None:
            for group_list in self.group_list.values():  
                for key,item in group_list.items():
                    if name == item.get_property('name'):
                        return item
        return None
    
    def get_group_list(self,server_ip = None,name = None):
        if server_ip in self.group_list:
            return self.group_list[server_ip]
        elif name is not None:
            for value in self.group_list.values():
                for group_model in value.values():
                    if name == group_model.get_property('name'):
                        return group_model
        else:
            return {}
        
    def clear(self):
        self.group_list = []

    def delete_server(self,server_ip = None):
        if server_ip in self.group_list:
            del self.group_list[server_ip]
    
    def clear_list_standard_item(self):
        for group_list in self.group_list.values():
            for group_model in group_list.values():
                group_model.clear_list_standard_item()

group_model_manager = GroupModelManager.get_instance()
