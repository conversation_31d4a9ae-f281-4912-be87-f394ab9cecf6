import QtQuick 
import QtQuick.Controls 
import QtQuick.Layouts
import QtLocation
import QtPositioning

Rectangle {
    property var currentCoordinate: (typeof map_picker_controller !== "undefined" &&
                                      map_picker_controller !== undefined &&
                                      map_picker_controller.lon !== undefined &&
                                      map_picker_controller.lat !== undefined)
                                      ? QtPositioning.coordinate(map_picker_controller.lat, map_picker_controller.lon)
                                      : QtPositioning.coordinate(0.0, 0.0)
    
    property bool isLongPress: false

    // Plugin {
    //     id: mapPlugin
    //     name: "osm"
    //     PluginParameter {
    //         name: "osm.mapping.providersrepository.disabled"
    //         value: "true"
    //     }
    //     PluginParameter {
    //         name: "osm.cache.directory"
    //         value: ""
    //     }
    //     PluginParameter {
    //         name: "osm.cache.disk.cost"
    //         value: "0"
    //     }
    //     PluginParameter {
    //         name: "osm.cache.memory.cost"
    //         value: "0"
    //     }
    // }
    
    
    Map {
        id: map
        anchors.fill: parent
        plugin: Plugin {
            id: mapPlugin
            name: "osm"
            PluginParameter {
                name: "osm.mapping.custom.host"
                value: "https://api.gpstech.vn/geo-service"
            }
            PluginParameter {
                name: "osm.mapping.offline.directory"
                value: "e:/Project/python/VMS-Training/qml_reconstruct/cache"
            }
            PluginParameter {
                name: "osm.mapping.cache.directory"
                value: "e:/Project/python/VMS-Training/qml_reconstruct/cache"
            }
            PluginParameter {
                name: "osm.cache.directory"
                value: ""
            }
            PluginParameter {
                name: "osm.cache.disk.cost"
                value: "0"
            }
            PluginParameter {
                name: "osm.cache.memory.cost"
                value: "0"
            }
        }

        zoomLevel: 14
        center: currentCoordinate
        activeMapType: supportedMapTypes[supportedMapTypes.length - 1]
        property geoCoordinate startCentroid
        antialiasing: true

        Behavior on center {
            PropertyAnimation {
                duration: 400
                easing.type: Easing.InOutQuad
            }
        }

        Point {
            coordinate: currentCoordinate
        }

        PinchHandler {
            id: pinch
            target: null
            onActiveChanged: if (active) {
                map.startCentroid = map.toCoordinate(pinch.centroid.position, false)
            }
            onScaleChanged: (delta) => {
                map.zoomLevel += Math.log2(delta)
                map.alignCoordinateToPoint(map.startCentroid, pinch.centroid.position)
            }
            onRotationChanged: (delta) => {
                map.bearing -= delta
                map.alignCoordinateToPoint(map.startCentroid, pinch.centroid.position)
            }
            grabPermissions: PointerHandler.TakeOverForbidden
        }
        WheelHandler {
            id: wheel
            acceptedDevices: Qt.platform.pluginName === "cocoa" || Qt.platform.pluginName === "wayland"
                             ? PointerDevice.Mouse | PointerDevice.TouchPad
                             : PointerDevice.Mouse
            rotationScale: 1/120
            property: "zoomLevel"
        }
        DragHandler {
            id: drag
            target: null
            onTranslationChanged: (delta) => map.pan(-delta.x, -delta.y)
        }
        Shortcut {
            enabled: map.zoomLevel < map.maximumZoomLevel
            sequence: StandardKey.ZoomIn
            onActivated: map.zoomLevel = Math.round(map.zoomLevel + 1)
        }
        Shortcut {
            enabled: map.zoomLevel > map.minimumZoomLevel
            sequence: StandardKey.ZoomOut
            onActivated: map.zoomLevel = Math.round(map.zoomLevel - 1)
        }
        
        MouseArea {
            anchors.fill: parent
            hoverEnabled: true
            property var clickTimer: Timer {
                interval: 500
                onTriggered: isLongPress = true
            }

            onPressed: {
                isLongPress = false
                clickTimer.start()
            }

            onReleased: (mouse) =>{
                clickTimer.stop()
                if (!isLongPress) {
                    var clickedCoordinate = map.toCoordinate(Qt.point(mouse.x, mouse.y))
                    currentCoordinate = clickedCoordinate
                    map_picker_controller.set_position(currentCoordinate.longitude, currentCoordinate.latitude)
                }
            }

            onPositionChanged: function(mouse) {
                var clickedCoordinate = map.toCoordinate(Qt.point(mouse.x, mouse.y))
                coordText.text = clickedCoordinate.latitude + ", " + clickedCoordinate.longitude
            }

            onPressAndHold: {
                isLongPress = true
            }
        }
    }

    Text{
        id: coordText
        anchors.top: parent.top
        anchors.right: parent.right
        text: currentCoordinate.latitude + ", " + currentCoordinate.longitude
    }
}