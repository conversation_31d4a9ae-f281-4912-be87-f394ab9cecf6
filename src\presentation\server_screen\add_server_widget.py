from src.common.widget.search_widget.search_bar import SearchBar
from src.styles.style import Style, Theme
from PySide6.QtCore import Qt,QSize
from PySide6.QtGui import QIcon, QColor
from PySide6.QtWidgets import <PERSON><PERSON><PERSON>ckedWidge<PERSON>, <PERSON>W<PERSON><PERSON>,QPushButton,QHBoxLayout, QGraphicsDropShadowEffect, QLabel
from src.presentation.server_screen.login_dialog import LoginDialog
from src.common.controller.main_controller import main_controller
import os
import logging
from PySide6.QtCore import QCoreApplication

logger = logging.getLogger(__name__)

basedir = os.path.dirname(__file__)

class AddServerWidget(QWidget):
    def __init__(self,parent = None):
        super().__init__(parent)

        self.is_on_background = False
        main_layout = QHBoxLayout(self)
        self.stackedwidget = QStackedWidget()
        self.add_server_widget = self.create_add_server_widget()
        self.search_server_widget = self.create_search_server_widget()
        self.stackedwidget.addWidget(self.add_server_widget)
        self.stackedwidget.addWidget(self.search_server_widget)
        # self.stackedwidget.setCurrentIndex(1)
        main_layout.addWidget(self.stackedwidget)
                

        self.set_dynamic_stylesheet()

        # Kết nối signal cập nhật số lượng server
        from src.common.server.server_info import server_info_model_manager
        server_info_model_manager.add_server_signal.connect(self.update_server_status)
        server_info_model_manager.delete_server_signal.connect(self.update_server_status)
        for server in server_info_model_manager.server_list.values():
            try:
                server.change_status_signal.connect(self.update_server_status)
            except:
                pass

    def create_add_server_widget(self):
        add_server_widget = QWidget()
        layout = QHBoxLayout(add_server_widget)
        layout.setContentsMargins(0,0,0,0)
        layout.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.btn_search_server = QPushButton(icon = QIcon(main_controller.get_theme_attribute("Image", "search_server")))
        self.btn_search_server.setFixedSize(32,32)
        self.btn_search_server.setIconSize(QSize(24,24))
        self.btn_search_server.clicked.connect(self.btn_search_server_clicked)
        
        # Thêm label trạng thái server
        self.server_status_label = QLabel()
        self.server_status_label.setStyleSheet(f"color: {main_controller.get_theme_attribute('Color', 'text')}; font-size: 14px; font-weight: 400; margin-left: 8px;")
        self.server_status_label.setFixedHeight(32)
        self.update_server_status()

        self.btn_noti_server = QPushButton(icon = QIcon(main_controller.get_theme_attribute("Image", "noti_server")))
        self.btn_noti_server.setFixedSize(32,32)
        self.btn_noti_server.setIconSize(QSize(24,24))
        # self.btn_noti_server.clicked.connect(self.btn_search_server_clicked)

        self.btn_add_server = QPushButton('Add Server')
        self.btn_add_server.setIcon(QIcon(main_controller.get_theme_attribute("Image", "add_server")))
        self.btn_add_server.setToolTip('Add Server')
        self.btn_add_server.setFixedSize(131,32)
        self.btn_add_server.setIconSize(QSize(16,16))
        self.btn_add_server.clicked.connect(self.btn_add_server_clicked)
        self.btn_add_server.setStyleSheet(f'''
            QPushButton {{
                color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                text-align: left;
                padding-left: 12px;
            }}  
            QPushButton{{
                background-color: {main_controller.get_theme_attribute("Color", "primary")};
                color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                font-size: 14px;
                border: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                border-radius: 10px;
                padding: 1px;
                padding-left: 12px;
            }}

            QPushButton::hover{{
                background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};
                color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                font-size: 14px;
                border: 1px solid {main_controller.get_theme_attribute("Color", "text")};
                border-radius: 10px;
                padding: 5px;
                padding-left: 12px;
            }}
            QPushButton::pressed{{
                background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};
            }}               
        ''')
        layout.addWidget(self.server_status_label)  # Đưa label sang bên trái
        layout.addWidget(self.btn_search_server)
        layout.addWidget(self.btn_noti_server)
        layout.addWidget(self.btn_add_server)
        return add_server_widget
    
    def create_search_server_widget(self):
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0,0,10,0)
        layout.setSpacing(10)
        # layout.setAlignment(Qt.AlignmentFlag.AlignRight)
        right_layout = QHBoxLayout()
        self.btn_search = QPushButton('Search')
        self.btn_search.clicked.connect(self.btn_search_clicked)
        self.btn_cancel = QPushButton('Cancel')
        self.btn_cancel.clicked.connect(self.btn_cancel_clicked)
        right_layout.addWidget(self.btn_search)
        right_layout.addWidget(self.btn_cancel)
        self.search_server = SearchBar(parent=self)
        self.search_server.search_bar.setFixedSize(600,45)
        self.search_server.search_items_signal.connect(self.search_items_signal)
        self.search_server.search_bar.returnPressed.connect(self.btn_search_clicked)
        layout.addWidget(self.search_server)
        layout.addLayout(right_layout)
        return widget
    
    def btn_add_server_clicked(self):
        logger.debug(f'btn_add_server_clicked')
        add_server_dialog = LoginDialog(parent=self)
        add_server_dialog.exec()

    def btn_search_server_clicked(self):
        self.stackedwidget.setCurrentIndex(1)
        self.is_on_background = True
        self.on_background()

    def btn_search_clicked(self):
        main_controller.search_server_signal.emit(self.search_server.search_bar.text())

    def btn_cancel_clicked(self):
        self.stackedwidget.setCurrentIndex(0)
        self.is_on_background = False
        self.on_background()
        self.search_server.search_bar.setText('')
        main_controller.search_server_signal.emit('')

    def search_items_signal(self, text):
        logger.debug(f'search_items_signal = {text}')
        # main_controller.search_server_signal.emit(text)

    def on_background(self):
        if self.is_on_background:
            self.setStyleSheet(f'''
                QWidget {{
                        background-color: {main_controller.get_theme_attribute("Color", "search_server_background")};
                        border-radius: 8px;
                        }}
                QLabel {{
                        color: {Style.PrimaryColor.white_2};
                        }}  

                QPushButton{{
                    background-color: transparent;
                    color: {Style.PrimaryColor.white};
                    font-size: 14px;
                    border: none;
                    border-radius: 4px;
                    padding: 5px;}}

                QPushButton::hover{{
                    background-color: {main_controller.get_theme_attribute("Color", "search_server_lineedit_placeholder")};
                    color: {Style.PrimaryColor.white};
                    font-size: 14px;
                    border: none;
                    border-radius: 4px;
                    padding: 5px;}}
                    
                QPushButton::disabled{{
                    background-color: #5c5c5c;}}
                    
                QPushButton::pressed{{
                    background-color: {main_controller.get_theme_attribute("Color", "search_server_lineedit_placeholder")};
                    color: {Style.PrimaryColor.white};
                    font-size: 14px;
                    border: none;
                    border-radius: 4px;
                    padding: 5px;
                    }} 
            ''')
        else:
            self.setStyleSheet(f'''
                QWidget {{
                        background-color: transparent;
                        border-radius: 8px;
                        
                        }}
                QLabel {{
                        color: {Style.PrimaryColor.white_2};
                        }}  

                QPushButton{{
                    background-color: transparent;
                    color: {Style.PrimaryColor.white};
                    font-size: 14px;
                    border: none;
                    border-radius: 4px;
                    padding: 5px;}}

            ''')
            
    def set_dynamic_stylesheet(self):
        self.server_status_label.setStyleSheet(f"color: {main_controller.get_theme_attribute('Color', 'text')}; font-size: 14px; font-weight: 400; margin-left: 8px;")
        self.search_server.search_bar.setStyleSheet(
            f"""
            QLineEdit {{
                background-color: {main_controller.get_theme_attribute("Color", "search_server_lineedit_background")};
                color: {main_controller.get_theme_attribute("Color", "search_server_lineedit_placeholder")};
                border-radius: 20px;
                border: 1px solid {main_controller.get_theme_attribute("Color", "search_server_lineedit_border")};
                padding-left: 10px;
                padding-right: 10px;
                font-size: {Style.Size.body}px;
            }}

            QLineEdit:focus {{
                border: 1px solid {main_controller.get_theme_attribute("Color", "search_server_lineedit_border")};
            }}
            QLineEdit::clear-button {{
                image: url({main_controller.get_theme_attribute("Image", "close_tab")});
            }}
            """)

        self.btn_search.setStyleSheet(f'''                
                QPushButton{{
                    background-color: {main_controller.get_theme_attribute("Color", "primary")};
                    color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                    font-size: 14px;
                    border: none;
                    border-radius: 20px;
                    padding-left: 20px;
                    padding-right: 20px;
                    padding-top: 10px;
                    padding-bottom: 10px;}}

                QPushButton::disabled{{
                    background-color: #5c5c5c;}}
                QPushButton::pressed{{
                    background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};}}    
                ''')

        self.btn_cancel.setStyleSheet(f'''                
                QPushButton{{
                    background-color: {Style.PrimaryColor.button_second_background};
                    color: {Style.PrimaryColor.white};
                    font-size: 14px;
                    border: none;
                    border-radius: 20px;
                    padding-left: 20px;
                    padding-right: 20px;
                    padding-top: 10px;
                    padding-bottom: 10px;}}

                QPushButton::disabled{{
                    background-color: #5c5c5c;}}
                QPushButton::pressed{{
                    background-color: {Style.PrimaryColor.button_second_background};}}    
                ''')
        self.btn_add_server.setStyleSheet(f'''
            QPushButton {{
                color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                
                text-align: left;
                padding-left: 12px;
            }}  
            QPushButton{{
                background-color: {main_controller.get_theme_attribute("Color", "primary")};
                color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                font-size: 14px;
                border: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                border-radius: 10px;
                padding: 1px;
                padding-left: 12px;
            }}

            QPushButton::hover{{
                background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};
                color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                font-size: 14px;
                border: 1px solid {main_controller.get_theme_attribute("Color", "text")};
                border-radius: 10px;
                padding: 5px;
                padding-left: 12px;
            }}
            QPushButton::pressed{{
                background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};
            }}               
        ''')
        self.btn_add_server.setIcon(QIcon(main_controller.get_theme_attribute("Image", "add_server")))
        self.btn_noti_server.setIcon(QIcon(main_controller.get_theme_attribute("Image", "noti_server")))
        self.btn_search_server.setIcon(QIcon(main_controller.get_theme_attribute("Image", "search_server")))

        self.on_background()
        self.btn_noti_server.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                border: none;
                border-radius: 6px;
                padding: 2px;
            }}
            QPushButton::hover {{
                background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};
            }}
            QPushButton::pressed {{
                background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};
            }}
        """)
        self.btn_search_server.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                border: none;
                border-radius: 10px;
                padding: 2px;
            }}
            QPushButton::hover {{
                background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};
                
            }}
            QPushButton::pressed {{
                background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};
            }}
        """)

    def update_server_status(self):
        from src.common.server.server_info import server_info_model_manager
        connected_count = 0
        disconnected_count = 0
        for server in server_info_model_manager.server_list.values():
            if server.status:
                connected_count += 1
            else:
                disconnected_count += 1
        connected_icon = main_controller.get_theme_attribute("Image", "icon_state_server_on")
        disconnected_icon = main_controller.get_theme_attribute("Image", "icon_state_server_off")
        self.server_status_label.setText(
            f'<img src="{connected_icon}" width="8" height="8" style="vertical-align:middle;"/> '
            f'{QCoreApplication.translate("ServerScreen", "Connected")}: {connected_count}'
            f'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'  # thay cho tab
            f'<img src="{disconnected_icon}" width="8" height="8" style="vertical-align:middle;"/> '
            f'{QCoreApplication.translate("ServerScreen", "Disconnected")}: {disconnected_count}'
        )