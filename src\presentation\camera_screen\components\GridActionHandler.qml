import QtQuick 2.15
import QtQuick.Controls 2.15
import "../constants/ZIndexConstants.js" as ZIndex

Item {
    id: root
    anchors.fill: parent

    property var gridModel: null
    property var gridLinesOverlay: null
    property var dragHighlight: null
    property var selectionOverlay: null
    property bool isDarkTheme: true
    property var currentDragItem: null
    property bool isProcessingAction: false
    property bool isDragSelecting: false
    signal actionRequested(string actionType, var actionData)
    signal dragStartRequested(var item, real mouseX, real mouseY)
    signal dragUpdateRequested(var item, real mouseX, real mouseY)
    signal dragEndRequested(var item, real mouseX, real mouseY)

    // ✅ O(1) OPTIMIZATION: Throttle selection updates
    property var pendingSelection: null
    property bool selectionUpdatePending: false

    Timer {
        id: selectionThrottleTimer
        interval: 16  // ~60 FPS throttling
        repeat: false
        onTriggered: {
            if (pendingSelection && gridModel) {
                var selection = pendingSelection
                pendingSelection = null
                selectionUpdatePending = false

                // Perform actual selection update
                gridModel.selectRectangle(
                    selection.startRow, selection.startCol,
                    selection.endRow, selection.endCol
                )

                // Selection count will be updated via selectionCountChanged signal
            }
        }
    }

    MouseArea {
        id: globalMouseTracker
        anchors.fill: parent
        acceptedButtons: Qt.NoButton
        hoverEnabled: false
        z: ZIndex.contentMouseTracker

        property real currentMouseX: 0
        property real currentMouseY: 0

        onPositionChanged: function(mouse) {
            currentMouseX = mouse.x
            currentMouseY = mouse.y
        }

        function getCurrentMousePosition() {
            return Qt.point(currentMouseX, currentMouseY)
        }
    }

    MouseArea {
        id: globalWheelHandler
        anchors.fill: parent
        acceptedButtons: Qt.NoButton
        z: ZIndex.contentWheelHandler

        onWheel: function(wheel) {
            if (wheel.modifiers & Qt.ControlModifier) {
                handleCtrlWheel(wheel.angleDelta.y)
                wheel.accepted = true
            }
        }
    }

    MouseArea {
        id: dragSelectionArea
        anchors.fill: parent
        acceptedButtons: Qt.LeftButton
        z: ZIndex.contentMouseTracker - 1
        enabled: !currentDragItem

        property point dragStartPos: Qt.point(0, 0)

        onPressed: function(mouse) {
            if (isGridLocked) { mouse.accepted = false; return; }
            var itemAtPos = getItemAtPosition(mouse.x, mouse.y)

            if (mouse.modifiers & Qt.ShiftModifier) {
                if (!itemAtPos) {
                    isDragSelecting = true
                    dragStartPos = Qt.point(mouse.x, mouse.y)

                    if (selectionOverlay) {
                        selectionOverlay.startDragSelection(mouse.x, mouse.y)
                    }
                }
            } else if (!itemAtPos) {
                // Clear both selection and focus when clicking empty space
                if (gridModel && gridModel.clearSelectionRowCol) {
                    gridModel.clearSelectionRowCol()
                }
                if (gridModel && gridModel.clearFocus) {
                    gridModel.clearFocus()
                }

                if (gridModel && gridModel.deactivateAllPtz) {
                    gridModel.deactivateAllPtz()
                }
            }
        }

        onPositionChanged: function(mouse) {
            if (isDragSelecting && (mouse.buttons & Qt.LeftButton)) {
                if (selectionOverlay) {
                    selectionOverlay.updateDragSelection(mouse.x, mouse.y)
                }

                updateDragSelection(dragStartPos.x, dragStartPos.y, mouse.x, mouse.y)
            }
        }

        onReleased: function(mouse) {
            if (isDragSelecting) {
                if (selectionOverlay) {
                    selectionOverlay.endDragSelection()
                }

                finalizeDragSelection(dragStartPos.x, dragStartPos.y, mouse.x, mouse.y)

                isDragSelecting = false
            }
        }

        onCanceled: {
            if (isDragSelecting) {
                if (selectionOverlay) {
                    selectionOverlay.cancelDragSelection()
                }
                isDragSelecting = false
            }
        }
    }

    // ✅ MVVM: Property binding instead of method calls
    property int selectionCount: gridModel ? gridModel.selectionCount : 0

    onSelectionCountChanged: {
        if (selectionOverlay) {
            selectionOverlay.updateSelectedItemCount(selectionCount)
        }
    }

    Keys.onPressed: function(event) {
        if (!root.activeFocus) {
            root.forceActiveFocus()
        }

        if (isProcessingAction || !gridModel) return

        switch (event.key) {
            case Qt.Key_Delete:
                handleDeleteKey()
                event.accepted = true
                break

            case Qt.Key_F11:
                handleF11Key()
                event.accepted = true
                break

            case Qt.Key_G:
                if (event.modifiers & Qt.ControlModifier) {
                    handleCtrlG()
                    event.accepted = true
                }
                break

            case Qt.Key_Escape:
                handleEscapeKey()
                event.accepted = true
                break

            case Qt.Key_A:
                if (event.modifiers & Qt.ControlModifier) {
                    handleCtrlA()
                    event.accepted = true
                }
                break

            case Qt.Key_Up:
                handleArrowKey("Up")
                event.accepted = true
                break

            case Qt.Key_Down:
                handleArrowKey("Down")
                event.accepted = true
                break

            case Qt.Key_Left:
                handleArrowKey("Left")
                event.accepted = true
                break

            case Qt.Key_Right:
                handleArrowKey("Right")
                event.accepted = true
                break

            case Qt.Key_Space:
                handleSpaceKey()
                event.accepted = true
                break

            default:
                break
        }
    }

    function handleCtrlWheel(angleDelta) {
        if (gridLinesOverlay) {
            gridLinesOverlay.startCtrlWheelMode()
        }

        if (gridModel) {
            gridModel.handleCtrlWheel(angleDelta)
        }
    }

    function handleDeleteKey() {
        if (!gridModel) return;

        var selectedItems = gridModel.getSelectedItems();
        var positions = [];
        for (var i = 0; i < selectedItems.length; ++i) {
            var item = selectedItems[i];
            if (item && typeof item.row !== 'undefined' && typeof item.col !== 'undefined') {
                positions.push({row: item.row, col: item.col});
            }
        }
        if (positions.length > 0) {
            gridModel.removeMultipleCamerasAt(positions);
        }
    }

    function handleF11Key() {
        actionRequested("toggle_fullscreen", {})
    }

    function handleCtrlG() {
        if (gridLinesOverlay) {
            gridLinesOverlay.toggleManualMode()
            gridLinesOverlay.debugState()
        }
    }

    function handleEscapeKey() {
        // Clear both selection and focus on ESC
        if (gridModel && gridModel.clearSelectionRowCol) {
            gridModel.clearSelectionRowCol()
        }
        if (gridModel && gridModel.clearFocus) {
            gridModel.clearFocus()
        }

        if (gridModel && gridModel.deactivateAllPtz) {
            gridModel.deactivateAllPtz()
        }

        if (currentDragItem) {
            cancelDrag()
        }

        actionRequested("hide_context_menus", {})
        actionRequested("exit_fullscreen", {})
    }

    // Handle Ctrl+A selection
    function handleCtrlA() {
        if (gridModel && gridModel.selectAll) {
            gridModel.selectAll()
            Qt.callLater(function() {
                root.forceActiveFocus()
            })
        }
    }

    // ✅ REMOVED: getFirstGridItem method - using direct gridModel.selectAll approach

    function handleArrowKey(direction) {
        if (gridModel && gridModel.handleKeyPress) {
            gridModel.handleKeyPress(direction)
        }
    }

    function handleSpaceKey() {
        if (gridModel && gridModel.handleKeyPress) {
            gridModel.handleKeyPress("Space")
        }
    }

    function handleShiftPress(x, y, modifiers) {
        if (modifiers & Qt.ShiftModifier) {
            isDragSelecting = true
            dragSelectionArea.dragStartPos = Qt.point(x, y)

            if (selectionOverlay) {
                selectionOverlay.startDragSelection(x, y)
            }
        }
    }

    function handleShiftMove(x, y, modifiers) {
        if (isDragSelecting && (modifiers & Qt.ShiftModifier)) {
            if (selectionOverlay) {
                selectionOverlay.updateDragSelection(x, y)
            }

            updateDragSelection(dragSelectionArea.dragStartPos.x, dragSelectionArea.dragStartPos.y, x, y)
        }
    }

    function handleShiftRelease(x, y, modifiers) {
        if (isDragSelecting) {
            if (selectionOverlay) {
                selectionOverlay.endDragSelection()
            }

            finalizeDragSelection(dragSelectionArea.dragStartPos.x, dragSelectionArea.dragStartPos.y, x, y)

            isDragSelecting = false
        }
    }

    // Calculate drag target and execution decision using O(1) algorithm with collision detection
    function calculateDragDecision(item, cellWidth, cellHeight) {
        var itemRect = item.mapToItem(root, 0, 0, item.width, item.height)
        var itemLeft = itemRect.x, itemTop = itemRect.y
        var itemWidth = item.width, itemHeight = item.height

        var itemCenterX = itemLeft + itemWidth * 0.5
        var itemCenterY = itemTop + itemHeight * 0.5
        var primaryCol = Math.floor(itemCenterX / cellWidth)
        var primaryRow = Math.floor(itemCenterY / cellHeight)

        // True O(1): Unroll 4 candidate checks - no loops
        var result1 = checkSingleCandidateQML(primaryRow, primaryCol, itemLeft, itemTop, itemWidth, itemHeight, cellWidth, cellHeight, gridModel.rows, gridModel.columns)
        var result2 = checkSingleCandidateQML(primaryRow - 1, primaryCol, itemLeft, itemTop, itemWidth, itemHeight, cellWidth, cellHeight, gridModel.rows, gridModel.columns)
        var result3 = checkSingleCandidateQML(primaryRow, primaryCol - 1, itemLeft, itemTop, itemWidth, itemHeight, cellWidth, cellHeight, gridModel.rows, gridModel.columns)
        var result4 = checkSingleCandidateQML(primaryRow - 1, primaryCol - 1, itemLeft, itemTop, itemWidth, itemHeight, cellWidth, cellHeight, gridModel.rows, gridModel.columns)

        // Find best result with direct comparisons - no loops
        var bestOverlap = 0, bestTarget = null
        if (result1.overlap > bestOverlap) { bestOverlap = result1.overlap; bestTarget = result1.target }
        if (result2.overlap > bestOverlap) { bestOverlap = result2.overlap; bestTarget = result2.target }
        if (result3.overlap > bestOverlap) { bestOverlap = result3.overlap; bestTarget = result3.target }
        if (result4.overlap > bestOverlap) { bestOverlap = result4.overlap; bestTarget = result4.target }

        if (!bestTarget || bestOverlap < 0.1) {
            return { action: "hide" }
        }

        var targetRow = bestTarget[0], targetCol = bestTarget[1]
        var isValidMove = targetRow !== item.gridRow || targetCol !== item.gridCol
        var targetItem = gridModel.getItemAtRowColAnyPosition(targetRow, targetCol)
        var isDifferentItem = targetItem && (targetItem !== item.itemData)

        var itemCols = item.itemData.cols_cell || 1, itemRows = item.itemData.rows_cell || 1
        var hasCollision = false

        // True O(1) collision detection: check exactly 5 strategic positions
        var topLeft = gridModel.getItemAtRowColAnyPosition(targetRow, targetCol)
        var topRight = gridModel.getItemAtRowColAnyPosition(targetRow, targetCol + itemCols - 1)
        var bottomLeft = gridModel.getItemAtRowColAnyPosition(targetRow + itemRows - 1, targetCol)
        var bottomRight = gridModel.getItemAtRowColAnyPosition(targetRow + itemRows - 1, targetCol + itemCols - 1)
        var center = gridModel.getItemAtRowColAnyPosition(targetRow + Math.floor(itemRows/2), targetCol + Math.floor(itemCols/2))

        hasCollision = (topLeft && topLeft !== item.itemData && topLeft !== targetItem) ||
                      (topRight && topRight !== item.itemData && topRight !== targetItem) ||
                      (bottomLeft && bottomLeft !== item.itemData && bottomLeft !== targetItem) ||
                      (bottomRight && bottomRight !== item.itemData && bottomRight !== targetItem) ||
                      (center && center !== item.itemData && center !== targetItem)

        var canExecute = isValidMove && !hasCollision && (
            (isDifferentItem && bestOverlap >= 0.1) || (!isDifferentItem && bestOverlap >= 0.3)
        )

        return {
            action: canExecute ? "execute" : "fallback",
            targetRow: targetRow, targetCol: targetCol,
            targetItem: targetItem, isDifferentItem: isDifferentItem,
            overlap: bestOverlap
        }
    }

    // Check overlap percentage between item bounds and grid position
    function checkSingleCandidateQML(row, col, itemLeft, itemTop, itemWidth, itemHeight,
                                    cellWidth, cellHeight, gridRows, gridCols) {
        if (row < 0 || col < 0) {
            return { target: null, overlap: 0 }
        }

        var itemCols = Math.ceil(itemWidth / cellWidth)
        var itemRows = Math.ceil(itemHeight / cellHeight)

        if (row + itemRows > gridRows || col + itemCols > gridCols) {
            return { target: null, overlap: 0 }
        }

        var targetLeft = col * cellWidth
        var targetTop = row * cellHeight
        var targetRight = targetLeft + itemWidth
        var targetBottom = targetTop + itemHeight

        var overlapLeft = Math.max(itemLeft, targetLeft)
        var overlapRight = Math.min(itemLeft + itemWidth, targetRight)
        var overlapTop = Math.max(itemTop, targetTop)
        var overlapBottom = Math.min(itemTop + itemHeight, targetBottom)

        if (overlapRight > overlapLeft && overlapBottom > overlapTop) {
            var overlapArea = (overlapRight - overlapLeft) * (overlapBottom - overlapTop)
            var itemArea = itemWidth * itemHeight
            var overlapPercent = overlapArea / itemArea
            return { target: [row, col], overlap: overlapPercent }
        }

        return { target: null, overlap: 0 }
    }

    // Initialize drag operation and show initial highlight
    function startDrag(item, mouseX, mouseY) {
        if (!item || currentDragItem) {
            return false
        }
        if (isGridLocked || (item.itemData && item.itemData.isAnimating)) {
            return false
        }
        if (item.isMaximized || (item.itemData && item.itemData.fullscreen)) {
            return false
        }
        currentDragItem = item
        isProcessingAction = true

        if (gridLinesOverlay) {
            gridLinesOverlay.startDragMode()
        }

        if (dragHighlight && gridModel) {
            var currentRow = item.gridRow
            var currentCol = item.gridCol
            var itemCols = item.cols_cell || 1
            var itemRows = item.rows_cell || 1
            dragHighlight.updateHighlight(currentCol, currentRow, itemCols, itemRows, true, false)
        }

        dragStartRequested(item, mouseX, mouseY)
        return true
    }

    // Update drag highlight based on current item position
    function updateDrag(item, mouseX, mouseY) {
        if (isGridLocked || (item.itemData && item.itemData.isAnimating)) {
            return
        }
        if (item.isMaximized || (item.itemData && item.itemData.fullscreen)) {
            return
        }
        if (currentDragItem !== item) {
            return
        }

        if (gridModel && dragHighlight) {
            var cellWidth = root.width / gridModel.columns
            var cellHeight = root.height / gridModel.rows
            updateDragWithItemBounds(item, cellWidth, cellHeight)
        }

        dragUpdateRequested(item, mouseX, mouseY)
    }

    // Calculate and display drag highlight using unified decision logic
    function updateDragWithItemBounds(item, cellWidth, cellHeight) {
        var decision = calculateDragDecision(item, cellWidth, cellHeight)

        if (decision.action === "hide") {
            dragHighlight.hideHighlight()
            return
        }

        var highlightCols = item.itemData.cols_cell || 1
        var highlightRows = item.itemData.rows_cell || 1
        var highlightCol, highlightRow, isValid

        if (decision.action === "execute") {
            highlightCol = decision.targetCol
            highlightRow = decision.targetRow

            if (decision.isDifferentItem) {
                highlightCol = decision.targetItem.col
                highlightRow = decision.targetItem.row
                highlightCols = decision.targetItem.cols_cell || 1
                highlightRows = decision.targetItem.rows_cell || 1
            }

            isValid = true
        } else {
            highlightCol = item.gridCol
            highlightRow = item.gridRow
            isValid = false
        }

        if (highlightCol >= 0 && highlightRow >= 0 &&
            highlightCol + highlightCols <= gridModel.columns &&
            highlightRow + highlightRows <= gridModel.rows) {
            dragHighlight.updateHighlight(highlightCol, highlightRow, highlightCols, highlightRows, isValid, false)
        } else {
            dragHighlight.hideHighlight()
        }
    }

    // Legacy mouse-based drag update method
    function updateDragLegacy(item, mouseX, mouseY, cellWidth, cellHeight) {
        var clampedMouseX = Math.max(0, Math.min(mouseX, root.width - 1))
        var clampedMouseY = Math.max(0, Math.min(mouseY, root.height - 1))

        var targetCol = Math.floor(clampedMouseX / cellWidth)
        var targetRow = Math.floor(clampedMouseY / cellHeight)

        var targetItem = gridModel.getItemAtRowColAnyPosition(targetRow, targetCol)

        var highlightCols = 1
        var highlightRows = 1
        var highlightCol = targetCol
        var highlightRow = targetRow

        var isSameItem = targetItem && (targetItem === item.itemData)

        if (targetItem && !isSameItem) {
            highlightCols = targetItem.cols_cell || 1
            highlightRows = targetItem.rows_cell || 1
            highlightCol = targetItem.col
            highlightRow = targetItem.row
        } else {
            if (item.itemData && item.itemData.cols_cell !== undefined) {
                highlightCols = item.itemData.cols_cell
                highlightRows = item.itemData.rows_cell
            } else if (item.cols_cell !== undefined) {
                highlightCols = item.cols_cell
                highlightRows = item.rows_cell
            } else {
                highlightCols = 1
                highlightRows = 1
            }

            highlightCol = targetCol
            highlightRow = targetRow
        }

        var isValidRowCol = targetRow !== item.gridRow || targetCol !== item.gridCol
        var isWithinBounds = true

        var endCol = highlightCol + highlightCols - 1
        var endRow = highlightRow + highlightRows - 1

        if (endCol >= gridModel.columns || endRow >= gridModel.rows ||
            highlightCol < 0 || highlightRow < 0) {
            isWithinBounds = false
        }

        var isValid = isValidRowCol && isWithinBounds
        dragHighlight.updateHighlight(highlightCol, highlightRow, highlightCols, highlightRows, isValid, false)
    }

    // Finalize drag operation and execute move/swap if valid
    function endDrag(item, mouseX, mouseY) {
        if (!item || !gridModel || currentDragItem !== item) {
            return
        }
        if (item.isMaximized || (item.itemData && item.itemData.fullscreen)) {
            return
        }

        var cellWidth = root.width / gridModel.columns
        var cellHeight = root.height / gridModel.rows

        var finalTargetCol = item.gridCol
        var finalTargetRow = item.gridRow

        var result = endDragWithItemBounds(item, cellWidth, cellHeight)
        finalTargetCol = result.x
        finalTargetRow = result.y

        if (dragHighlight) {
            dragHighlight.hideHighlight()
        }

        if (gridLinesOverlay) {
            gridLinesOverlay.endDragMode()
        }

        currentDragItem = null
        isProcessingAction = false

        dragEndRequested(item, mouseX, mouseY)
        return Qt.point(finalTargetCol, finalTargetRow)
    }

    // Execute move or swap operation based on drag decision
    function endDragWithItemBounds(item, cellWidth, cellHeight) {
        var decision = calculateDragDecision(item, cellWidth, cellHeight)

        if (decision.action !== "execute") {
            if (item) item.isHovered = false;
            return Qt.point(item.gridCol, item.gridRow)
        }

        if (decision.isDifferentItem) {
            // Reset hover state for both items
            if (item) item.isHovered = false;
            if (decision.targetItem && decision.targetItem.qmlItem) decision.targetItem.qmlItem.isHovered = false;
            gridModel.swapItemsRowCol(item.gridRow, item.gridCol, decision.targetItem.row, decision.targetItem.col)
            return Qt.point(decision.targetItem.col, decision.targetItem.row)
        } else {
            if (item) item.isHovered = false;
            gridModel.moveItemRowCol(item.gridRow, item.gridCol, decision.targetRow, decision.targetCol)
            return Qt.point(decision.targetCol, decision.targetRow)
        }
    }

    // Legacy mouse-based drag end method
    function endDragLegacy(item, mouseX, mouseY, cellWidth, cellHeight) {
        var clampedMouseX = Math.max(0, Math.min(mouseX, root.width - 1))
        var clampedMouseY = Math.max(0, Math.min(mouseY, root.height - 1))
        var mouseTargetCol = Math.floor(clampedMouseX / cellWidth)
        var mouseTargetRow = Math.floor(clampedMouseY / cellHeight)

        var targetItem = gridModel.getItemAtRowColAnyPosition(mouseTargetRow, mouseTargetCol)
        var isSameItem = targetItem && (targetItem === item.itemData)

        var finalTargetCol = mouseTargetCol
        var finalTargetRow = mouseTargetRow

        if (targetItem && !isSameItem) {
            finalTargetCol = targetItem.col
            finalTargetRow = targetItem.row
        }

        var isValidMove = (finalTargetRow !== item.gridRow || finalTargetCol !== item.gridCol)

        if (isValidMove) {
            if (targetItem && !isSameItem) {
                gridModel.swapItemsRowCol(item.gridRow, item.gridCol, finalTargetRow, finalTargetCol)
            } else {
                gridModel.moveItemRowCol(item.gridRow, item.gridCol, finalTargetRow, finalTargetCol)
            }
        }

        return Qt.point(finalTargetCol, finalTargetRow)
    }

    // Cancel current drag operation and reset state
    function cancelDrag() {
        if (dragHighlight) {
            dragHighlight.hideHighlight()
        }

        if (gridLinesOverlay) {
            gridLinesOverlay.endDragMode()
        }

        currentDragItem = null
        isProcessingAction = false
    }

    // Get grid item at screen coordinates
    function getItemAtPosition(x, y) {
        if (!gridModel) return null

        var cellWidth = root.width / gridModel.columns
        var cellHeight = root.height / gridModel.rows
        var col = Math.floor(x / cellWidth)
        var row = Math.floor(y / cellHeight)

        if (row >= 0 && row < gridModel.rows && col >= 0 && col < gridModel.columns) {
            return gridModel.getItemAtRowColAnyPosition(row, col)
        }
        return null
    }

    // Convert mouse coordinates to grid row/column
    function mouseToGridRowCol(mouseX, mouseY) {
        if (!gridModel) return { row: 0, col: 0 }

        var cellWidth = root.width / gridModel.columns
        var cellHeight = root.height / gridModel.rows

        var col = Math.max(0, Math.min(Math.floor(mouseX / cellWidth), gridModel.columns - 1))
        var row = Math.max(0, Math.min(Math.floor(mouseY / cellHeight), gridModel.rows - 1))

        return { row: row, col: col }
    }

    // Update drag selection rectangle with throttling
    function updateDragSelection(startX, startY, endX, endY) {
        if (!gridModel) return

        var startRowCol = mouseToGridRowCol(startX, startY)
        var endRowCol = mouseToGridRowCol(endX, endY)

        pendingSelection = {
            startRow: startRowCol.row,
            startCol: startRowCol.col,
            endRow: endRowCol.row,
            endCol: endRowCol.col
        }

        if (!selectionUpdatePending) {
            selectionUpdatePending = true
            selectionThrottleTimer.start()
        }
    }

    // Finalize drag selection and apply to grid model
    function finalizeDragSelection(startX, startY, endX, endY) {
        if (!gridModel) return

        var startRowCol = mouseToGridRowCol(startX, startY)
        var endRowCol = mouseToGridRowCol(endX, endY)

        gridModel.selectRectangle(startRowCol.row, startRowCol.col, endRowCol.row, endRowCol.col)

        if (gridModel.getSelectedItems) {
            var selectedItems = gridModel.getSelectedItems()
        }
    }

    // Restore focus to GridActionHandler
    function refreshFocusAndHandlers() {
        root.forceActiveFocus()
    }

    // ✅ REMOVED: Duplicate testCtrlA method

    Component.onCompleted: {
        root.forceActiveFocus()
    }

    onActiveFocusChanged: {
        if (!activeFocus) {
            // Focus lost - will restore on next interaction
        }
    }

    // Background mouse area for focus recovery
    MouseArea {
        anchors.fill: parent
        acceptedButtons: Qt.NoButton
        hoverEnabled: true
        z: -1

        onClicked: {
            root.forceActiveFocus()
        }

        onPressed: {
            root.forceActiveFocus()
        }
    }

    onGridModelChanged: {
        if (gridModel) {
            root.forceActiveFocus()
        }
    }

    property bool isGridLocked: gridModel ? (gridModel.isAnimating || false) : false
}
