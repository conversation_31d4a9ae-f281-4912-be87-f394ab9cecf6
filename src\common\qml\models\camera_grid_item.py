from PySide6.QtCore import QObject, Property, Signal, Slot, QTimer
from src.common.qml.models.grid_item import GridItem
from src.common.qml.models.common_enum import CommonEnum
from typing import TYPE_CHECKING, List
from enum import IntEnum
import logging
import time
if TYPE_CHECKING:
    from src.common.model.camera_model import CameraModel
logger = logging.getLogger(__name__)

class StreamSwitchingConstants:
    # ✅ Percentage threshold for stream switching: > 30% of grid dimension → MAIN_STREAM
    MAIN_STREAM_THRESHOLD_PERCENTAGE = 30.0

    MAIN_STREAM_INDEX = 0
    SUB_STREAM_INDEX = 1
    SUB_STREAM_2_INDEX = 2

    AUTO_MODE = "AUTO"
    MAIN_FIXED_MODE = "MAIN_FIXED"
    SUB1_FIXED_MODE = "SUB1_FIXED"
    SUB2_FIXED_MODE = "SUB2_FIXED"

    FALLBACK_PRIORITY = {
        0: [1, 2],
        1: [2, 0],
        2: [1, 0],
    }
class CameraAnimationType(IntEnum):
    NONE = 0
    CURRENT = 1
    NEXT = 2

class CameraGridItem(GridItem):

    isPlayingChanged = Signal()
    cameraModelChanged = Signal()
    connectionStateChanged = Signal()
    cameraNameChanged = Signal()
    stateCamIconServerChanged = Signal()
    isNewlyAddedChanged = Signal()  # ✅ NEW: Signal for animation support
    isCurrentStreamTypeChanged = Signal()

    ptzSupportChanged = Signal()
    isPtzActiveChanged = Signal()
    isPtz3dActiveChanged = Signal()
    isDragZoomActiveChanged = Signal()
    ptzActivated = Signal(str)
    playerChanged = Signal(QObject)
    switchStreamTypeChanged = Signal(int, QObject)

    streamModeChanged = Signal()
    streamUrlsChanged = Signal()
    resizeCompleted = Signal(int, int)
    percentChanged = Signal()
    isVideoPlaybackModeChanged = Signal()  # ✅ NEW: Signal for video playback mode

    def __init__(self,row:int = 0,col:int = 0,rows_cell:int = 1,cols_cell:int = 1):
        super().__init__(row = row,col= col,rows_cell = rows_cell,cols_cell = cols_cell)

        import uuid
        self.uuid = str(uuid.uuid4())  # Unique identifier for this logical item
        self._itemType = "camera"
        self._state_cam_icon_server: str = self.getStateCamIconServer()
        self._cameraModel: 'CameraModel' = None
        self._cameraName: str = ""
        self._isPlaying: bool = False
        self._connectionState: str = "connecting"
        self._isNewlyAdded: bool = False

        self._isPtzActive: bool = False
        self._isPtz3dActive: bool = False
        self._isDragZoomActive: bool = False
        self._activePtzMode: str = "none"
        self._isCurrentStreamType: int = CommonEnum.StreamType.MAIN_STREAM
        self._streamMode: str = StreamSwitchingConstants.AUTO_MODE
        self._streamUrls: List[str] = ["", "", ""]
        self._isVideoPlaybackMode: bool = False  # Flag to track video playback mode
        self._percent = 0

    @Property(int, notify=isCurrentStreamTypeChanged)
    def isCurrentStreamType(self):
        """Current stream type property"""
        return self._isCurrentStreamType

    def setVideoPlaybackMode(self, enabled: bool):
        """Set video playback mode - no longer triggers automatic stream switching"""
        if self._isVideoPlaybackMode != enabled:
            self._isVideoPlaybackMode = enabled
            self.isVideoPlaybackModeChanged.emit()

    @Property(bool, notify=isVideoPlaybackModeChanged)
    def isVideoPlaybackMode(self) -> bool:
        """Check if currently in video playback mode"""
        return self._isVideoPlaybackMode

    @Property(str, notify=streamModeChanged)
    def streamMode(self):
        """Stream mode property (AUTO/MAIN_FIXED/SUB1_FIXED/SUB2_FIXED)"""
        return self._streamMode

    @streamMode.setter
    def streamMode(self, value: str):
        """Set stream mode - no longer triggers automatic stream switching"""
        if self._streamMode != value:
            self._streamMode = value
            self.streamModeChanged.emit()
            logger.debug(f"🚀 [STREAM_MODE] Stream mode changed to: {value}")

            # Don't switch stream when in video playback mode
            if not self._isVideoPlaybackMode:
                self.switchStreamTypeChanged.emit(self.getStreamTypeForCurrentMode(), None)
            else:
                logger.debug(f"🚀 [STREAM_MODE] Skipped stream switching - in video playback mode")

    @Property('QStringList', notify=streamUrlsChanged)
    def streamUrls(self):
        """Stream URLs property [main_url, sub_url, sub2_url]"""
        return self._streamUrls

    @streamUrls.setter
    def streamUrls(self, value: List[str]):
        """Set stream URLs and trigger re-evaluation"""
        if self._streamUrls != value:
            self._streamUrls = value
            self.streamUrlsChanged.emit()

    @Slot(int, int, int, int, int, int, int, int)
    def handleResizeComplete(self, old_rows: int, old_cols: int, new_rows: int, new_cols: int,
                            final_width: int, final_height: int, total_grid_rows: int, total_grid_cols: int):
        """
        ✅ PHASE 3: Handle resize completion with stream switch + update_resize
        Called when resize release occurs with row/col changes

        Args:
            old_rows: Previous row span
            old_cols: Previous column span
            new_rows: New row span
            new_cols: New column span
            final_width: Final pixel width
            final_height: Final pixel height
            total_grid_rows: Total grid rows
            total_grid_cols: Total grid columns
        """
        logger.debug(f"🚀 [RESIZE] Resize complete: {old_cols}x{old_rows} → {new_cols}x{new_rows}, size: {final_width}x{final_height}, grid: {total_grid_cols}x{total_grid_rows}")

        # 1. Switch stream if grid dimensions changed
        if old_rows != new_rows or old_cols != new_cols:
            new_stream_type = self.calculateStreamTypeForGridSize(new_rows, new_cols, total_grid_rows, total_grid_cols)
            if new_stream_type != self._isCurrentStreamType:
                logger.debug(f"🚀 [RESIZE] Stream switch: {self._isCurrentStreamType} → {new_stream_type}")
                # Simple signal emission - FrameModel will handle appropriately
                self.switchStreamTypeChanged.emit(new_stream_type, None)

        # 2. Always emit resize signal for player update (handled by FrameModel)
        self.resizeCompleted.emit(final_width, final_height)

    @Slot(int, int)
    def handleGridExpansion(self, new_grid_rows: int, new_grid_cols: int):
        """
        ✅ PHASE 4: Handle grid expansion - switch stream if grid > 2x2
        Called when grid expansion occurs (Ctrl+Wheel)

        Args:
            new_grid_rows: New total grid rows
            new_grid_cols: New total grid columns
        """
        logger.debug(f"🚀 [GRID_EXPANSION] Grid expanded to {new_grid_cols}x{new_grid_rows}")

        # 1. Check if grid becomes > 2x2 (affects stream type)
        if new_grid_rows > 2 or new_grid_cols > 2:
            # Calculate new stream type for current item position
            current_rows = getattr(self, '_rows_cell', 1)
            current_cols = getattr(self, '_cols_cell', 1)

            new_stream_type = self.calculateStreamTypeForGridSize(current_rows, current_cols, new_grid_rows, new_grid_cols)
            if new_stream_type != self._isCurrentStreamType:
                logger.debug(f"🚀 [GRID_EXPANSION] Stream switch: {self._isCurrentStreamType} → {new_stream_type}")

                # Don't switch stream when in video playback mode
                if not self._isVideoPlaybackMode:
                    # Simple signal emission - FrameModel will handle appropriately
                    self.switchStreamTypeChanged.emit(new_stream_type, None)
                else:
                    logger.debug(f"🚀 [GRID_EXPANSION] Skipped stream switching - in video playback mode")

        # 2. Always emit resize signal for player update after grid layout
        self.resizeCompleted.emit(self._width, self._height)



    def getStreamTypeForCurrentMode(self) -> int:
        """
        ✅ PHASE 5: Get stream type for current mode (replaces optimalStreamIndex)
        Used by FrameModel when direct grid info is not available
        """
        # Video playback mode takes priority
        if self._isVideoPlaybackMode:
            return CommonEnum.StreamType.VIDEO_STREAM

        # Fixed mode logic
        if self._streamMode != "AUTO":
            mode_map = {
                "MAIN_FIXED": CommonEnum.StreamType.MAIN_STREAM,
                "SUB1_FIXED": CommonEnum.StreamType.SUB_STREAM,
                "SUB2_FIXED": CommonEnum.StreamType.SUB_STREAM2
            }
            return mode_map.get(self._streamMode, CommonEnum.StreamType.MAIN_STREAM)

        # Auto mode - simplified fallback logic
        # Use current cell dimensions for basic decision
        current_rows = getattr(self, '_rows_cell', 1)
        current_cols = getattr(self, '_cols_cell', 1)

        # Basic logic: items spanning 2+ cells → MAIN_STREAM
        if current_rows >= 2 or current_cols >= 2:
            return CommonEnum.StreamType.MAIN_STREAM
        else:
            return CommonEnum.StreamType.SUB_STREAM

    def calculateStreamTypeForGridSize(self, rows: int, cols: int, total_rows: int, total_cols: int) -> int:
        """
        ✅ Calculate optimal stream type based on percentage of grid occupied

        Args:
            rows: Number of rows the item spans
            cols: Number of columns the item spans
            total_rows: Total grid rows
            total_cols: Total grid columns

        Returns:
            Stream type (CommonEnum.StreamType)
        """
        # Video playback mode takes priority
        if self._isVideoPlaybackMode:
            return CommonEnum.StreamType.VIDEO_STREAM

        # Fixed mode logic
        if self._streamMode != "AUTO":
            mode_map = {
                "MAIN_FIXED": CommonEnum.StreamType.MAIN_STREAM,
                "SUB1_FIXED": CommonEnum.StreamType.SUB_STREAM,
                "SUB2_FIXED": CommonEnum.StreamType.SUB_STREAM2
            }
            return mode_map.get(self._streamMode, CommonEnum.StreamType.MAIN_STREAM)

        # Auto mode - percentage-based logic
        if total_rows <= 0 or total_cols <= 0:
            # Fallback to cell-based logic if invalid grid info
            logger.warning(f"🚨 [FALLBACK] Invalid grid info: {total_cols}x{total_rows}, using cell-based logic")
            return CommonEnum.StreamType.MAIN_STREAM if (rows >= 2 or cols >= 2) else CommonEnum.StreamType.SUB_STREAM

        # Calculate percentage of grid occupied
        row_percentage = (rows / total_rows) * 100
        col_percentage = (cols / total_cols) * 100

        # Use the larger percentage for decision
        max_percentage = max(row_percentage, col_percentage)

        # Use threshold from constants
        logger.debug(f"🚀 [PERCENTAGE] Item {cols}x{rows} in grid {total_cols}x{total_rows}: row={row_percentage:.1f}%, col={col_percentage:.1f}%, max={max_percentage:.1f}%")

        if max_percentage > StreamSwitchingConstants.MAIN_STREAM_THRESHOLD_PERCENTAGE:
            return CommonEnum.StreamType.MAIN_STREAM
        else:
            return CommonEnum.StreamType.SUB_STREAM

    @Slot(str)
    def setStreamModeFromQML(self, mode: str):
        """Set stream mode from QML interface"""
        valid_modes = [StreamSwitchingConstants.AUTO_MODE,
                      StreamSwitchingConstants.MAIN_FIXED_MODE,
                      StreamSwitchingConstants.SUB1_FIXED_MODE,
                      StreamSwitchingConstants.SUB2_FIXED_MODE]
        if mode in valid_modes:
            self.streamMode = mode


    @isCurrentStreamType.setter
    def isCurrentStreamType(self, value: int):
        if self._isCurrentStreamType != value:
            self._isCurrentStreamType = value
            self.isCurrentStreamTypeChanged.emit()

    @Property(str, notify=stateCamIconServerChanged)
    def stateCamIconServer(self):
        """State camera icon server property"""
        return self._state_cam_icon_server

    @stateCamIconServer.setter
    def stateCamIconServer(self, value: str ):
        if self._state_cam_icon_server != value:
            self._state_cam_icon_server = value
            self.stateCamIconServerChanged.emit()

    @Property(str, notify=cameraNameChanged)
    def cameraName(self):
        """Camera name property"""
        return self._cameraName

    @cameraName.setter
    def cameraName(self, value: str):
        if self._cameraName != value:
            self._cameraName = value
            self.cameraNameChanged.emit()

    @Property(bool, notify=isPlayingChanged)
    def isPlaying(self):
        """Video playing state property"""
        return self._isPlaying

    @isPlaying.setter
    def isPlaying(self, value: bool):
        self._isPlaying = value
        self.isPlayingChanged.emit()

    @Property('QVariant', notify=cameraModelChanged)
    def cameraModel(self):
        """Camera model reference property"""
        return self._cameraModel

    @cameraModel.setter
    def cameraModel(self, value: 'CameraModel'):
        if self._cameraModel != value:
            self._cameraModel = value
            self.cameraModelChanged.emit()

    @Property(bool, notify=isNewlyAddedChanged)
    def isNewlyAdded(self):
        """Flag indicating if this item was just added (for animation)"""
        return self._isNewlyAdded

    @isNewlyAdded.setter
    def isNewlyAdded(self, value: bool):
        if self._isNewlyAdded != value:
            self._isNewlyAdded = value
            self.isNewlyAddedChanged.emit()


    def getStateCamIconServer(self, cameraModel: 'CameraModel' = None):
        if cameraModel is not None:
            from src.common.qml.models.common_enum import CommonEnum
            if cameraModel.state_merged == CommonEnum.CameraState.CONNECTED_REC_PIN:
                return "qrc:/src/assets/treeview_and_menu_treeview/rec_pin.svg"
            elif cameraModel.state_merged == CommonEnum.CameraState.CONNECTED_REC_UNPIN:
                return "qrc:/src/assets/treeview_and_menu_treeview/rec_unpin.svg"
            elif cameraModel.state_merged == CommonEnum.CameraState.CONNECTED_NOREC_PIN:
                return "qrc:/src/assets/treeview_and_menu_treeview/norec_pin.svg"
            elif cameraModel.state_merged == CommonEnum.CameraState.CONNECTED_NOREC_UNPIN:
                return "qrc:/src/assets/treeview_and_menu_treeview/norec_unpin.svg"
            elif cameraModel.state_merged == CommonEnum.CameraState.DISCONNECTED_REC_PIN:
                return "qrc:/src/assets/treeview_and_menu_treeview/rec_pin.svg"
            elif cameraModel.state_merged == CommonEnum.CameraState.DISCONNECTED_REC_UNPIN:
                return "qrc:/src/assets/treeview_and_menu_treeview/rec_unpin.svg"
            elif cameraModel.state_merged == CommonEnum.CameraState.DISCONNECTED_NOREC_PIN:
                return "qrc:/src/assets/treeview_and_menu_treeview/norec_pin.svg"
            else:  # DISCONNECTED_NOREC_UNPIN
                return "qrc:/src/assets/treeview_and_menu_treeview/norec_unpin.svg"
        return "qrc:/src/assets/treeview_and_menu_treeview/norec_unpin.svg"  # Default return value if no camera model

    @Property(str, notify=connectionStateChanged)
    def connectionState(self):
        """Connection state for overlay display - MVVM property"""
        return self._connectionState

    @connectionState.setter
    def connectionState(self, value: str):
        if self._connectionState != value:
            self._connectionState = value
            self.connectionStateChanged.emit()
            logger.debug(f"Connection state changed to: {value}")

    @Property(str, notify=percentChanged)
    def percent(self):
        return self._percent

    @percent.setter
    def percent(self, value: str):
        if self._percent != value:
            self._percent = value
            self.percentChanged.emit()


    # ✅ PTZ STATE PROPERTIES: Add PTZ button state management
    @Property(bool, notify=isPtzActiveChanged)
    def isPtzActive(self):
        """PTZ panel active state property"""
        return self._isPtzActive

    @isPtzActive.setter
    def isPtzActive(self, value: bool):
        if self._isPtzActive != value:
            self._isPtzActive = value
            self.isPtzActiveChanged.emit()
            logger.debug(f"🎮 [PTZ_STATE] PTZ panel active changed to: {value}")

    @Property(bool, notify=isPtz3dActiveChanged)
    def isPtz3dActive(self):
        """PTZ 3D control active state property"""
        return self._isPtz3dActive

    @isPtz3dActive.setter
    def isPtz3dActive(self, value: bool):
        if self._isPtz3dActive != value:
            self._isPtz3dActive = value
            self.isPtz3dActiveChanged.emit()
            logger.debug(f"🎮 [PTZ_STATE] PTZ 3D active changed to: {value}")

    @Property(bool, notify=isDragZoomActiveChanged)
    def isDragZoomActive(self):
        """Drag zoom control active state property"""
        return self._isDragZoomActive

    @isDragZoomActive.setter
    def isDragZoomActive(self, value: bool):
        if self._isDragZoomActive != value:
            self._isDragZoomActive = value
            self.isDragZoomActiveChanged.emit()
            logger.debug(f"🎮 [PTZ_STATE] Drag zoom active changed to: {value}")

    @Property(str, notify=ptzActivated)
    def activePtzMode(self):
        """Current active PTZ mode property"""
        return self._activePtzMode

    @activePtzMode.setter
    def activePtzMode(self, value: str):
        if self._activePtzMode != value:
            self._activePtzMode = value
            self.ptzActivated.emit(value)
            logger.debug(f"🎮 [PTZ_STATE] Active PTZ mode changed to: {value}")

    @Slot(result=str)
    def getCameraId(self) -> str:
        """Get camera ID"""
        return self._cameraModel.id if self._cameraModel else ""

    @Slot(result=str)
    def getCameraName(self) -> str:
        """Get camera name"""
        return self._cameraModel.name if self._cameraModel else ""

    @Slot(result=bool)
    def supportsPTZ(self) -> bool:
        """Check if camera supports PTZ"""
        if self._cameraModel:
            camera_id = self.getCameraId()
            camera_name = self.getCameraName()

            if hasattr(self._cameraModel, 'supports_ptz') and callable(getattr(self._cameraModel, 'supports_ptz')):
                ptz_support = self._cameraModel.supports_ptz()
                return ptz_support

            if hasattr(self._cameraModel, 'data') and self._cameraModel.data:
                ptz_caps = self._cameraModel.data.get('ptzCap', [])
                ptz_support = ptz_caps is not None and len(ptz_caps) > 0
                return ptz_support

            if hasattr(self._cameraModel, 'ptzCap'):
                ptz_caps = getattr(self._cameraModel, 'ptzCap', [])
                ptz_support = ptz_caps is not None and len(ptz_caps) > 0
                return ptz_support

        return False
    
    @Slot(result=bool)
    def supportsZoomOnly(self) -> bool:
        """Check if camera supports PTZ but only has zoom capability (no pan/tilt)"""
        if not self._cameraModel:
            return False
            
        if hasattr(self._cameraModel, 'supports_zoom_only') and callable(getattr(self._cameraModel, 'supports_zoom_only')):
            return self._cameraModel.supports_zoom_only()

        # Fallback: Check ptzCap manually
        if hasattr(self._cameraModel, 'data') and self._cameraModel.data:
            ptz_caps = self._cameraModel.data.get('ptzCap', [])
            if not ptz_caps:
                return False
            
            zoom_caps = ['continuous_zoom', 'relative_zoom', 'absolute_zoom', 'zoom_limits']
            pan_tilt_caps = ['continuous_pan_tilt', 'relative_pan_tilt', 'absolute_pan_tilt', 'pan_tilt_limits']
            
            has_zoom = any(cap in ptz_caps for cap in zoom_caps)
            has_pan_tilt = any(cap in ptz_caps for cap in pan_tilt_caps)
            
            return has_zoom and not has_pan_tilt

        return False

    @Slot(str)
    def activatePtzButton(self, buttonType: str):
        """Activate PTZ button with specific type and manage state"""
        if buttonType == "ptz":
            self.isPtzActive = True
            self.isPtz3dActive = False
            self.isDragZoomActive = False
            self.activePtzMode = "ptz"

        elif buttonType == "ptz3d":
            self.isPtzActive = False
            self.isPtz3dActive = True
            self.isDragZoomActive = False
            self.activePtzMode = "ptz3d"

        elif buttonType == "dragZoom":
            self.isPtzActive = False
            self.isPtz3dActive = False
            self.isDragZoomActive = True
            self.activePtzMode = "dragZoom"

        else:
            self.isPtzActive = False
            self.isPtz3dActive = False
            self.isDragZoomActive = False
            self.activePtzMode = "none"

    @Slot()
    def deactivateAllPtz(self):
        """Deactivate all PTZ controls"""
        self.activatePtzButton("none")

    @Slot(result=bool)
    def hasActivePtz(self) -> bool:
        """Check if any PTZ control is currently active"""
        return self.isPtzActive or self.isPtz3dActive or self.isDragZoomActive

    @Slot(result=str)
    def getActivePtzMode(self) -> str:
        """Get the currently active PTZ mode"""
        return self.activePtzMode

    def isConnected(self) -> bool:
        """Check if camera is connected"""
        if self._cameraModel:
            camera_state = getattr(self._cameraModel, 'state_merged', 7)
            return camera_state in [0, 1, 2, 3]
        return False

    def getStreamUrl(self) -> str:
        """Get camera stream URL"""
        if self._cameraModel:
            return getattr(self._cameraModel, 'stream_url', "")
        return ""

    def isValid(self) -> bool:
        """Validate camera grid item data"""
        base_valid = super().isValid()
        camera_valid = self._cameraModel is not None
        return base_valid and camera_valid

    def __str__(self):
        """String representation for debugging"""
        camera_name = self.getCameraName()
        return f"CameraGridItem(row={self._row}, col={self._col}, camera='{camera_name}', playing={self._isPlaying}, state={self._connectionState})"
