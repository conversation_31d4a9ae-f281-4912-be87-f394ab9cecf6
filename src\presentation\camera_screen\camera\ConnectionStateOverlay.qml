import QtQuick
import QtQuick.Controls
import QtQuick.Layouts

/**
 * ConnectionStateOverlay.qml - Reusable component cho connection state display
 *
 * CHỨC NĂNG CHÍNH:
 * - Hi<PERSON><PERSON> thị trạng thái kết nối camera (connecting, disconnected, connected)
 * - Layout tối ưu với ColumnLayout
 * - Animation cho loading state
 * - Responsive design theo kích thước parent
 * - Single source of truth cho connection state
 *
 * KIẾN TRÚC:
 * - Centralized state management
 * - Optimized layout system
 * - Dynamic z-index từ model
 * - Reusable component pattern
 */
Item {
    id: root

    // Required properties
    property var itemData: null
    property bool isDarkTheme: true
    property int baseZIndex: 20


    // Component visibility
    visible: {
        if (root.itemData) {
            return root.itemData.connectionState !== "started"
        }
        return false
    }

    // Dynamic sizing based on parent
    anchors.centerIn: parent
    width: Math.max(120, Math.min(200, parent.width * 0.6))
    height: Math.max(80, Math.min(120, parent.height * 0.4))
    z: baseZIndex

    // // Background overlay cho better visibility
    // Rectangle {
    //     id: backgroundOverlay
    //     anchors.fill: parent
    //     color: root.isDarkTheme ? "#80000000" : "#80ffffff"
    //     radius: 8
    //     opacity: 0.8

    //     // Subtle border cho better definition
    //     border.width: 1
    //     border.color: root.isDarkTheme ? "#40ffffff" : "#40000000"
    // }

    // Main content layout
    ColumnLayout {
        id: contentLayout
        anchors.centerIn: parent
        spacing: 8
        width: parent.width - 16  // Padding

        // Row chứa icon và text trạng thái
        RowLayout {
            id: rowStatus
            Layout.alignment: Qt.AlignHCenter
            Layout.fillWidth: true
            spacing: 8

            // Hiệu ứng loading 3 bước khi connecting
            Item {
                id: loadingIcons
                width: Math.max(20, Math.min(32, root.width / 8))
                height: width
                visible: root.itemData && root.itemData.connectionState === "connecting"

                property int currentStep: 1

                Timer {
                    id: loadingTimer
                    interval: 300
                    running: loadingIcons.visible
                    repeat: true
                    onTriggered: {
                        loadingIcons.currentStep = loadingIcons.currentStep % 3 + 1
                    }
                }

                // Loading1
                Image {
                    anchors.fill: parent
                    source: "qrc:/src/assets/state/Loading1.svg"
                    opacity: loadingIcons.currentStep === 1 ? 1.0 : 0.3
                    Behavior on opacity { NumberAnimation { duration: 150 } }
                }
                // Loading2
                Image {
                    anchors.fill: parent
                    source: "qrc:/src/assets/state/Loading2.svg"
                    opacity: loadingIcons.currentStep === 2 ? 1.0 : 0.3
                    Behavior on opacity { NumberAnimation { duration: 150 } }
                }
                // Loading3
                Image {
                    anchors.fill: parent
                    source: "qrc:/src/assets/state/Loading3.svg"
                    opacity: loadingIcons.currentStep === 3 ? 1.0 : 0.3
                    Behavior on opacity { NumberAnimation { duration: 150 } }
                }
            }

            // Icon disconnect khi disconnected
            Image {
                id: disconnectIcon
                width: Math.max(20, Math.min(32, root.width / 8))
                height: width
                fillMode: Image.PreserveAspectFit
                visible: root.itemData && root.itemData.connectionState === "disconnected"
                source: "qrc:/src/assets/state/disconnectHover.svg"
            }

            // Status text
            Text {
                id: statusText
                Layout.alignment: Qt.AlignVCenter
                text: {
                    if (root.itemData) {
                        if (root.itemData.connectionState === "disconnected") {
                            return qsTr("Disconnected")
                        } else if (root.itemData.connectionState === "connecting") {
                            return qsTr("Connecting")
                        } else if (root.itemData.connectionState === "buffering") {
                            return root.itemData.percent
                        }
                    }
                    return ""
                }
                font.pixelSize: Math.max(12, Math.min(16, root.width / 12))
                font.bold: true
                color: root.itemData && root.itemData.connectionState === "connecting" ? "#F6BE00"
                    : root.itemData && root.itemData.connectionState === "buffering" ? "#F6BE00"
                    : root.itemData && root.itemData.connectionState === "disconnected" ? "#ED4845"
                    : "white"                
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                wrapMode: Text.WordWrap
                maximumLineCount: 2
                elide: Text.ElideRight
                opacity: text !== "" ? 1.0 : 0.0
                Behavior on opacity {
                    NumberAnimation { duration: 200 }
                }
            }
        }
    }

    // Component lifecycle
    Component.onCompleted: {
    }

    Component.onDestruction: {
    }
}
