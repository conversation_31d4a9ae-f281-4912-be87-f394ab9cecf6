import QtQuick 2.15
import QtQuick.Controls 2.15
import models 1.0

/**
 * QML component đơn giản chỉ chứa FrameModel
 * Sử dụng cho AI zone dialog
 */
Rectangle {
    id: root
    width: 640
    height: 360
    color: "#1a1a1a"
    border.color: "#333333"
    border.width: 1

    FrameModel {
        id: frameModel
        objectName: "frameModel"
        anchors.fill: parent
        onItemDataChanged: {
            if (itemData) {
                itemData.isPlaying = true
            }
        }
        // Debug: Thêm background để thấy FrameModel
        // color: "transparent"
    }

    // Status overlay để debug
    Rectangle {
        id: statusOverlay
        anchors.centerIn: parent
        width: 200
        height: 60
        color: "#80000000"
        radius: 8
        visible: frameModel.frameCount === 0

        Text {
            anchors.centerIn: parent
            text: "Connecting..."
            color: "white"
            font.pixelSize: 14
        }
    }


}
