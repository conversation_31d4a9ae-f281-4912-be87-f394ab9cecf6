import enum
import sys

from PySide6.QtWidgets import <PERSON>A<PERSON><PERSON>, QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QDialog
from PySide6.QtCore import Qt, QSize,QSize, Qt, Signal, QPropertyAnimation, QEasingCurve, Qt, QParallelAnimationGroup, QRect, QPoint
from PySide6.QtGui import Qt, QIcon
from src.styles.style import Style
import resources_rc
from src.common.controller.main_controller import main_controller

class FooterType(enum.Enum):
    NONE_TYPE = 0
    SAVE_CANCEL = 1
    SAVE_CANCEL_DELETE = 2
    SAVE = 3
    CANCEL = 4
    DELETE = 5
    UPDATE_CANCEL = 6
    UPDATE_CANCEL_DELETE = 7
    CREATE_CANCEL = 8
    CLOSE = 9
    CONNECT = 10
    OK_CANCEL = 11

class NewBaseDialog(QDialog):
    save_update_signal = Signal()

    def __init__(self, parent=None, title=None, content_widget=None, footer_type=FooterType.SAVE_CANCEL, width_dialog=None,
                 min_height_dialog=None, max_height_dialog=None):
        super().__init__(parent)
        self.platform = sys.platform
        self.title = title
        self.footer_type = footer_type
        self.content_widget = content_widget
        self.animation_duration = 300  # Duration of the animation in milliseconds
        self.setModal(False)
        self.setWindowFlag(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.set_up_base_ui()
        self.setup_stylesheet()
        if width_dialog is not None:
            self.title_bar.setFixedWidth(width_dialog)
            self.footer.setFixedWidth(width_dialog)
            self.setFixedWidth(width_dialog)
        if min_height_dialog is not None:
            self.setMinimumHeight(min_height_dialog)

        if max_height_dialog is not None:
            self.setMaximumHeight(max_height_dialog)

    def set_up_base_ui(self):
        self.title_bar = TitleBarWidget(self, self.title)
        # close dialog
        self.title_bar.close_dialog.connect(self.close)
        self.footer = FooterDialog(footer_type=self.footer_type)
        self.footer.close_cancel_signal.connect(self.close)
        self.footer.save_update_signal.connect(lambda: (self.save_update_signal.emit()))

        layout_main = QVBoxLayout()
        layout_main.setContentsMargins(0, 0, 0, 0)
        layout_main.setSpacing(0)
        layout_main.addWidget(self.title_bar, 5)

        if self.content_widget:
            self.content_widget.setStyleSheet(f'''
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
            ''')
            layout_main.addWidget(self.content_widget, 90)
        layout_main.addWidget(self.footer, 5)

        self.setLayout(layout_main)

    def setup_stylesheet(self):
        self.setStyleSheet(
            f'''
                QDialog {{
                    background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                    color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                    
                }}
                QWidget{{
                    background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                    color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                }}
            '''
        )

    def disable_ui(self, disable):
        self.title_bar.setDisabled(disable)
        if self.footer is not None:
            self.footer.setDisabled(disable)

    def keyPressEvent(self, event):
        if event.key() in (Qt.Key_Return, Qt.Key_Enter):
            event.ignore()  # Ignore the event to prevent closing the dialog
        else:
            super().keyPressEvent(event)

    def showEvent(self, event):
        # hủy đăng ký lắng nghe phím tắt khi show Dialog
        if main_controller.key_filter is not None:
            QApplication.instance().removeEventFilter(main_controller.key_filter)
        if self.platform not in ["darwin", "linux"]:
            self.fade_in()
        super().showEvent(event)

    def closeEvent(self, event):
        # đăng ký lắng nghe phím tắt khi show Dialog
        if main_controller.key_filter is not None:
            QApplication.instance().installEventFilter(main_controller.key_filter)

    def close(self):
        my_super = super()
        if self.platform not in ["darwin", "linux"]:
            def close_dialog():
                return my_super.close()
            self.fade_out(close_dialog)
        else:
            my_super.close()

    def fade_in(self):
        self.setWindowOpacity(0)

        # Define the starting and ending geometries for the zoom effect
        full_geometry = self.geometry()
        center = full_geometry.center()

        small_size = QSize(int(full_geometry.width()), int(full_geometry.height() * 0.4))
        small_bottom_left = QPoint(center.x() - small_size.width() // 2 + 2, full_geometry.bottom() - small_size.height())
        small_geometry = QRect(small_bottom_left, small_size)

        self.opacity_animation = QPropertyAnimation(self, b"windowOpacity")
        self.opacity_animation.setDuration(self.animation_duration)
        self.opacity_animation.setStartValue(0)
        self.opacity_animation.setEndValue(1)
        self.opacity_animation.setEasingCurve(QEasingCurve.OutCubic)

        self.zoom_animation = QPropertyAnimation(self, b"geometry")
        self.zoom_animation.setDuration(self.animation_duration)
        self.zoom_animation.setStartValue(small_geometry)
        self.zoom_animation.setEndValue(full_geometry)
        self.zoom_animation.setEasingCurve(QEasingCurve.OutCubic)

        self.animation_group = QParallelAnimationGroup()
        self.animation_group.addAnimation(self.opacity_animation)
        self.animation_group.addAnimation(self.zoom_animation)
        self.animation_group.start()

    def fade_out(self, callback):
        if not self.isVisible():
            callback()  # If already hidden, call the callback immediately
            return

        def safe_callback():
            if hasattr(self, '_is_destroyed') and self._is_destroyed:
                print("Dialog already destroyed; skipping callback.")
            else:
                callback()

        self.destroyed.connect(lambda: setattr(self, '_is_destroyed', True))

        full_geometry = self.geometry()
        center = full_geometry.center()

        small_size = QSize(int(full_geometry.width()), int(full_geometry.height() * 0.4))
        small_bottom_left = QPoint(center.x() - small_size.width() // 2 + 2,
                                   full_geometry.bottom() - small_size.height())
        small_geometry = QRect(small_bottom_left, small_size)

        self.opacity_animation = QPropertyAnimation(self, b"windowOpacity")
        self.opacity_animation.setDuration(self.animation_duration)
        self.opacity_animation.setStartValue(1)  # Start with full opacity
        self.opacity_animation.setEndValue(0)  # End with transparent
        self.opacity_animation.setEasingCurve(QEasingCurve.InCubic)

        self.zoom_animation = QPropertyAnimation(self, b"geometry")
        self.zoom_animation.setDuration(self.animation_duration)
        self.zoom_animation.setStartValue(full_geometry)  # Start with full size
        self.zoom_animation.setEndValue(small_geometry)  # End with small size
        self.zoom_animation.setEasingCurve(QEasingCurve.InCubic)

        self.animation_group = QParallelAnimationGroup()
        self.animation_group.addAnimation(self.opacity_animation)
        self.animation_group.addAnimation(self.zoom_animation)
        self.animation_group.finished.connect(safe_callback)
        self.animation_group.start()


class TitleBarWidget(QWidget):
    close_dialog = Signal()

    def __init__(self, parent=None, title=None):
        super().__init__(parent)
        # layout
        self.title_bar_widget = QWidget()
        self.title_bar_widget.setObjectName("title_bar")
        # set background
        self.title_bar_widget.setStyleSheet(f'''
                        QWidget#title_bar {{
                            background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                            border-top-left-radius: 10px;
                            border-top-right-radius: 10px;
                            border-top: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                            border-left: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                            border-right: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                        }}
                    ''')
        self.title_bar_layout = QHBoxLayout()
        # event name
        self.title_name_label = QLabel(title)
        self.title_name_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.title_name_label.setStyleSheet(f"""
            color: {main_controller.get_theme_attribute("Color", "text_color_all_app")}; 
            font-size: {Style.Size.body_strong}px; 
            font-weight: 600; 
            background-color: {main_controller.get_theme_attribute("Color", "main_background")}
        """)

        self.close_button = ButtonCloseDialog()
        self.close_button.setIconSize(QSize(24, 24))
        self.close_button.setFixedSize(24, 24)
        self.close_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {main_controller.get_theme_attribute("Color", "main_background")}; 
                border: None
                }}
        """)
        self.close_button.clicked.connect(self.close_dialog.emit)
        # add widget
        self.title_bar_layout.addWidget(self.title_name_label, 90)
        self.title_bar_layout.addWidget(self.close_button, 10)
        self.title_bar_widget.setLayout(self.title_bar_layout)
        self.layout = QVBoxLayout()
        self.layout.setSpacing(0)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.addWidget(self.title_bar_widget)
        self.setLayout(self.layout)


class FooterDialog(QWidget):
    close_cancel_signal = Signal()
    save_update_signal = Signal()

    def __init__(self, parent=None, footer_type=FooterType.SAVE_CANCEL):
        super().__init__(parent)
        if footer_type == FooterType.SAVE_CANCEL:
            btn_close_title = self.tr('Cancel')
            btn_save_title = self.tr('Save')
        elif footer_type == FooterType.UPDATE_CANCEL:
            btn_close_title = self.tr('Cancel')
            btn_save_title = self.tr('Update')
        elif footer_type == FooterType.CREATE_CANCEL:
            btn_close_title = self.tr('Cancel')
            btn_save_title = self.tr('Create')
        elif footer_type == FooterType.CLOSE:
            btn_close_title = self.tr('Close')
            btn_save_title = self.tr('Create')
        elif footer_type == FooterType.CONNECT:
            btn_close_title = self.tr('Cancel')
            btn_save_title = self.tr('Connect')
        elif footer_type == FooterType.OK_CANCEL:
            btn_close_title = self.tr('Cancel')
            btn_save_title = self.tr('Ok') 
        else:
            btn_close_title = self.tr('Cancel')
            btn_save_title = self.tr('Save')

        self.btn_close_title = btn_close_title
        self.btn_save_title = btn_save_title
        # layout
        self.footer_widget = QWidget()
        self.footer_widget.setObjectName("footer_widget")
        # set background
        self.footer_widget.setStyleSheet(f'''
                QWidget#footer_widget {{
                    background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                    border-bottom: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                    border-left: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                    border-right: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                    border-bottom-left-radius: 10px;
                    border-bottom-right-radius: 10px
                }}
            ''')

        self.footer_layout = QHBoxLayout()
        self.footer_layout.setAlignment(Qt.AlignmentFlag.AlignRight)
        if footer_type != FooterType.CONNECT:
            self.button_close = QPushButton(self.btn_close_title)
            # Style nút Hủy giống btn_connect ở trạng thái connect (disconnect)
            btn_cancel_style = f'''
                QPushButton{{
                    background-color: transparent;
                    color: {main_controller.get_theme_attribute("Color", "text")};
                    font-size: 14px;
                    border: 1px solid {main_controller.get_theme_attribute("Color", "text")};
                    border-radius: 10px;
                    padding: 5px;}}
                QPushButton::hover{{
                    background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};             
                    font-size: 14px;
                    border: 1px solid {main_controller.get_theme_attribute("Color", "text")};
                    border-radius: 10px;
                    padding: 5px;}}
                QPushButton::pressed{{
                    background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};}}              
            '''
            self.button_close.setStyleSheet(btn_cancel_style)
            self.button_close.setFixedSize(144, 48)
            self.button_close.clicked.connect(lambda: (self.close_cancel_signal.emit()))
        self.button_save_update = QPushButton(self.btn_save_title)
        self.button_save_update.clicked.connect(lambda: (self.save_update_signal.emit()))
        # Style nút Lưu giống btn_connect ở trạng thái disconnect (connect)
        btn_save_style = f'''
            QPushButton {{
                color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
            }}  
            QPushButton{{
                background-color: {main_controller.get_theme_attribute("Color", "primary")};
                color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                font-size: 14px;
                border: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                border-radius: 10px;
                padding: 1px;}}

            QPushButton::hover{{
                color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                font-size: 14px;
                border: 1px solid {main_controller.get_theme_attribute("Color", "text_same_bg")};
                border-radius: 10px;
                padding: 5px;}}
            QPushButton::pressed{{
                background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};}}               
        '''
        self.button_save_update.setStyleSheet(btn_save_style)
        self.button_save_update.setFixedSize(144, 48)

        # add widget
        if footer_type != FooterType.CONNECT:
            self.footer_layout.addWidget(self.button_close)
        if footer_type != FooterType.CLOSE:
            self.footer_layout.addWidget(self.button_save_update)
        if footer_type == FooterType.NONE_TYPE:
            self.footer_widget.setLayout(QVBoxLayout())
        else:
            self.footer_widget.setLayout(self.footer_layout)
        self.layout = QVBoxLayout()
        self.layout.setSpacing(0)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.addWidget(self.footer_widget)
        self.setLayout(self.layout)

class ButtonCloseDialog(QPushButton):
    def __init__(self, parent=None):
        super().__init__(parent)
        close_icon = QIcon(main_controller.get_theme_attribute("Image", "dialog_close"))
        self.setIcon(close_icon)
        self.setIconSize(QSize(24, 24))
        self.setFixedSize(24, 24)

    def enterEvent(self, event):
        self.setIcon(QIcon(main_controller.get_theme_attribute("Image", "dialog_close_hoverred")))

    def leaveEvent(self, event):
        self.setIcon(QIcon(main_controller.get_theme_attribute("Image", "dialog_close")))
