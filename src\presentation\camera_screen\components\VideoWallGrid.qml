import QtQuick 2.15
import QtQuick.Controls 2.15

/**
 * VideoWallGrid.qml - Video Wall Grid Container
 *
 * CHỨC NĂNG CHÍNH:
 * - <PERSON><PERSON> thừa từ MainGrid.qml
 * - Video wall optimized cho large displays
 * - Virtual window display support
 * - Synchronized với MainGrid qua shared GridModel
 * - Large screen responsive layout
 */
MainGrid {
    id: videoWallGrid
    // ✅ Override video wall specific properties (inherited from MainGrid)
    isVirtualGrid: true // Flag để identify virtual grid
    showLabels: true // Show camera labels on video wall
    // showControlButtons: true // Show control buttons on video wall
}
