from dataclasses import replace
import logging
from src.common.controller.controller_manager import controller_manager,Controller
from src.common.model.camera_model import Camera, CameraModel,camera_model_manager
from src.common.model.zone_model import ZoneModel
from src.common.onvif_api.worker_thread import WorkerThread
from src.presentation.device_management_screen.widget.ai_state import AIScript<PERSON><PERSON>,AIFlowType
from src.common.model.aiflows_model import <PERSON><PERSON>low,AiFlowModel,aiflow_model_manager
from src.common.controller.main_controller import main_controller
from src.presentation.device_management_screen.widget.ai_camera_zone_widget import AICameraZoneWidget, InputType
from src.common.widget.dialogs.base_dialog import NewBaseDialog, FooterType
from src.utils.config import Config
from src.utils.theme_setting import theme_setting
from src.presentation.device_management_screen.widget.ai_zone_dropdown_draw import AIZoneDropDownDraw
logger = logging.getLogger(__name__)
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QHBoxLayout, QLineEdit, QMessageBox
from PySide6.QtGui import QCloseEvent, Qt
from PySide6.QtCore import Signal
from src.styles.style import Style

class ContentAddUpdateScriptAIDialog(NewBaseDialog):
    create_update_success = Signal(bool)
    def __init__(self, parent=None, camera_data=None, ai_flow_type = None):
        logger.info(f'init ai_flow_type = {ai_flow_type} type: {type(ai_flow_type)} - camera_data: {type(camera_data)} - camera_data = {camera_data}')
        self.camera_data: Camera = Camera.from_dict(camera_data)
        self.camera_id = self.camera_data.id
        self.server_ip = self.camera_data.server_ip
        self.dialog_type = InputType.CAMERA
        self.ai_flow: AiFlowModel = None
        self.ai_script_state = AIScriptState.EDIT
        self.ai_flow_type_selected = ai_flow_type
        self._success = False  # Add success state property

        # Default values for AI settings
        self.default_settings = {
            AIFlowType.RECOGNITION: {
                "confidence": 60,  # 60%
                "frequency_update": 3,  # 3 seconds
            },
            AIFlowType.PROTECTION: {
                "confidence": 60,  # 60%
                "frequency_update": 5,  # 5 seconds
            },
            AIFlowType.FREQUENCY: {
                "confidence": 60,  # 60%
                "frequency_update": 5,  # 5 seconds
                "tracking_time": 10,  # 10 minutes
                "tracking_count": 15,  # 15 times
            },
            AIFlowType.ACCESS: {
                "confidence": 60,  # 60%
                "frequency_update": 5,  # 5 seconds
                "disappear_time": 5,  # 5 minutes
            },
            AIFlowType.MOTION: {
                "confidence": 60,  # 60%
                "frequency_update": 5,  # 5 seconds
                "standing_time": 30,  # 30 seconds
            },
            AIFlowType.TRAFFIC: {
                "confidence": 60,  # 60%
                "frequency_update": 5,  # 5 seconds
            },
            AIFlowType.UFO: {
                "confidence": 60,  # 60%
                "frequency_update": 5,  # 5 seconds
            },
            AIFlowType.WEAPON: {
                "confidence": 60,  # 60%
                "frequency_update": 5,  # 5 seconds
            }
        }

        # load data from server to edit
        footer_type = FooterType.UPDATE_CANCEL
        title = self.tr('EDIT AI SCRIPT')
        # Mapping of AIType enum values to translated strings
        self.ai_flow_type_map = {
            AIFlowType.RECOGNITION: self.tr('Recognition'),
            AIFlowType.PROTECTION: self.tr('Protection'),
            AIFlowType.FREQUENCY: self.tr('Frequency'),
            AIFlowType.ACCESS: self.tr('Access'),
            AIFlowType.MOTION: self.tr('Motion'),
            AIFlowType.TRAFFIC: self.tr('Traffic'),
            AIFlowType.WEAPON: self.tr('Weapon'),
            AIFlowType.UFO: self.tr('UFO'),
        }

        self.ai_flow_type_str = self.ai_flow_type_map[self.ai_flow_type_selected]

        self.load_ui()

        content_widget = QWidget()
        content_widget.setLayout(self.layout_dialog)

        super().__init__(parent, title=title, content_widget=content_widget, footer_type=footer_type)
        self.setObjectName("ContentAddUpdateScriptAIDialog")
        
        # Move signal connections after parent initialization
        aiflow_model_manager.add_aiflow_signal.connect(self.add_aiflow_signal)
        self.create_update_success.connect(self.on_create_update_success)
        self.save_update_signal.connect(self.create_update_ai_config)
        
        self.controller: Controller = controller_manager.get_controller(server_ip=self.server_ip)
        self.controller.get_ai_flow_and_type(self.camera_id, self.ai_flow_type_selected)

    def create_input_layout(self, label_text, default_value="", unit_text=None, visible_for_types=None):
        """Create a reusable input layout with label, text input, and optional unit label.
        
        Args:
            label_text: The text for the label
            default_value: Default value for the QLineEdit
            unit_text: Optional text for unit label (e.g., "Giây")
            visible_for_types: List of AIFlowType values for which this input should be visible
                              If None, the input is always visible
            
        Returns:
            tuple: (QLineEdit, QWidget) - The QLineEdit and container widget
        """
        container_widget = QWidget()
        container_widget.setStyleSheet(f'''
            QWidget {{
                background-color: transparent;
            }}
        ''')
        main_layout = QVBoxLayout(container_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(8)  # Space between label and input field
        
        # Set visibility based on flow type
        if visible_for_types:
            container_widget.setVisible(self.ai_flow_type_selected in visible_for_types)
        
        # Get default value from settings if available
        if self.ai_flow_type_selected in self.default_settings:
            settings = self.default_settings[self.ai_flow_type_selected]
            if label_text == self.tr("Confidence threshold:"):
                default_value = str(settings["confidence"])
            elif label_text == self.tr("Update frequency:"):
                default_value = str(settings["frequency_update"])
            elif label_text == self.tr("Tracking time:"):
                # Default value is already in minutes in default_settings
                default_value = str(settings.get("tracking_time", ""))
            elif label_text == self.tr("Appearance count:"):
                default_value = str(settings.get("tracking_count", ""))
            elif label_text == self.tr("Disappear time:"):
                # Default value is already in minutes in default_settings
                default_value = str(settings.get("disappear_time", ""))
            elif label_text == self.tr("Standing time:"):
                default_value = str(settings.get("standing_time", ""))
        
        # Label with required asterisk
        label_container = QWidget()
        label_layout = QHBoxLayout(label_container)
        label_layout.setContentsMargins(0, 0, 0, 0)
        label_layout.setSpacing(2)
        
        label = QLabel(self.tr(label_text))
        label.setStyleSheet(f'''
            QLabel {{
                background-color: transparent;
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                font-size: 14px;
                font-weight: medium;
            }}
        ''')
        label_layout.addWidget(label)
        
        # Add red asterisk for required fields
        required_label = QLabel(self.tr("*"))
        required_label.setStyleSheet(f"QLabel {{ color: {main_controller.get_theme_attribute('Color', 'error')}; background-color: transparent; }}")
        label_layout.addWidget(required_label)
        label_layout.addStretch()
        
        # Add label container to main layout
        main_layout.addWidget(label_container)
        
        # Input field with unit container
        input_container = QWidget()
        input_layout = QHBoxLayout(input_container)
        input_layout.setContentsMargins(0, 0, 0, 0)
        input_layout.setSpacing(0)
        
        # Create the input field with dark style
        qline_edit = QLineEdit(default_value)
        qline_edit.setFixedHeight(36)
        
        if unit_text:
            # Input field with unit
            qline_edit.setStyleSheet(f'''
                QLineEdit {{
                    border: 1px solid {main_controller.get_theme_attribute("Color", "input_border")};
                    border-right: none;
                    border-top-left-radius: 4px;
                    border-bottom-left-radius: 4px;
                    border-top-right-radius: 0;
                    border-bottom-right-radius: 0;
                    padding: 0 12px;
                    background-color: {main_controller.get_theme_attribute("Color", "input_background")};
                    color: {main_controller.get_theme_attribute("Color", "input_text")};
                    font-size: 14px;
                }}
                QLineEdit:focus {{
                    border: 1px solid {main_controller.get_theme_attribute("Color", "primary")};
                    border-right: none;
                }}
            ''')
            input_layout.addWidget(qline_edit, 1)
            
            # Create unit container
            unit_container = QWidget()
            unit_container.setFixedHeight(36)
            unit_container.setMinimumWidth(50)  # Set minimum width
            unit_container.setStyleSheet(f'''
                QWidget {{
                    background-color: {main_controller.get_theme_attribute("Color", "widget_background_2")};
                    border: 1px solid {main_controller.get_theme_attribute("Color", "input_border")};
                    border-top-right-radius: 4px;
                    border-bottom-right-radius: 4px;
                }}
            ''')
            
            # Unit label
            unit_layout = QHBoxLayout(unit_container)
            unit_layout.setContentsMargins(10, 0, 10, 0)
            
            unit_label = QLabel(unit_text)
            unit_label.setStyleSheet(f'''
                QLabel {{
                    background-color: transparent;
                    color: {main_controller.get_theme_attribute("Color", "input_text")};
                    border: none;
                    font-size: 13px;
                }}
            ''')
            unit_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            unit_layout.addWidget(unit_label)
            
            input_layout.addWidget(unit_container)
        else:
            # Input field without unit
            qline_edit.setStyleSheet(f'''
                QLineEdit {{
                    border: 1px solid {main_controller.get_theme_attribute("Color", "input_border")};
                    border-radius: 4px;
                    padding: 0 12px;
                    background-color: {main_controller.get_theme_attribute("Color", "input_background")};
                    color: {main_controller.get_theme_attribute("Color", "input_text")};
                    font-size: 14px;
                }}
                QLineEdit:focus {{
                    border: 1px solid {main_controller.get_theme_attribute("Color", "primary")};
                }}
            ''')
            input_layout.addWidget(qline_edit, 1)
        
        main_layout.addWidget(input_container)
        
        return qline_edit, container_widget

    def create_dropdown_layout(self, label_text, items, default_item=None, is_required=True, is_enabled=True):
        """Create a reusable dropdown layout with label and AIZoneDropDownDraw dropdown.
        
        Args:
            label_text: The text for the label
            items: List of items to display in the dropdown
            default_item: Default selected item for the dropdown
            is_required: Whether to show a required asterisk
            is_enabled: Whether the dropdown should be enabled
            
        Returns:
            tuple: (AIZoneDropDownDraw, QWidget) - The dropdown component and container widget
        """
        container_widget = QWidget()
        
        main_layout = QVBoxLayout(container_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(8)  # Space between label and dropdown field
        
        # Label with required asterisk if needed
        label_container = QWidget()
        label_layout = QHBoxLayout(label_container)
        label_layout.setContentsMargins(0, 0, 0, 0)
        label_layout.setSpacing(2)
        
        label = QLabel(self.tr(label_text))
        label.setStyleSheet(f'''
            QLabel {{
                background-color: transparent;
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                font-size: 14px;
                font-weight: medium;
            }}
        ''')
        label_layout.addWidget(label)
        
        # Add red asterisk for required fields
        if is_required:
            required_label = QLabel(self.tr("*"))
            required_label.setStyleSheet(f"QLabel {{ color: {main_controller.get_theme_attribute('Color', 'error')}; background-color: transparent; }}")
            label_layout.addWidget(required_label)
        
        label_layout.addStretch()
        
        # Add label container to main layout
        main_layout.addWidget(label_container)
        
        # Create dropdown with matching height to QLineEdit
        dropdown = AIZoneDropDownDraw(combo_box_height=36)
        # Don't set a fixed width for the dropdown
        
        # Style the dropdown to match QLineEdit appearance
        dropdown.combo_box.setStyleSheet(f'''
            QComboBox {{ 
                background-color: {main_controller.get_theme_attribute("Color", "input_background")}; 
                border: 1px solid {main_controller.get_theme_attribute("Color", "input_border")}; 
                border-radius: 4px;
                padding: 0 12px;
                color: {main_controller.get_theme_attribute("Color", "input_text")};
                font-size: 14px;
            }}
            QComboBox::drop-down {{
                 background-color: transparent;
                 border: none;
                 subcontrol-origin: padding;
                 subcontrol-position: right;
                 width: 20px;
             }}
            QComboBox::down-arrow {{ 
                image: url({Style.PrimaryImage.down_arrow_linedit}); 
            }}
        ''')
        
        # Add items to dropdown
        dropdown.add_items(items)
        
        # Set default selection if provided
        if default_item:
            dropdown.set_selected_items([default_item])
        
        # Set enabled state
        dropdown.setEnabled(is_enabled)
        
        # Add dropdown to layout
        main_layout.addWidget(dropdown)
        
        return dropdown, container_widget

    def load_ui(self):
        h_layout_ai_choose_box = QHBoxLayout()
        h_layout_ai_choose_box.setSpacing(8)

        layout_ai_type = QHBoxLayout()
        layout_ai_type.setAlignment(Qt.AlignmentFlag.AlignLeft)
        layout_ai_type.setContentsMargins(0, 0, 0, 0)
        layout_ai_type.setSpacing(18)  # Increase spacing between items for better visual separation

        # Get all available AI flow types
        ai_flow_types = []
        for ai_flow_type_str in self.ai_flow_type_map.values():
            ai_flow_types.append(ai_flow_type_str)
            
        # Use the create_dropdown_layout function for AI type dropdown with no fixed width
        self.ai_type_dropdown, ai_type_container = self.create_dropdown_layout(
            label_text=self.tr("AI Type"),
            items=ai_flow_types,
            default_item=self.ai_flow_type_str,
            is_enabled=False,
        )
        
        # Connect the dropdown selection signal
        self.ai_type_dropdown.item_selected.connect(self.on_ai_type_selected)
        
        # Add the container to the main layout
        layout_ai_type.addWidget(ai_type_container)
        
        # Create inputs using the reusable function
        self.qline_edit_frequency_update, self.frequency_update_container = self.create_input_layout(
            self.tr("Update frequency:"), "1", self.tr("Seconds")
        )
        
        self.qline_edit_confidence, self.confidence_container = self.create_input_layout(
            self.tr("Confidence threshold:"), "30", "%"
        )
        
        # Only show tracking time for frequency monitoring
        self.qline_edit_tracking_time, self.tracking_time_container = self.create_input_layout(
            self.tr("Tracking time:"), "10", self.tr("Minutes"), visible_for_types=[AIFlowType.FREQUENCY]
        )
        
        # Only show tracking count for frequency monitoring
        self.qline_edit_tracking_count, self.tracking_count_container = self.create_input_layout(
            self.tr("Appearance count:"), "15", self.tr("Times"), visible_for_types=[AIFlowType.FREQUENCY]
        )
        
        # Only show disappear time for access monitoring
        self.qline_edit_disappear_time, self.disappear_time_container = self.create_input_layout(
            self.tr("Disappear time:"), "10", self.tr("Minutes"), visible_for_types=[AIFlowType.ACCESS]
        )
        
        # Only show standing time for motion monitoring
        self.qline_edit_standing_time, self.standing_time_container = self.create_input_layout(
            self.tr("Standing time:"), "2", self.tr("Seconds"), visible_for_types=[AIFlowType.MOTION]
        )
        
        layout_ai_type.addWidget(self.frequency_update_container)
        layout_ai_type.addWidget(self.confidence_container)
        layout_ai_type.addWidget(self.tracking_time_container)
        layout_ai_type.addWidget(self.tracking_count_container)
        layout_ai_type.addWidget(self.disappear_time_container)
        layout_ai_type.addWidget(self.standing_time_container)
        # hide all container
        self.frequency_update_container.setVisible(False)
        self.confidence_container.setVisible(False)
        self.tracking_time_container.setVisible(False)
        self.tracking_count_container.setVisible(False)
        self.disappear_time_container.setVisible(False)
        self.standing_time_container.setVisible(False)

        h_layout_ai_choose_box.addLayout(layout_ai_type)

        zone_activate_widget = QWidget()
        zone_activate_widget.setObjectName('zone_activate_widget')
        zone_activate_widget.setStyleSheet(f'''
                    QWidget#zone_activate_widget{{
                        background-color: {main_controller.get_theme_attribute("Color", "ai_zone_background")};
                        border: 1px solid {main_controller.get_theme_attribute("Color", "dialog_header_background")};
                        border-radius: 4px;
                    }}
                ''')
        zone_activate_layout = QVBoxLayout()
        zone_activate_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        zone_activate_layout.setSpacing(4)

        layout_label_zone_active = QHBoxLayout()
        layout_label_zone_active.setContentsMargins(8, 0, 8, 0)
        layout_label_zone_active.setAlignment(Qt.AlignmentFlag.AlignLeft)
        zone_activate_label = QLabel(self.tr('Draw active zone'))
        zone_activate_label.setStyleSheet(f'background-color: transparent; color: {main_controller.get_theme_attribute("Color", "subtabbar_text_normal")};'
                                          f'font-size: {Style.Size.body_strong}px;')
        zone_activate_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        layout_label_zone_active.addWidget(zone_activate_label)
        # camera zone AICameraZoneWidget
        self.ai_camera_zone_widget = AICameraZoneWidget(parent = self,
                                                        camera_data=self.camera_data, 
                                                        ai_flow_type = self.ai_flow_type_selected,
                                                        input_type=self.dialog_type)
        zone_activate_layout.addLayout(layout_label_zone_active)
        zone_activate_layout.addWidget(self.ai_camera_zone_widget)
        zone_activate_widget.setLayout(zone_activate_layout)

        self.layout_dialog = QVBoxLayout()
        self.layout_dialog.setSpacing(12)
        self.layout_dialog.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_dialog.addLayout(h_layout_ai_choose_box)
        
        self.layout_dialog.addWidget(zone_activate_widget)

    def add_aiflow_signal(self, aiflow):
        logger.debug(f'add_aiflow_signal = {aiflow}')
        self.ai_flow: AiFlowModel = aiflow
        logger.debug(f'add_aiflow_signal = {type(self.ai_flow)} - type: {self.ai_flow.data.type} - {self.ai_flow.data.id}')
        self.ai_camera_zone_widget.get_zone_and_update_ui(self.ai_flow.data.id)
        
        # update hiển thị settings của ai_flow
        # RECOGNITION -> chỉ hiển thị tần suất cập nhật và độ tin cậy
        # FREQUENCY -> hiển thị tần suất cập nhật, độ tin cậy, thời gian theo dõi và số lần xuất hiện
        # PROTECTION -> chỉ hiển thị tần suất cập nhật và độ tin cậy
        # ACCESS -> chỉ hiển thị tần suất cập nhật và độ tin cậy và thời gian biến mất
        # MOTION -> chỉ hiển thị tần suất cập nhật và độ tin cậy và thời gian đứng yên
        # TRAFFIC -> chỉ hiển thị tần suất cập nhật và độ tin cậy và thời gian đứng yên
        # UFO -> chỉ hiển thị tần suất cập nhật và độ tin cậy
        # WEAPON -> chỉ hiển thị tần suất cập nhật và độ tin cậy
        # Get all containers that need to be managed
        all_containers = {
            'frequency': self.frequency_update_container,
            'confidence': self.confidence_container,
            'tracking_time': self.tracking_time_container,
            'tracking_count': self.tracking_count_container,
            'disappear_time': self.disappear_time_container,
            'standing_time': self.standing_time_container
        }

        # Define visibility rules for each AI type
        visibility_rules = {
            AIFlowType.RECOGNITION: ['frequency', 'confidence'],
            AIFlowType.FREQUENCY: ['frequency', 'confidence', 'tracking_time', 'tracking_count'],
            AIFlowType.PROTECTION: ['frequency', 'confidence'],
            AIFlowType.ACCESS: ['frequency', 'confidence', 'disappear_time'],
            AIFlowType.MOTION: ['frequency', 'confidence', 'standing_time'],
            AIFlowType.TRAFFIC: ['frequency', 'confidence'], # do bên BE chưa làm nên tạm xoá đi
            AIFlowType.UFO: ['frequency', 'confidence'],
            AIFlowType.WEAPON: ['frequency', 'confidence']
        }

        logger.debug(f'self.ai_flow_type_selected = {self.ai_flow_type_selected}')
        
        # Apply visibility rules
        visible_containers = visibility_rules.get(self.ai_flow_type_selected, [])
        logger.debug(f'visible_containers = {visible_containers}')
        for container_name, container in all_containers.items():
            container.setVisible(container_name in visible_containers)

        # Load existing settings if available
        self.load_existing_settings()

    def load_existing_settings(self):
        """Load existing settings from the AI flow if available."""
        # Get default settings for the current AI type
        default_settings = self.default_settings.get(self.ai_flow_type_selected, {})
        
        # Initialize with default values from self.default_settings
        threshold = default_settings.get("confidence", 60) / 100
        interval = default_settings.get("frequency_update", 5)
        stopDuration = default_settings.get("standing_time", 30)
        disappearDuration = default_settings.get("disappear_time", 5)
        appearanceCount = default_settings.get("tracking_count", 15)
        trackingDuration = default_settings.get("tracking_time", 10)

        # Override with AI flow values if available
        if self.ai_flow is not None:
            if self.ai_flow.data.threshold is not None:
                threshold = self.ai_flow.data.threshold
            if self.ai_flow.data.interval is not None:
                interval = self.ai_flow.data.interval
            if self.ai_flow.data.stopDuration is not None:
                stopDuration = self.ai_flow.data.stopDuration
            if self.ai_flow.data.disappearDuration is not None:
                disappearDuration = self.ai_flow.data.disappearDuration
            if self.ai_flow.data.appearanceCount is not None:
                appearanceCount = self.ai_flow.data.appearanceCount
            if self.ai_flow.data.trackingDuration is not None:
                trackingDuration = self.ai_flow.data.trackingDuration

        logger.debug(f"Loading existing settings: {threshold} - {interval} - {stopDuration} - {disappearDuration} - {appearanceCount} - {trackingDuration}")
        
        # Update the UI with the loaded settings
        self.qline_edit_confidence.setText(str(self.convert_threshold_to_percentage_unit(threshold)))
        self.qline_edit_frequency_update.setText(str(interval))
        
        if self.ai_flow_type_selected == AIFlowType.MOTION:
            self.qline_edit_standing_time.setText(str(stopDuration))
        if self.ai_flow_type_selected == AIFlowType.ACCESS:
            # Convert seconds from server to minutes for display
            disappear_time_minutes = self.convert_seconds_to_minutes(disappearDuration)
            self.qline_edit_disappear_time.setText(str(disappear_time_minutes))
        if self.ai_flow_type_selected == AIFlowType.FREQUENCY:
            # Convert seconds from server to minutes for display
            tracking_time_minutes = self.convert_seconds_to_minutes(trackingDuration)
            self.qline_edit_tracking_time.setText(str(tracking_time_minutes))
            self.qline_edit_tracking_count.setText(str(appearanceCount))

    def convert_threshold_to_percentage_unit(self, threshold):
        logger.debug(f"convert_threshold_to_percentage_unit: {threshold}")
        return f"{threshold * 100}"

    def convert_percentage_unit_to_threshold(self, percentage):
        logger.debug(f"convert_percentage_unit_to_threshold: {percentage}")
        return float(percentage) / 100.0

    def convert_seconds_to_minutes(self, seconds):
        """Convert seconds to minutes for display purposes."""
        logger.debug(f"convert_seconds_to_minutes: {seconds}")
        return int(seconds / 60) if seconds is not None else 0

    def convert_minutes_to_seconds(self, minutes):
        """Convert minutes to seconds for server storage."""
        logger.debug(f"convert_minutes_to_seconds: {minutes}")
        return int(minutes * 60)

    def on_ai_type_selected(self, selected_items):
        """Handle the selection of an AI type from the dropdown."""
        # đầu tiên: từ selected_items -> xem là AI type nào được chọn -> get ai_flow_id 
        # sau đó: lấy ai_flow_id để lấy data của ai_flow và cập nhật các qline_edit, zone_data
        # sau đó: cập nhật lại ui
        # Convert selected text to AIFlowType
        selected_text = selected_items[0]
        # Create reverse mapping from text to AIFlowType
        reverse_map = {v: k for k, v in self.ai_flow_type_map.items()}
        self.ai_flow_type_selected = reverse_map[selected_text]
        logger.debug(f'selected_items = {selected_items}, mapped to AIFlowType = {self.ai_flow_type_selected}')
        
        self.controller.get_ai_flow_and_type(self.camera_id, self.ai_flow_type_selected)


    def on_create_update_success(self, success: bool):
        logger.debug(f'on_create_update_success success = {success}')
        self._success = success  # Store success state
        self.accept()

    @property
    def success(self) -> bool:
        return self._success

    def show_error_dialog(self, message):
        """Show an error dialog with the given message."""
        error_dialog = QMessageBox(self)
        error_dialog.setIcon(QMessageBox.Icon.Critical)
        error_dialog.setWindowTitle(self.tr("Error"))
        error_dialog.setText(message)
        error_dialog.setStandardButtons(QMessageBox.StandardButton.Ok)
        error_dialog.exec()

    def validate_inputs(self):
        """Validate all input fields and return whether they are all valid."""
        # try:
        # Common validation for all AI types
        # Parse to float first, then convert to int to handle decimal values like "1.0"
        update_frequency = float(self.qline_edit_frequency_update.text())
        if update_frequency < 0:
            self.show_error_dialog(self.tr("Update frequency must be greater than zero or equal to zero."))
            return False
            
        # Parse to float first, then convert to int to handle decimal values like "50.0"
        confidence_threshold = float(self.qline_edit_confidence.text())
        if confidence_threshold < 0 or confidence_threshold > 100:
            self.show_error_dialog(self.tr("Confidence threshold must be between 0 and 100."))
            return False
        
        # Validate type-specific fields
        if self.ai_flow_type_selected == AIFlowType.FREQUENCY:
            tracking_time = int(float(self.qline_edit_tracking_time.text()))
            if tracking_time <= 0:
                self.show_error_dialog(self.tr("Tracking time must be greater than zero."))
                return False
                
            appearance_count = int(float(self.qline_edit_tracking_count.text()))
            if appearance_count <= 0:
                self.show_error_dialog(self.tr("Appearance count must be greater than zero."))
                return False
        
        if self.ai_flow_type_selected == AIFlowType.ACCESS:
            disappear_time = int(float(self.qline_edit_disappear_time.text()))
            if disappear_time <= 0:
                self.show_error_dialog(self.tr("Disappear time must be greater than zero."))
                return False
        
        if self.ai_flow_type_selected == AIFlowType.MOTION:
            standing_time = int(float(self.qline_edit_standing_time.text()))
            if standing_time <= 0:
                self.show_error_dialog(self.tr("Standing time must be greater than zero."))
                return False
                
        return True
        # except ValueError as e:
        #     self.show_error_dialog(self.tr("Invalid input. Please enter numeric values only."))
        #     logger.error(f"Input validation error: {e}")
        #     return False

    # CREATE/UPDATE AI CONFIG
    def create_update_ai_config(self):
        # Validate inputs first
        if not self.validate_inputs():
            logger.error("Input validation failed")
            self.create_update_success.emit(False)
            return
            
        # Get values from input fields (already validated)
        update_frequency = int(float(self.qline_edit_frequency_update.text()))
        confidence_threshold = self.convert_percentage_unit_to_threshold(float(self.qline_edit_confidence.text()))
        logger.debug(f'update_frequency = {update_frequency} - confidence_threshold = {confidence_threshold}')
        if self.ai_flow_type_selected == AIFlowType.FREQUENCY:
            # Convert minutes from UI to seconds for server
            tracking_time_minutes = int(float(self.qline_edit_tracking_time.text()))
            tracking_time = self.convert_minutes_to_seconds(tracking_time_minutes)
            appearance_count = int(float(self.qline_edit_tracking_count.text()))
            logger.debug(f'tracking_time_minutes = {tracking_time_minutes} -> tracking_time_seconds = {tracking_time} - appearance_count = {appearance_count}')
        if self.ai_flow_type_selected == AIFlowType.ACCESS:
            # Convert minutes from UI to seconds for server
            disappear_time_minutes = int(float(self.qline_edit_disappear_time.text()))
            disappear_time = self.convert_minutes_to_seconds(disappear_time_minutes)
            logger.debug(f'disappear_time_minutes = {disappear_time_minutes} -> disappear_time_seconds = {disappear_time}')
        if self.ai_flow_type_selected == AIFlowType.MOTION:
            standing_time = int(float(self.qline_edit_standing_time.text()))
            logger.debug(f'standing_time = {standing_time}')
        # Create settings dictionary
        ai_settings = {
            "interval": update_frequency,
            "threshold": confidence_threshold
        }
        if self.ai_flow_type_selected == AIFlowType.FREQUENCY:
            ai_settings["trackingDuration"] = tracking_time
            ai_settings["appearanceCount"] = appearance_count
        if self.ai_flow_type_selected == AIFlowType.ACCESS:
            ai_settings["disappearDuration"] = disappear_time
        if self.ai_flow_type_selected == AIFlowType.MOTION:
            ai_settings["stopDuration"] = standing_time

        logger.debug(f'ai_settings = {ai_settings}')
        
        # AI Flow not exist -> Create
        logger.debug(f'self.ai_camera_zone_widget.list_zone_data_saved: {self.ai_camera_zone_widget.list_zone_data_saved}')
        if len(self.ai_camera_zone_widget.list_zone_data_saved) > 0:
            # Get the controller for this camera
            if self.controller:
                def batch_process_callback(success: bool):
                    logger.debug(f'batch_process_callback = {success}')
                    if success:
                        logger.debug(f'batch_process_callback success')
                        self.create_update_success.emit(True)
                        logger.debug(f'batch_process_callback success accept')
                    else:
                        logger.error("Failed to process zones and AI flow batch")
                        self.create_update_success.emit(False)
                
                # Process zones and AI flow in batch
                self.controller.process_zone_aiflow_batch_async(
                    parent=self,
                    zones_data=self.ai_camera_zone_widget.list_zone_data_saved,
                    aiflow_id=self.ai_flow.data.id,
                    camera_id=self.camera_data.id,
                    aiflow_type=self.ai_flow_type_selected,
                    ai_settings=ai_settings,  # Add the settings dictionary
                    callback=batch_process_callback
                )
            else:
                logger.error("Could not find controller for camera")
                self.create_update_success.emit(False)
        else:
            self.create_update_success.emit(True)

    def closeEvent(self, event: QCloseEvent) -> None:
        if self.ai_camera_zone_widget is not None and self.dialog_type == InputType.CAMERA:
            self.ai_camera_zone_widget.close_camera_stream_zone()
        return super().closeEvent(event)

