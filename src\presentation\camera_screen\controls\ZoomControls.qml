import QtQuick
import QtQuick.Controls
import QtQuick.Layouts

/**
 * ZoomControls.qml - Đ<PERSON><PERSON><PERSON>n zoom cho camera và map
 *
 * CHỨC NĂNG CHÍNH:
 * - Zoom in/out
 * - Reset zoom
 * - Digital zoom
 * - Zoom speed control
 */
Item {
    id: root
    anchors.fill: parent

    // 1. Properties
    property var gridItem: null  // Reference đến GridItem parent
    property bool isDarkTheme: gridItem ? gridItem.isDarkTheme : true
    property bool isSelected: gridItem ? gridItem.isSelected : false
    property bool isMaximized: gridItem && gridItem.gridModel ? gridItem.gridModel.isMaximized : false
    property bool isHovered: gridItem ? gridItem.isHovered : false
    property bool showControls: isHovered || isSelected || isMaximized

    // 2. Zoom properties
    property real zoomFactor: 1.0
    property real minZoom: 1.0
    property real maxZoom: 5.0
    property real zoomStep: 0.5
    property bool isZooming: false

    // 3. Signals
    signal zoomIn()
    signal zoomOut()
    signal zoomReset()
    signal zoomChanged(real factor)

    // 4. Zoom Controls
    Column {
        id: zoomControls
        anchors.right: parent.right
        anchors.top: parent.top
        anchors.margins: 8
        spacing: 4
        visible: showControls

        // Zoom in button
        Rectangle {
            id: zoomInButton
            width: 24
            height: 24
            color: zoomInMouseArea.pressed ? "#4fd1c5" : (isDarkTheme ? "#2d2d2d" : "#f0f0f0")
            radius: 4
            opacity: zoomFactor < maxZoom ? 1.0 : 0.5

            Text {
                text: "+"
                color: isDarkTheme ? "#ffffff" : "#000000"
                font.pixelSize: 12
                anchors.centerIn: parent
            }

            MouseArea {
                id: zoomInMouseArea
                anchors.fill: parent
                enabled: zoomFactor < maxZoom

                onClicked: {
                    var newZoom = Math.min(maxZoom, zoomFactor + zoomStep)
                    zoomFactor = newZoom
                    zoomChanged(newZoom)
                    zoomIn()
                }
            }
        }

        // Zoom out button
        Rectangle {
            id: zoomOutButton
            width: 24
            height: 24
            color: zoomOutMouseArea.pressed ? "#4fd1c5" : (isDarkTheme ? "#2d2d2d" : "#f0f0f0")
            radius: 4
            opacity: zoomFactor > minZoom ? 1.0 : 0.5

            Text {
                text: "-"
                color: isDarkTheme ? "#ffffff" : "#000000"
                font.pixelSize: 12
                anchors.centerIn: parent
            }

            MouseArea {
                id: zoomOutMouseArea
                anchors.fill: parent
                enabled: zoomFactor > minZoom

                onClicked: {
                    var newZoom = Math.max(minZoom, zoomFactor - zoomStep)
                    zoomFactor = newZoom
                    zoomChanged(newZoom)
                    zoomOut()
                }
            }
        }

        // Reset zoom button
        Rectangle {
            id: zoomResetButton
            width: 24
            height: 24
            color: zoomResetMouseArea.pressed ? "#4fd1c5" : (isDarkTheme ? "#2d2d2d" : "#f0f0f0")
            radius: 4
            opacity: zoomFactor !== 1.0 ? 1.0 : 0.5

            Text {
                text: "1:1"
                color: isDarkTheme ? "#ffffff" : "#000000"
                font.pixelSize: 8
                anchors.centerIn: parent
            }

            MouseArea {
                id: zoomResetMouseArea
                anchors.fill: parent
                enabled: zoomFactor !== 1.0

                onClicked: {
                    zoomFactor = 1.0
                    zoomChanged(1.0)
                    zoomReset()
                }
            }
        }
    }

    // 5. Zoom Speed Control
    Rectangle {
        id: zoomSpeedControl
        anchors.right: zoomControls.left
        anchors.top: parent.top
        anchors.margins: 8
        width: 40
        height: 80
        color: isDarkTheme ? "#80000000" : "#80ffffff"
        radius: 8
        visible: showControls

        Column {
            anchors.centerIn: parent
            spacing: 4

            // Speed label
            Text {
                text: "Zoom Speed"
                color: isDarkTheme ? "#ffffff" : "#000000"
                font.pixelSize: 10
                anchors.horizontalCenter: parent.horizontalCenter
            }

            // Speed slider
            Slider {
                id: zoomSpeedSlider
                orientation: Qt.Vertical
                from: 0.1
                to: 1.0
                value: 0.5
                height: 60

                onValueChanged: {
                    zoomStep = value
                }
            }
        }
    }
} 