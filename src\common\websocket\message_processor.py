from typing import Callable, List
import threading

from src.common.controller.controller_manager import Controller, controller_manager
from src.common.model.camera_model import Camera,CameraModel, camera_model_manager
from src.common.model.group_model import Group, GroupModel, group_model_manager
from src.common.model.event_data_model import EventModel, event_manager
from src.common.model.record_model import Record,RecordModel,record_model_manager,RecordData
from src.common.qml.models.map_controller import FloorMode<PERSON>,floor_manager,BuildingModel,building_manager,MapModel,map_manager
from src.common.camera.video_player_manager import video_player_manager
from src.utils.utils import Utils
import queue
import logging
logger = logging.getLogger(__name__)

class MessageProcessor:
    def __init__(self,server_ip = None):
        self.server_ip = server_ip
        self.controller:Controller = controller_manager.get_controller(server_ip = server_ip)
        self.message_queue = queue.Queue()
        self.threads = self.start_threads(5,self._process_messages)
        self.handlers = {
            "CAMERA_RECORDING_STATE_UPDATE": self.cameraRecordingStateUpdate,
            "RECORDING_UPDATE": self.recordingUpdate,
            "CAMERA_STATE_UPDATE": self.cameraStateUpdate,
            # Camera
            "vms_camera_delete": self.vms_camera_delete,
            "vms_camera_update": self.vms_camera_update,
            "vms_camera_add": self.vms_camera_add,
            "vms_camera_group_add": self.vms_camera_group_add,
            "vms_camera_group_update": self.vms_camera_group_update,
            "vms_camera_group_delete": self.vms_camera_group_delete,
            "vms_aiflow_update": self.vms_aiflow_update,
            # Map
            "BUILDING_CREATE": self.buildingCreate,
            "BUILDING_DELETE": self.buildingDelete,
            "BUILDING_UPDATE": self.buildingUpdate,
            "FLOOR_CREATE": self.floorCreate,
            "FLOOR_DELETE": self.floorDelete,
            "FLOOR_UPDATE": self.floorUpdate,
            # Event
            "warning": self.warning,
            "SERVICE_STATE_UPDATE": self.serviceStateUpdate
        }

    def _process_messages(self):
        while True:
            message_json = self.message_queue.get()
            if message_json is None:
                break
            try:
                # Xử lý lâu ở đây, không làm nghẽn WebSocket
                event = message_json.get("event",message_json.get("type",None))
                data = message_json.get("data",None)
                # logger.debug(f"event = {event} data = {data}")
                handler = self.handlers.get(event, self.handle_default)
                handler(data)
            except Exception:
                logger.exception("Error processing message")
            self.message_queue.task_done()
        logger.info(f'_process_messages done')
        
    def handle_default(self, data):
        pass

    def cameraRecordingStateUpdate(self,data):
        camera_model:CameraModel = camera_model_manager.get_camera_model(id = data.get("id",None))
        if camera_model is not None:
            state = data.get("state", None)
            if state is not None:
                camera_model.recordingState = state

    def recordingUpdate(self, data):
        cameraId = data.get("cameraId",None)
        id = data.get("id",None)
        recordData: RecordData = record_model_manager.record_data.get(cameraId, None)
        if recordData is not None:
            # trạng thái con cam này đang có trên Grid rồi
            check = False
            for recordModel in recordData.data:
                if id == recordModel.data.id:
                    # case link record này đã có trên thanh timeline rồi
                    recordModel.data.start_duration = Utils.convert_string_to_duration(data.get("start",None))
                    recordModel.data.end_duration = Utils.convert_string_to_duration(data.get("end",None))
                    recordModel.recordModelChanged.emit()
                    check = True
                    break
            if not check:
                # case link record này chưa có trên thanh timeline
                record = Record.from_dict(data)
                recordModel = RecordModel(record=record)
                recordModel.data.start_duration = Utils.convert_string_to_duration(record.start)
                recordModel.data.end_duration = Utils.convert_string_to_duration(record.end)
                if recordModel.data.end_duration is not None:
                    if len(recordData.data) == 0:
                        recordData.start_duration = recordModel.data.start_duration
                        recordData.end_duration = recordModel.data.end_duration
                    recordData.data.append(recordModel)
                    recordData.recordDataChanged.emit()
        else:
            if cameraId == "0066fd36-2076-4d69-b53b-69928d8c4d6f":
                logger.info(f'recordingUpdate recordData = None')

    def cameraStateUpdate(self, data):
        camera_model:CameraModel = camera_model_manager.get_camera_model(id = data.get("id",None))
        if camera_model is not None:
            state = data.get("state", None)
            if state is not None:
                logger.info(f'cameraStateUpdate = {state}')
                camera_model.set_state(state)
                video_player_manager.notify_camera_state_change(camera_model.id, state)

    def vms_camera_delete(self, data):
        pass
    def serviceStateUpdate(self, data):
        if data.get("type") == "STREAMING":
            if data.get("state") == "CONNECTED":
                # Directly restart all video captures when streaming service reconnects
                video_player_manager.restart_all_players()

    def vms_camera_update(self, data):
        clientId = data.get("clientId",None)
        if clientId != Utils.clientId:
            camera_model = CameraModel(data=data)
            diff = []
            camera_model_origin:CameraModel = camera_model_manager.get_camera_model(id = camera_model.get_property("id",None))
            if camera_model_origin is not None:
                map_manager.updateCamera(camera_model_origin,data)
                diff = camera_model_origin.diff_camera_model(camera = camera_model.data)
                if 'cameraGroupIds' in diff:
                    self.controller.get_groups() 

    def vms_camera_add(self, data):
        data = Camera.from_dict(data)
        camera_model = CameraModel(data=data)
        camera_model.data.server_ip = self.server_ip
        camera_model_manager.add_camera(camera=camera_model)
        if camera_model.data.cameraGroupIds is not None and len(camera_model.data.cameraGroupIds) > 0:
            self.controller.get_groups()

    def vms_camera_group_add(self, data):
        pass

    def vms_camera_group_update(self, data):
        pass

    def vms_camera_group_delete(self, data):
        pass

    def vms_aiflow_update(self, data):
        pass

    def buildingCreate(self, data):
        clientId = data.get("clientId",None)
        if clientId != Utils.clientId:
            building_manager.createBuildingWSSignal.emit((self.server_ip,data))

    def buildingDelete(self, data):
        clientId = data.get("clientId",None)
        if clientId != Utils.clientId:
            building_manager.removeBuildingWSSignal.emit((self.server_ip,data))

    def buildingUpdate(self, data):
        clientId = data.get("clientId",None)
        if clientId != Utils.clientId:
            id = data.get("id", None)
            buildingModel:BuildingModel = building_manager.getBuilding(id)
            if buildingModel is not None:
                buildingModel.diffBuildingModel(data)
            building_manager.updateBuildingWSSignal.emit((self.server_ip,data))

    def floorCreate(self, data):
        pass
        # xử lý case này trong buildingUpdate rồi lên thôi
        # floor_manager.createFloorWSSignal.emit((self.server_ip,data))

    def floorDelete(self, data):
        clientId = data.get("clientId",None)
        if clientId != Utils.clientId:
            floor_manager.removeFloorWSSignal.emit((self.server_ip,data))

    def floorUpdate(self, data):
        id = data.get("id", None)
        logger.info(f'floorUpdate = {data.get("name", "ahihi")}')
        floorModel:FloorModel = floor_manager.getFloor(id= id)
        if floorModel is not None:
            floorModel.diff_floor_model(data)

    def warning(self, data):
        try:
            event_manager.add_event_data.emit(data)

        except Exception as e:
            logger.error(f'on_websocket_event error: {e}')
    
    def start_threads(self,number: int, target: Callable, *args) -> List[threading.Thread]:
        threads = []
        for _ in range(number):
            thread = threading.Thread(target=target, args=args)
            thread.daemon = True
            threads.append(thread)
            thread.start()
