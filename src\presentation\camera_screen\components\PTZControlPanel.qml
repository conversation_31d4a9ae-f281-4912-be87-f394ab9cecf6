import QtQuick 2.15
import QtQuick.Layouts 1.15
import "../constants/ZIndexConstants.js" as ZIndex

Rectangle {
    id: ptzControlPanel

    // Properties
    property real speedValue: 1.0  // Gi<PERSON> trị mặc định là 1.0 (ở giữa slider)
    property bool isDarkTheme: true
    property real buttonSize: 32  // Giảm kích thước nút xuống 32
    property real iconSize: 32   // Giảm kích thước icon xuống 20
    property real lineWidth: 1


    width: buttonSize * 4.5   // Giảm chiều rộng
    height: buttonSize * 6  // Giảm chiều cao
    color: "#1A1B32" // Màu tím xuyên thấu
    radius: 20  // Tăng radius để bo tròn hơn
    border.color: "#555555"
    border.width: 0.5

    // Signals
    signal ptzMove(string direction)
    signal ptzZoom(real factor)
    signal ptzSpeed(real speed)
    signal ptzClose()
    signal ptzStop()

    
    
    // Thêm một MouseArea ở phía dưới để chặn sự kiện truyền xuống GridItem
    MouseArea {
        id: backgroundBlocker
        anchors.fill: parent
        hoverEnabled: true
        z: ZIndex.gridItemModalBackground

        // Chặn tất cả các sự kiện, không cho truyền xuống GridItem
        onClicked: function(mouse) { mouse.accepted = true }
        onPressed: function(mouse) { mouse.accepted = true }
        onReleased: function(mouse) { mouse.accepted = true }
        onPositionChanged: function(mouse) { mouse.accepted = true }
        onWheel: function(wheel) { wheel.accepted = true }
    }

    // Main content
    ColumnLayout {
        id: mainContent
        anchors.fill: parent
        anchors.margins: 4
        anchors.bottomMargin: 10
        spacing: 5
        z: ZIndex.gridItemDialog

        // Direction controls - Circular layout
        Item {
            Layout.alignment: Qt.AlignHCenter
            Layout.preferredWidth: ptzControlPanel.buttonSize * 6
            Layout.preferredHeight: ptzControlPanel.buttonSize * 6

            property real centerX: width / 2
            property real centerY: height / 2
            property real radius: ptzControlPanel.buttonSize * 2

            // Direction buttons data
            property var buttons: [
                {dir: "up", icon: "top", angle: -Math.PI/2, id: "upMouse"},
                {dir: "top-right", icon: "right_top", angle: -Math.PI/4, id: "topRightMouse"},
                {dir: "right", icon: "right", angle: 0, id: "rightMouse"},
                {dir: "bottom-right", icon: "right_bottom", angle: Math.PI/4, id: "bottomRightMouse"},
                {dir: "down", icon: "bottom", angle: Math.PI/2, id: "downMouse"},
                {dir: "bottom-left", icon: "left_bottom", angle: 3*Math.PI/4, id: "bottomLeftMouse"},
                {dir: "left", icon: "left", angle: Math.PI, id: "leftMouse"},
                {dir: "top-left", icon: "left_top", angle: 5*Math.PI/4, id: "topLeftMouse"}
            ]
            Canvas {
                anchors.fill: parent
                onPaint: {
                    var ctx = getContext("2d");
                    ctx.clearRect(0, 0, width, height);
                    ctx.beginPath();
                    ctx.arc(width / 2, height / 2, parent.radius + 20, 0, 2 * Math.PI);
                    ctx.lineWidth = 2;
                    ctx.strokeStyle = "#737373";
                    ctx.stroke();
                }
            }
            // Generate direction buttons
            Repeater {
                model: parent.buttons
                Rectangle {
                    width: ptzControlPanel.buttonSize * 1
                    height: width
                    radius: width / 2
                    color: mouseArea.containsMouse ? "gray" : "transparent"

                    x: parent.centerX + parent.radius * Math.cos(modelData.angle) - width / 2
                    y: parent.centerY + parent.radius * Math.sin(modelData.angle) - height / 2

                    Image {
                        anchors.centerIn: parent
                        width: parent.width * 1
                        height: parent.height * 1
                        source: gridModel.isDarkTheme ? `qrc:src/assets/ptz_icon/${modelData.icon}.svg` : `qrc:src/assets/ptz_icon/${modelData.icon}_light.svg`
                        fillMode: Image.PreserveAspectFit
                        sourceSize: Qt.size(width, height)
                    }

                    MouseArea {
                        id: mouseArea
                        anchors.fill: parent
                        hoverEnabled: true
                        onPressed: ptzControlPanel.ptzMove(modelData.dir)
                        onReleased: ptzControlPanel.ptzStop()
                    }
                }
            }

            // Home button - Center
            Rectangle {
                width: ptzControlPanel.buttonSize
                height: width
                radius: width / 2
                color: "transparent"

                x: parent.centerX - width / 2
                y: parent.centerY - height / 2

                Image {
                    anchors.centerIn: parent
                    width: parent.width * 2
                    height: parent.height * 2
                    source: gridModel.isDarkTheme ? "qrc:src/assets/ptz_icon/around.svg" : "qrc:src/assets/ptz_icon/around_light.svg"
                    fillMode: Image.PreserveAspectFit
                    sourceSize: Qt.size(width, height)
                }

                MouseArea {
                    id: homeMouse
                    anchors.fill: parent
                    hoverEnabled: true
                    onPressed: ptzControlPanel.ptzMove("home")
                    onReleased: ptzControlPanel.ptzStop()
                }
            }
        }

        // Zoom controls - Compact design with circles
        RowLayout {
            Layout.fillWidth: true
            Layout.alignment: Qt.AlignHCenter
            spacing: 6  // Giảm khoảng cách để compact hơn

            // Zoom out - Circle
            Rectangle {
                Layout.preferredWidth: ptzControlPanel.buttonSize
                Layout.preferredHeight: ptzControlPanel.buttonSize
                radius: width / 2  // Tạo hình tròn
                color: "transparent"

                Image {
                    anchors.centerIn: parent
                    width: parent.width * 1
                    height: parent.height * 1
                    source: gridModel.isDarkTheme ? "qrc:src/assets/ptz_icon/zoom_out.svg" : "qrc:src/assets/ptz_icon/zoom_out_light.svg"
                    fillMode: Image.PreserveAspectFit
                }

                MouseArea {
                    id: zoomOutMouse
                    anchors.fill: parent
                    hoverEnabled: true
                    onPressed: ptzControlPanel.ptzZoom(-1)
                    onReleased: ptzControlPanel.ptzStop()
                }
            }

            // Zoom in - Circle
            Rectangle {
                Layout.preferredWidth: ptzControlPanel.buttonSize
                Layout.preferredHeight: ptzControlPanel.buttonSize
                radius: width / 2  // Tạo hình tròn
                color: "transparent"
       // Thicker border for circles

                Image {
                    anchors.centerIn: parent
                    width: parent.width * 1
                    height: parent.height * 1
                    source: gridModel.isDarkTheme ? "qrc:src/assets/ptz_icon/zoom_in.svg" : "qrc:src/assets/ptz_icon/zoom_in_light.svg"
                    fillMode: Image.PreserveAspectFit
                }

                MouseArea {
                    id: zoomInMouse
                    anchors.fill: parent
                    hoverEnabled: true
                    onPressed: ptzControlPanel.ptzZoom(1)
                    onReleased: ptzControlPanel.ptzStop()
                }
            }
        }
        // Speed slider - Compact design with border
        Rectangle {
            Layout.preferredWidth: parent.width * 0.8
            Layout.alignment: Qt.AlignHCenter
            Layout.preferredHeight: ptzControlPanel.iconSize + 3  // Add padding for border
            Layout.bottomMargin: 8 // Đảm bảo không sát cạnh dưới
            radius: (ptzControlPanel.iconSize + 8) / 2  // Pill/pin shape radius
            color: "transparent"
            border.color: gridModel.isDarkTheme ? "#555555" : "#cccccc"
            border.width: 1

            RowLayout {
                anchors.fill: parent
                spacing: 0
            // Minus icon - Circle
            Rectangle {
                id: minusButton
                Layout.preferredWidth: ptzControlPanel.iconSize
                Layout.preferredHeight: ptzControlPanel.iconSize
                radius: ptzControlPanel.iconSize / 2  // Tạo hình tròn
                color: "transparent"


                Image {
                    anchors.centerIn: parent
                    width: parent.width * 0.8
                    height: parent.height * 0.8
                    source: gridModel.isDarkTheme ? "qrc:src/assets/ptz_icon/down_speed.svg" : "qrc:src/assets/ptz_icon/down_speed_light.svg"
                    fillMode: Image.PreserveAspectFit
                    sourceSize: Qt.size(width, height)
                }

                MouseArea {
                    id: minusMouseArea
                    anchors.fill: parent
                    hoverEnabled: true
                    onClicked: function(mouse) {
                        // Giảm tốc độ khi click vào nút minus
                        var newValue = Math.max(0, ptzControlPanel.speedValue - 0.1);
                        ptzControlPanel.speedValue = newValue;
                        ptzControlPanel.ptzSpeed(newValue);
                        mouse.accepted = true;
                    }
                }
            }

            // Custom Slider Implementation - Compact design
            Rectangle {
                id: customSlider
                Layout.fillWidth: true
                Layout.preferredHeight: ptzControlPanel.iconSize
                color: "transparent"

                property real from: 0
                property real to: 1
                property real value: ptzControlPanel.speedValue
                property real stepSize: 0.1
                property real visualPosition: (value - from) / (to - from)

                // Background track
                Rectangle {
                    id: sliderTrack
                    anchors.centerIn: parent
                    width: parent.width - 8
                    height: 3  // Giảm chiều cao track
                    radius: 1.5
                    color: gridModel.isDarkTheme ? "gray" : "black"
                    // Progress indicator
                    Rectangle {
                        width: customSlider.visualPosition * parent.width
                        height: parent.height
                        color: "white"
                        radius: 1.5
                    }
                }

                // Handle - Circle
                Rectangle {
                    id: sliderHandle
                    x: 6 + customSlider.visualPosition * (sliderTrack.width - width)
                    y: (parent.height - height) / 2
                    width: ptzControlPanel.iconSize * 0.2  // Giảm kích thước handle
                    height: ptzControlPanel.iconSize * 0.8
                    color: "gray"
                    radius: height/2
                }

                MouseArea {
                    id: sliderMouseArea
                    anchors.fill: parent

                    function updateValue(mouseX) {
                        var newPosition = Math.max(0, Math.min(1, (mouseX - 8) / sliderTrack.width))
                        var newValue = customSlider.from + newPosition * (customSlider.to - customSlider.from)

                        // Apply step size
                        if (customSlider.stepSize > 0) {
                            newValue = Math.round(newValue / customSlider.stepSize) * customSlider.stepSize
                        }

                        customSlider.value = Math.max(customSlider.from, Math.min(customSlider.to, newValue))
                        ptzControlPanel.speedValue = customSlider.value

                        // Calculate PTZ speed
                        var ptzSpeed = customSlider.value
                        ptzControlPanel.ptzSpeed(ptzSpeed)
                        
                    }

                    onPressed: function(mouse) {
                        updateValue(mouse.x)
                    }

                    onPositionChanged: function(mouse) {
                        if (pressed) {
                            updateValue(mouse.x)
                        }
                    }
                }
            }

            // Plus icon - Circle
            Rectangle {
                id: plusButton
                Layout.preferredWidth: ptzControlPanel.iconSize
                Layout.preferredHeight: ptzControlPanel.iconSize
                radius: ptzControlPanel.iconSize / 2  // Tạo hình tròn
                color: "transparent"


                Image {
                    anchors.centerIn: parent
                    width: parent.width * 0.8
                    height: parent.height * 0.8
                    source: gridModel.isDarkTheme ? "qrc:src/assets/ptz_icon/up_speed.svg" : "qrc:src/assets/ptz_icon/up_speed_light.svg"
                    fillMode: Image.PreserveAspectFit
                    sourceSize: Qt.size(width, height)
                }

                MouseArea {
                    id: plusMouseArea
                    anchors.fill: parent
                    hoverEnabled: true
                    onClicked: function(mouse) {
                        // Tăng tốc độ khi click vào nút plus
                        var newValue = Math.min(1, ptzControlPanel.speedValue + 0.1);
                        customSlider.value = newValue;
                        ptzControlPanel.speedValue = newValue;
                        ptzControlPanel.ptzSpeed(newValue);
                        mouse.accepted = true;
                    }
                }
            }
        }
        }
    }
}
