import QtQuick 
import QtQuick.Controls 
import QtQuick.Layouts 
// import QtQuick.Controls.Material 
Rectangle {
    // width: parent.width
    anchors.fill: parent
    // height: 500
    property color mainBackground: device_controller ? device_controller.get_color_theme_by_key("main_background") : "#1E1E1E"
    property color textColor: device_controller ? device_controller.get_color_theme_by_key("text_color_all_app") : "#2D2D2D"

    property color headerBackground: Qt.rgba(textColor.r, textColor.g, textColor.b, 0.15)
    property color subHeaderBackground: device_controller ? device_controller.get_color_theme_by_key("filter_background") : "#FFFFFF"
    
    property color hoverColor: subHeaderBackground
    property color borderColor: headerBackground
    color: mainBackground

    Connections{
        target: device_controller
        function onThemeChanged(){
            mainBackground = device_controller.get_color_theme_by_key("main_background")
            textColor = device_controller.get_color_theme_by_key("text_color_all_app")
            headerBackground = Qt.rgba(textColor.r, textColor.g, textColor.b, 0.15)
            subHeaderBackground = device_controller.get_color_theme_by_key("filter_background")
            hoverColor = subHeaderBackground
            borderColor = headerBackground
        }
    }

    Item {
        id: header
        // anchors.fill: parent
        width: parent.width
        height: 50  // Điều chỉnh theo chiều cao của các phần tử bên trong
        // color: on_hover_secondary
        Rectangle {
            // width: parent.width
            anchors.fill: parent
            anchors.leftMargin: 10
            anchors.rightMargin: 10
            radius: 8
            color: headerBackground
            RowLayout  {
                spacing: 0
                anchors.fill: parent
                // anchors.fill: parent
                anchors.leftMargin: 10
                anchors.rightMargin: 10
                CustomText {
                    text: qsTr("Group name")
                    width: parent.width * 0.3
                    Layout.minimumWidth: width
                    Layout.maximumWidth: width
                    font.pixelSize: 14
                    color: textColor
                    font.weight: 600
                }
                CustomText {
                    text: qsTr("Box")
                    width: parent.width * 0.1
                    Layout.minimumWidth: width
                    Layout.maximumWidth: width
                    font.pixelSize: 14
                    color: textColor
                    font.weight: 600
                }
                CustomText {
                    text: qsTr("Camera")
                    width: parent.width * 0.5
                    Layout.minimumWidth: width
                    Layout.maximumWidth: width
                    font.pixelSize: 14
                    color: textColor
                    font.weight: 600
                }
                // CustomText {
                //     text: qsTr("DOOR")
                //     Layout.fillWidth: true
                //     Layout.preferredWidth: 64
                //     Layout.alignment: Qt.AlignLeft
                //     font.pixelSize: 14
                //     color: textColor
                //     font.weight: 600
                // }
                CustomText {
                    text: qsTr("Action")
                    width: parent.width * 0.1
                    Layout.minimumWidth: width
                    Layout.maximumWidth: width
                    color: textColor
                    font.pixelSize: 14
                    font.weight: 600
                }
            }
        }
    }

    Flickable {
        id: flickable
        anchors.top: header.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        contentHeight: root.implicitHeight
        // boundsBehavior: Flickable.OvershootBounds

        Pane {
            id: root
            anchors.fill: parent
            background: Rectangle {
                color: mainBackground  // Thay đổi thành màu nền bạn muốn (ví dụ: màu trắng)
            }
            Column {
                spacing: 10
                anchors.right: parent.right
                anchors.left: parent.left
                anchors.verticalCenter: parent.verticalCenter
                Repeater {
                    model: device_controller ? device_controller.listmodel : []
                    delegate: Column {
                        property bool showList: false
                        // spacing: 10
                        anchors.left: parent.left
                        anchors.right: parent.right

                        Item {
                            width: parent.width
                            height: 45  // Điều chỉnh theo chiều cao của các phần tử bên trong
                            // anchors.fill: parent

                            Rectangle {
                                anchors.fill: parent
                                border.color: borderColor
                                border.width: 0.5
                                radius: 8
                                color: paneSettingsList.shown ? subHeaderBackground : "transparent"

                                MouseArea {
                                    anchors.fill: parent
                                    hoverEnabled: true
                                    onClicked: {
                                        paneSettingsList.shown = !paneSettingsList.shown
                                    }
                                    onEntered: parent.color = hoverColor
                                    onExited: parent.color = paneSettingsList.shown ? subHeaderBackground : "transparent"
                                }
                                RowLayout  {
                                    spacing: 0
                                    anchors.fill: parent
                                    anchors.leftMargin: 10
                                    anchors.rightMargin: 10
                                    CustomText {
                                        text: model && model.name !== undefined ? model.name : "-"
                                        width: parent.width * 0.3
                                        Layout.minimumWidth: width
                                        Layout.maximumWidth: width
                                        font.pixelSize: 14
                                        
                                        elide: Text.ElideRight
                                        color: textColor
                                    }
                                    CustomText {
                                        text: model && model.box_count !== undefined ? qsTr("Box (" + model.box_count + ")") : qsTr("Box (0)")
                                        width: parent.width * 0.1
                                        Layout.minimumWidth: width
                                        Layout.maximumWidth: width
                                        font.pixelSize: 12
                                        elide: Text.ElideRight
                                        color: textColor
                                    }
                                    CustomText {
                                        text: model && model.camera_count !== undefined ? qsTr("Camera (" + model.camera_count + ")") : qsTr("Camera (0)")
                                        width: parent.width * 0.48
                                        Layout.minimumWidth: width
                                        Layout.maximumWidth: width
                                        font.pixelSize: 12
                                        elide: Text.ElideRight
                                        color: textColor
                                    }
                                    // CustomText {
                                    //     text: model && model.door_count !== undefined ? qsTr("REVOLVING DOOR (" + model.door_count + ")") : qsTr("REVOLVING DOOR (0)")
                                    //     Layout.fillWidth: true
                                    //     Layout.preferredWidth: 1
                                    //     font.pixelSize: 12
                                    //     elide: Text.ElideRight
                                    //     color: textColor
                                    // }
                                    ControlGroup {
                                        width: parent.width * 0.06
                                        Layout.minimumWidth: width
                                        Layout.maximumWidth: width
                                        isExpand: paneSettingsList
                                    }
                                    Item {
                                        width: parent.width * 0.06
                                        Layout.minimumWidth: width
                                        Layout.maximumWidth: width
                                    }
                                }

                            }
                        }
                        Pane {
                            id: paneSettingsList
                            leftPadding: 0
                            // ## relevant part ##
                            property bool shown: false
                            visible: height > 0
                            height: shown ? implicitHeight : 0
                            Behavior on height {
                                NumberAnimation {
                                    duration: 200
                                    easing.type: Easing.InOutQuad
                                }
                            }
                            clip: true
                            // ## relevant part ##

                            // Material.background: "lightblue"
                            // padding: 10
                            // anchors.topMargin: 10
                            anchors.left: parent.left
                            anchors.right: parent.right
                            background: Rectangle {
                                color: "transparent"
                            }
                            Column {
                                spacing: 10
                                anchors.right: parent.right
                                anchors.left: parent.left
                                Item {
                                    width: parent.width
                                    height: 30  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                    // anchors.fill: parent
                                    visible: paneSettingsList
                                    RowLayout {
                                        anchors.verticalCenter: parent.verticalCenter
                                        anchors.left: parent.left
                                        anchors.right: parent.right
                                        anchors.leftMargin: 40
                                        spacing: 10

                                        RowLayout {
                                            spacing: 10
                                            id: rowRecognitionAndProtection1
                                            width: (parent.width + 40) * 0.3 - 44
                                            Layout.minimumWidth: width
                                            Layout.maximumWidth: width
                                            Image {
                                                width: 24
                                                height: 24
                                                source: "../../../../src/assets/ai_icons/ic_recognition_security.svg" // Thay đổi icon dựa trên trạng thái
                                                fillMode: Image.PreserveAspectFit
                                                sourceSize: Qt.size(width, height)
                                            }
                                            CustomText {
                                                Layout.preferredWidth:150
                                                text: qsTr("Recognition & Protection (%1)").arg(model.recognition_protection || 0)
                                                font.pixelSize: 12
                                                color: textColor
                                            }
                                            Item {
                                                Layout.fillWidth: true
                                            }
                                        }
                                        
                                        RowLayout{
                                            width: parent.width - rowRecognitionAndProtection1.width
                                            Layout.minimumWidth: width
                                            Layout.maximumWidth: width
                                            spacing: 10
                                            ButtonAIFlow {
                                                ai_text: qsTr("Recognition")
                                                buttonType: type
                                                aiType: model.recognition.state
                                                onClicked: {
                                                    device_controller.aiflow_clicked("recognition",model,false)
                                                }
                                                onCheckboxClicked: {
                                                    device_controller.aiflow_clicked("recognition",model,true)
                                                }
                                            } 
                                            ButtonAIFlow {
                                                ai_text: qsTr("Protection")
                                                buttonType: type
                                                aiType: model.protection.state
                                                onClicked: {
                                                    device_controller.aiflow_clicked("protection",model,false)
                                                }
                                                onCheckboxClicked: {
                                                    device_controller.aiflow_clicked("protection",model,true)
                                                }
                                            }   
                                            ButtonAIFlow {
                                                ai_text: qsTr("Frequency")
                                                buttonType: type
                                                aiType: model.frequency.state
                                                onClicked: {
                                                    device_controller.aiflow_clicked("frequency",model,false)
                                                }
                                                onCheckboxClicked: {
                                                    device_controller.aiflow_clicked("frequency",model,true)
                                                }
                                            }  
                                            ButtonAIFlow {
                                                ai_text: qsTr("Access")
                                                buttonType: type
                                                aiType: model.access.state
                                                onClicked: {
                                                    device_controller.aiflow_clicked("access",model,false)
                                                }
                                                onCheckboxClicked: {
                                                    device_controller.aiflow_clicked("access",model,true)
                                                }
                                            }  
                                            ButtonAIFlow {
                                                ai_text: qsTr("Motion")
                                                buttonType: type
                                                aiType: model.motion.state
                                                onClicked: {
                                                    device_controller.aiflow_clicked("motion",model,false)
                                                }
                                                onCheckboxClicked: {
                                                    device_controller.aiflow_clicked("motion",model,true)
                                                }
                                            } 
                                            ButtonAIFlow {
                                                ai_text: qsTr("Traffic")
                                                buttonType: type
                                                aiType: model.traffic.state
                                                onClicked: {
                                                    device_controller.aiflow_clicked("traffic",model,false)
                                                }
                                                onCheckboxClicked: {
                                                    device_controller.aiflow_clicked("traffic",model,true)
                                                }
                                            }
                                            Item {
                                                Layout.fillWidth: true
                                            }
                                        }
                                    }
                                }
                                Item {
                                    width: parent.width
                                    height: 30  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                    // anchors.fill: parent
                                    visible: paneSettingsList
                                    RowLayout  {
                                        anchors.verticalCenter: parent.verticalCenter
                                        anchors.left: parent.left
                                        anchors.right: parent.right
                                        anchors.leftMargin: 40
                                        spacing: 10

                                        RowLayout {
                                            id: rowRiskIdentification1
                                            spacing: 10
                                            width: (parent.width + 40) * 0.3 - 44
                                            Layout.minimumWidth: width
                                            Layout.maximumWidth: width
                                            Image {
                                                width: 24
                                                height: 24
                                                source: "../../../../src/assets/ai_icons/ic_risk_identification.svg" // Thay đổi icon dựa trên trạng thái
                                                fillMode: Image.PreserveAspectFit
                                                sourceSize: Qt.size(width, height)
                                            }
                                            CustomText {
                                                Layout.preferredWidth: 150
                                                text: qsTr("Risk Recognition (%1)").arg(model.risk_identification || 0)
                                                font.pixelSize: 12
                                                color: textColor
                                            }
                                            Item{
                                                Layout.fillWidth: true
                                            }
                                        }

                                        RowLayout{
                                            width: parent.width - rowRiskIdentification1.width
                                            Layout.minimumWidth: width
                                            Layout.maximumWidth: width
                                            spacing: 10
                                            
                                            ButtonAIFlow {
                                                ai_text: qsTr("Weapon")
                                                buttonType: type
                                                aiType: model.weapon.state
                                                onClicked: {
                                                    device_controller.aiflow_clicked("weapon",model,false)
                                                }
                                                onCheckboxClicked: {
                                                    device_controller.aiflow_clicked("weapon",model,true)
                                                }
                                            }  
                                            ButtonAIFlow {
                                                ai_text: qsTr("UFO")
                                                buttonType: type
                                                aiType: model.ufo.state
                                                onClicked: {
                                                    device_controller.aiflow_clicked("ufo",model,false)
                                                }
                                                onCheckboxClicked: {
                                                    device_controller.aiflow_clicked("ufo",model,true)
                                                }
                                            }   
                                            Item {
                                                Layout.fillWidth: true
                                            }
                                        }
                                    }
                                }
                                Repeater {
                                    id: listSettings1
                                    property bool showList: false
                                    anchors.left: parent.left
                                    anchors.right: parent.right
                                    model: child
                                    delegate: Column {
                                        property bool showList: false
                                        anchors.left: parent.left
                                        anchors.right: parent.right
                                        Item {
                                            width: parent.width
                                            height: 45  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                            // anchors.fill: parent
                                            Rectangle {
                                                anchors.fill: parent
                                                border.color: model.type === "AIBox" ? borderColor : "transparent"
                                                // border.color: "transparent"
                                                // border.width: model.type === "AIBox" ? 1 : 0
                                                border.width: 0.5
                                                radius: model.type === "AIBox" ? 8 : 0
                                                
                                                color: paneSettingsList1.shown ? hoverColor : model.type === "AIBox" ? headerBackground : "transparent"

                                                MouseArea {
                                                    anchors.fill: parent
                                                    hoverEnabled: true
                                                    onClicked: {
                                                        paneSettingsList1.shown = !paneSettingsList1.shown
                                                    }
                                                    onEntered: parent.color = hoverColor
                                                    onExited: parent.color = paneSettingsList1.shown ? hoverColor : model.type === "AIBox" ? headerBackground : "transparent"
                                                }
                                                RowLayout  {
                                                    spacing: 0
                                                    anchors.fill: parent
                                                    // anchors.left: parent.left
                                                    // anchors.right: parent.right
                                                    // anchors.top: parent.top
                                                    // anchors.bottom: parent.bottom
                                                    // anchors.leftMargin: model.type === "AIBox" ? 10 : 0
                                                    anchors.rightMargin: model.type === "AIBox" ? 10 : 10
                                                    Item{
                                                        Layout.preferredWidth: 40
                                                    }
                                                    CustomText {
                                                        text: name
                                                        width: (parent.width + 40) * 0.3 - 44
                                                        Layout.minimumWidth: width
                                                        Layout.maximumWidth: width
                                                        font.pixelSize: 14
                                                        elide: Text.ElideRight
                                                        color: textColor
                                                        // font.pixelSize: 12
                                                    }

                                                    CustomText {
                                                        text: qsTr("Number of AI selected")
                                                        color: textColor
                                                        Layout.minimumWidth: implicitWidth
                                                        Layout.maximumWidth: implicitWidth
                                                        // font.pixelSize: 12
                                                    }

                                                    Item{
                                                        width: 30
                                                        Layout.minimumWidth: width
                                                        Layout.maximumWidth: width
                                                    }

                                                    Image {
                                                        width: 24
                                                        height: 24
                                                        Layout.minimumWidth: width
                                                        Layout.maximumWidth: width
                                                        source: "../../../../src/assets/ai_icons/ic_recognition_security.svg" // Thay đổi icon dựa trên trạng thái
                                                        fillMode: Image.PreserveAspectFit
                                                        sourceSize: Qt.size(width, height)
                                                    }
                                                    Item{
                                                        width: 10
                                                        Layout.minimumWidth: width
                                                        Layout.maximumWidth: width
                                                    }
                                                    
                                                    CustomText {
                                                        text: "(" + model.human_count + ")"
                                                        Layout.minimumWidth: implicitWidth
                                                        Layout.maximumWidth: implicitWidth
                                                        color: textColor
                                                        // font.pixelSize: 12
                                                    }
                                                    
                                                    Item{
                                                        width: 30
                                                        Layout.minimumWidth: width
                                                        Layout.maximumWidth: width
                                                    }

                                                    Image {
                                                        width: 24
                                                        height: 24
                                                        Layout.minimumWidth: width
                                                        Layout.maximumWidth: width
                                                        source: "../../../../src/assets/ai_icons/ic_risk_identification.svg" // Thay đổi icon dựa trên trạng thái
                                                        fillMode: Image.PreserveAspectFit
                                                        sourceSize: Qt.size(width, height)
                                                    }
                                                    Item{
                                                        Layout.preferredWidth: 4
                                                    }
                                                    CustomText {
                                                        text: "(" + model.vehicle_count + ")"
                                                        Layout.minimumWidth: implicitWidth
                                                        Layout.maximumWidth: implicitWidth
                                                        color: textColor
                                                        // font.pixelSize: 12
                                                    }
                                                    Item {
                                                        Layout.fillWidth: true
                                                    }
                                                    ControlGroup {
                                                        width: parent.width * 0.06
                                                        Layout.minimumWidth: width
                                                        Layout.maximumWidth: width
                                                        isExpand: paneSettingsList1
                                                    }
                                                    Item {
                                                        width: parent.width * 0.06 - 10
                                                        Layout.minimumWidth: width
                                                        Layout.maximumWidth: width
                                                    }
                                                }
                                                Rectangle {
                                                    anchors.bottom: parent.bottom
                                                    anchors.left: parent.left
                                                    anchors.right: parent.right
                                                    // anchors.leftMargin: 30
                                                    // anchors.rightMargin: 30
                                                    height: model.type === "Camera" ? 1 : 0  // Độ dày của line
                                                    color: subHeaderBackground  // Màu của line
                                                }
                                            }
                                        }

                                        Pane {
                                            id: paneSettingsList1

                                            // ## relevant part ##
                                            property bool shown: false
                                            visible: height > 0
                                            height: shown ? implicitHeight : 0
                                            Behavior on height {
                                                NumberAnimation {
                                                    duration: 200
                                                    easing.type: Easing.InOutQuad
                                                }
                                            }
                                            clip: true
                                            // ## relevant part ##

                                            // Material.background: "lightblue"
                                            padding: 10
                                            anchors.left: parent.left
                                            anchors.right: parent.right
                                            background: Rectangle {
                                                color: mainBackground
                                                radius: 8
                                            }
                                            Column {
                                                spacing: 10
                                                anchors.right: parent.right
                                                anchors.left: parent.left
                                                Item {
                                                    width: parent.width
                                                    height: 30  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                                    // anchors.fill: parent
                                                    visible: paneSettingsList1
                                                    RowLayout  {
                                                        anchors.verticalCenter: parent.verticalCenter
                                                        anchors.left: parent.left
                                                        anchors.right: parent.right
                                                        anchors.leftMargin: 30
                                                        spacing: 10
                                                        RowLayout {
                                                            spacing: 10
                                                            width: parent.width * 0.28
                                                            Layout.minimumWidth: width
                                                            Layout.maximumWidth: width

                                                            Image {
                                                                width: 24
                                                                height: 24
                                                                source: "../../../../src/assets/ai_icons/ic_recognition_security.svg" // Thay đổi icon dựa trên trạng thái
                                                                fillMode: Image.PreserveAspectFit
                                                                sourceSize: Qt.size(width, height)
                                                            }
                                                            CustomText {
                                                                Layout.preferredWidth:150
                                                                text: qsTr("Recognition & Protection (%1)").arg(model.recognition_protection || 0)
                                                                font.pixelSize: 12
                                                                color: textColor
                                                            }
                                                            Item{
                                                                Layout.fillWidth: true
                                                            }
                                                        }
                                                        
                                                        RowLayout{
                                                            width: parent.width * 0.72
                                                            Layout.minimumWidth: width
                                                            Layout.maximumWidth: width
                                                            spacing: 10
                                                            ButtonAIFlow {
                                                                ai_text: qsTr("Recognition")
                                                                buttonType: type
                                                                aiType: model.recognition.state
                                                                onClicked: {
                                                                    console.log("Add Abc = ",ai_text,index,model.recognition.state)
                                                                    device_controller.aiflow_clicked("recognition",model,false)
                                                                }
                                                                onCheckboxClicked: {
                                                                    device_controller.aiflow_clicked("recognition",model,true)
                                                                }
                                                            } 
                                                            ButtonAIFlow {
                                                                ai_text: qsTr("Protection")
                                                                buttonType: type
                                                                aiType: model.protection.state
                                                                onClicked: {
                                                                    console.log("Add Abc = ",ai_text,index,model.protection.state)
                                                                    device_controller.aiflow_clicked("protection",model,false)
                                                                }
                                                                onCheckboxClicked: {
                                                                    device_controller.aiflow_clicked("protection",model,true)
                                                                }
                                                            }    
                                                            ButtonAIFlow {
                                                                ai_text: qsTr("Frequency")
                                                                buttonType: type
                                                                aiType: model.frequency.state
                                                                onClicked: {
                                                                    console.log("Add Abc = ",ai_text,index,model.frequency.state)
                                                                    device_controller.aiflow_clicked("frequency",model,false)
                                                                }
                                                                onCheckboxClicked: {
                                                                    device_controller.aiflow_clicked("frequency",model,true)
                                                                }
                                                            } 
                                                            ButtonAIFlow {
                                                                ai_text: qsTr("Access")
                                                                buttonType: type
                                                                aiType: model.access.state
                                                                onClicked: {
                                                                    console.log("Add Abc = ",ai_text,index,model.access.state)
                                                                    device_controller.aiflow_clicked("access",model,false)
                                                                }
                                                                onCheckboxClicked: {
                                                                    device_controller.aiflow_clicked("access",model,true)
                                                                }
                                                            } 
                                                            ButtonAIFlow {
                                                                ai_text: qsTr("Motion")
                                                                buttonType: type
                                                                aiType: model.motion.state
                                                                onClicked: {
                                                                    console.log("Add Abc = ",ai_text,index,model.motion.state)
                                                                    device_controller.aiflow_clicked("motion",model,false)
                                                                }
                                                                onCheckboxClicked: {
                                                                    device_controller.aiflow_clicked("motion",model,true)
                                                                }
                                                            } 
                                                            ButtonAIFlow {
                                                                ai_text: qsTr("Traffic")
                                                                buttonType: type
                                                                aiType: model.traffic.state
                                                                onClicked: {
                                                                    console.log("Add Abc = ",ai_text,index,model.traffic.state)
                                                                    device_controller.aiflow_clicked("traffic",model,false)
                                                                }
                                                                onCheckboxClicked: {
                                                                    device_controller.aiflow_clicked("traffic",model,true)
                                                                }
                                                            }
                                                            Item {
                                                                Layout.fillWidth: true
                                                            }
                                                        }
                                                    }
                                                }
                                                Item {
                                                    width: parent.width
                                                    height: 30  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                                    // anchors.fill: parent
                                                    visible: paneSettingsList1
                                                    RowLayout  {
                                                        anchors.verticalCenter: parent.verticalCenter
                                                        anchors.left: parent.left
                                                        anchors.right: parent.right
                                                        anchors.leftMargin: 30
                                                        spacing: 10

                                                        RowLayout {
                                                            spacing: 10
                                                            width: parent.width * 0.28
                                                            Layout.minimumWidth: width
                                                            Layout.maximumWidth: width
                                                            Image {
                                                                width: 24
                                                                height: 24
                                                                source: "../../../../src/assets/ai_icons/ic_risk_identification.svg" // Thay đổi icon dựa trên trạng thái
                                                                fillMode: Image.PreserveAspectFit
                                                                sourceSize: Qt.size(width, height)
                                                            }
                                                            CustomText {
                                                                Layout.preferredWidth:150
                                                                text: qsTr("Risk Recognition (%1)").arg(model.risk_identification || 0)
                                                                font.pixelSize: 12
                                                                color: textColor
                                                            }
                                                            Item{
                                                                Layout.fillWidth: true
                                                            }
                                                        }

                                                        RowLayout{
                                                            width: parent.width * 0.72
                                                            Layout.minimumWidth: width
                                                            Layout.maximumWidth: width
                                                            spacing: 10
                                                            ButtonAIFlow {
                                                                ai_text: qsTr("Weapon")
                                                                buttonType: type
                                                                aiType: model.weapon.state
                                                                onClicked: {
                                                                    console.log("Add Abc = ",ai_text,index,model.weapon.state)
                                                                    device_controller.aiflow_clicked("weapon",model,false)
                                                                }
                                                                onCheckboxClicked: {
                                                                    device_controller.aiflow_clicked("weapon",model,true)
                                                                }
                                                            }
                                                            ButtonAIFlow {
                                                                ai_text: qsTr("UFO")
                                                                buttonType: type
                                                                aiType: model.ufo.state
                                                                onClicked: {
                                                                    console.log("Add Abc = ",ai_text,index,model.ufo.state)
                                                                    device_controller.aiflow_clicked("ufo",model,false)
                                                                }
                                                                onCheckboxClicked: {
                                                                    device_controller.aiflow_clicked("ufo",model,true)
                                                                }
                                                            }
                                                            Item {
                                                                Layout.fillWidth: true
                                                            }
                                                        }
                                                    }
                                                }
                                                Repeater {
                                                    id: listSettings2
                                                    property bool showList: false
                                                    anchors.left: parent.left
                                                    anchors.right: parent.right
                                                    model: child
                                                    delegate: Column {
                                                        // property bool showList: false
                                                        // property bool paneSettingsList1: false
                                                        // spacing: 10
                                                        anchors.left: parent.left
                                                        anchors.right: parent.right
                                                        Item {
                                                            width: parent.width
                                                            height: 45  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                                            // anchors.fill: parent
                                                            MouseArea {
                                                                anchors.fill: parent
                                                                onClicked: {
                                                                    paneSettingsList2.shown = !paneSettingsList2.shown
                                                                    // console.log("visibleCameraList = ",name)
                                                                }
                                                            }
                                                            RowLayout  {
                                                                spacing: 0
                                                                anchors.fill: parent
                                                                // anchors.left: parent.left
                                                                // anchors.right: parent.right
                                                                // anchors.top: parent.top
                                                                // anchors.bottom: parent.bottom
                                                                // anchors.leftMargin: 30
                                                                // anchors.rightMargin: 30
                                                                CustomText {
                                                                    text: name
                                                                    Layout.fillWidth: true
                                                                    Layout.preferredWidth: 8
                                                                    font.pixelSize: 14
                                                                    elide: Text.ElideRight
                                                                    color: textColor
                                                                    // font.pixelSize: 12
                                                                }

                                                                CustomText {
                                                                    text: qsTr("Number of AI selected")
                                                                    Layout.fillWidth: true
                                                                    Layout.preferredWidth: 3
                                                                    color: textColor
                                                                    // font.pixelSize: 12
                                                                }
                                                                Image {
                                                                    width: 24
                                                                    height: 24
                                                                    source: "../../../../src/assets/ai_icons/ic_recognition_security.svg" // Thay đổi icon dựa trên trạng thái
                                                                    fillMode: Image.PreserveAspectFit
                                                                    sourceSize: Qt.size(width, height)
                                                                }
                                                                Item{
                                                                    Layout.preferredWidth: 4
                                                                }
                                                                CustomText {
                                                                    text: "(" + model.human_count + ")"
                                                                    Layout.fillWidth: true
                                                                    Layout.preferredWidth: 1
                                                                    color: textColor
                                                                    // font.pixelSize: 12
                                                                }
                                                                Item{
                                                                    Layout.preferredWidth: 20
                                                                }
                                                                Image {
                                                                    width: 24
                                                                    height: 24
                                                                    source: "../../../../src/assets/ai_icons/ic_risk_identification.svg" // Thay đổi icon dựa trên trạng thái
                                                                    fillMode: Image.PreserveAspectFit
                                                                    sourceSize: Qt.size(width, height)
                                                                }
                                                                Item{
                                                                    Layout.preferredWidth: 4
                                                                }
                                                                CustomText {
                                                                    text: "(" + model.vehicle_count + ")"
                                                                    Layout.fillWidth: true
                                                                    Layout.preferredWidth: 1
                                                                    color: textColor
                                                                    // font.pixelSize: 12
                                                                }
                                                                Item {
                                                                    Layout.fillWidth: true
                                                                    Layout.preferredWidth: 12
                                                                }
                                                                ControlGroup {
                                                                    Layout.leftMargin: 10
                                                                    isExpand: paneSettingsList2
                                                                }

                                                            }

                                                            Rectangle {
                                                                anchors.bottom: parent.bottom
                                                                anchors.left: parent.left
                                                                anchors.right: parent.right
                                                                // anchors.leftMargin: 30
                                                                // anchors.rightMargin: 30
                                                                height: 1  // Độ dày của line
                                                                color: headerBackground  // Màu của line
                                                            }
                                                        }
                                                        Pane {
                                                            id: paneSettingsList2

                                                            // ## relevant part ##
                                                            property bool shown: false
                                                            visible: height > 0
                                                            height: shown ? implicitHeight : 0
                                                            Behavior on height {
                                                                NumberAnimation {
                                                                    duration: 200
                                                                    easing.type: Easing.InOutQuad
                                                                }
                                                            }
                                                            clip: true
                                                            // ## relevant part ##

                                                            // Material.background: "lightblue"
                                                            padding: 10
                                                            anchors.left: parent.left
                                                            anchors.right: parent.right
                                                            background: Rectangle {
                                                                color: hoverColor
                                                                radius: 8
                                                            }
                                                            Column {
                                                                anchors.right: parent.right
                                                                anchors.left: parent.left
                                                                Item {
                                                                    width: parent.width
                                                                    height: 40  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                                                    // anchors.fill: parent
                                                                    visible: paneSettingsList2
                                                                    RowLayout  {
                                                                        anchors.verticalCenter: parent.verticalCenter
                                                                        anchors.left: parent.left
                                                                        anchors.right: parent.right
                                                                        spacing: 10
                                                                        Image {
                                                                            width: 24
                                                                            height: 24
                                                                            source: "../../../../src/assets/ai_icons/ic_recognition_security.svg" // Thay đổi icon dựa trên trạng thái
                                                                            fillMode: Image.PreserveAspectFit
                                                                            sourceSize: Qt.size(width, height)
                                                                        }
                                                                        CustomText {
                                                                            Layout.preferredWidth:150
                                                                            text: qsTr("Recognition & Protection (%1)").arg(model.recognition_protection || 0)
                                                                            font.pixelSize: 12
                                                                            color: textColor
                                                                        }

                                                                        Item{
                                                                            Layout.fillWidth: true
                                                                        }

                                                                        ButtonAIFlow {
                                                                            ai_text: qsTr("Recognition")
                                                                            buttonType: type
                                                                            aiType: model.recognition.state
                                                                            onClicked: {
                                                                                // console.log("Intrusion = ",device_controller.listmodel)
                                                                                device_controller.aiflow_clicked("recognition",model,false)
                                                                            }
                                                                            onCheckboxClicked: {
                                                                                device_controller.aiflow_clicked("recognition",model,true)
                                                                            }
                                                                        } 
                                                                        ButtonAIFlow {
                                                                            ai_text: qsTr("Protection")
                                                                            buttonType: type
                                                                            aiType: model.protection.state
                                                                            onClicked: {
                                                                                // console.log("Intrusion = ",device_controller.listmodel)
                                                                                device_controller.aiflow_clicked("protection",model,false)
                                                                            }
                                                                            onCheckboxClicked: {
                                                                                device_controller.aiflow_clicked("protection",model,true)
                                                                            }
                                                                        }   
                                                                        ButtonAIFlow {
                                                                            ai_text: qsTr("Frequency")
                                                                            buttonType: type
                                                                            aiType: model.frequency.state
                                                                            onClicked: {
                                                                                // console.log("Intrusion = ",device_controller.listmodel)
                                                                                device_controller.aiflow_clicked("frequency",model,false)
                                                                            }
                                                                            onCheckboxClicked: {
                                                                                device_controller.aiflow_clicked("frequency",model,true)
                                                                            }
                                                                        } 
                                                                        ButtonAIFlow {
                                                                            ai_text: qsTr("Access")
                                                                            buttonType: type
                                                                            aiType: model.access.state
                                                                            onClicked: {
                                                                                // console.log("Intrusion = ",device_controller.listmodel)
                                                                                device_controller.aiflow_clicked("access",model,false)
                                                                            }
                                                                            onCheckboxClicked: {
                                                                                device_controller.aiflow_clicked("access",model,true)
                                                                            }
                                                                        } 
                                                                        ButtonAIFlow {
                                                                            ai_text: qsTr("Motion")
                                                                            buttonType: type
                                                                            aiType: model.motion.state
                                                                            onClicked: {
                                                                                // console.log("Intrusion = ",device_controller.listmodel)
                                                                                device_controller.aiflow_clicked("motion",model,false)
                                                                            }
                                                                            onCheckboxClicked: {
                                                                                device_controller.aiflow_clicked("motion",model,true)
                                                                            }
                                                                        } 
                                                                        ButtonAIFlow {
                                                                            ai_text: qsTr("Traffic")
                                                                            buttonType: type
                                                                            aiType: model.traffic.state
                                                                            onClicked: {
                                                                                // console.log("Intrusion = ",device_controller.listmodel)
                                                                                device_controller.aiflow_clicked("traffic",model,false)
                                                                            }
                                                                            onCheckboxClicked: {
                                                                                device_controller.aiflow_clicked("traffic",model,true)
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                                Item {
                                                                    width: parent.width
                                                                    height: 40  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                                                    // anchors.fill: parent
                                                                    visible: paneSettingsList2
                                                                    RowLayout  {
                                                                        anchors.verticalCenter: parent.verticalCenter
                                                                        anchors.left: parent.left
                                                                        anchors.right: parent.right
                                                                        spacing: 10
                                                                        Image {
                                                                            width: 24
                                                                            height: 24
                                                                            source: "../../../../src/assets/ai_icons/ic_risk_identification.svg" // Thay đổi icon dựa trên trạng thái
                                                                            fillMode: Image.PreserveAspectFit
                                                                            sourceSize: Qt.size(width, height)
                                                                        }
                                                                        CustomText {
                                                                            Layout.preferredWidth:150
                                                                            text: qsTr("Risk Recognition (%1)").arg(model.risk_identification || 0)
                                                                            font.pixelSize: 12
                                                                            color: textColor
                                                                        }
                                                                        Item{
                                                                            Layout.fillWidth: true
                                                                        }
                                                                        ButtonAIFlow {
                                                                            ai_text: qsTr("Weapon")
                                                                            buttonType: type
                                                                            aiType: model.weapon.state
                                                                            onClicked: {
                                                                                // console.log("Intrusion = ",device_controller.listmodel)
                                                                                device_controller.aiflow_clicked("weapon",model,false)
                                                                            }
                                                                            onCheckboxClicked: {
                                                                                device_controller.aiflow_clicked("weapon",model,true)
                                                                            }
                                                                        }
                                                                        ButtonAIFlow {
                                                                            ai_text: qsTr("UFO")
                                                                            buttonType: type
                                                                            aiType: model.ufo.state
                                                                            onClicked: {
                                                                                // console.log("Intrusion = ",device_controller.listmodel)
                                                                                device_controller.aiflow_clicked("ufo",model,false)
                                                                            }
                                                                            onCheckboxClicked: {
                                                                                device_controller.aiflow_clicked("ufo",model,true)
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }

                                                }
                                            }
                                        }
                                    }

                                }
                            }
                        }
                    }
                }
            }
        }

        ScrollIndicator.vertical: ScrollIndicator { }

    }
}
