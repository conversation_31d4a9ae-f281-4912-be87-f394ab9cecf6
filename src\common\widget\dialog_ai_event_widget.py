import src.utils.log_utils as LogUtils
import logging
logger = logging.getLogger(__name__)
from concurrent.futures import ThreadPoolExecutor
import datetime
from enum import Enum
import json
import threading
from typing import List
from PySide6.QtWidgets import QApplication, QPushButton, QHBoxLayout, QDialog, QLabel, QWidget, QVBoxLayout, QFrame, QGraphicsView, QGraphicsScene, QGraphicsPixmapItem, QGraphicsTextItem,QSizePolicy
from PySide6.QtCore import Qt, QSize, QRect,QEvent, Slot
from PySide6.QtGui import QIcon, QPainter, QColor, QPixmap, QPainterPath, QGuiApplication
from src.common.controller.main_controller import main_controller
from src.common.model.event_data_model import CropItemObjectCrowd, EventAI, MetadataAccessControl, MetadataCrowed, MetadataHuman, MetadataTraffic
from src.styles.style import Style
from src.common.widget.image_widget import ImageLoader, ImageWidget
from src.utils.auth_qsettings import AuthQSettings

class EventAIType(Enum):
    FACE = 'HUMAN'
    ZONE_DETECT = 'ANPR'
    ACCESS_CONTROL = 'ACCESS_CONTROL'
    CROWD = 'CROWD'

# {"pedestrian", "motor", "bicycle", "car", "bus", "van", "truck", "container", "bagac", "cyclo", "ambulance", "firetruck", "wheelchair", "trashcar", "tanktruck", "mixertruck", "crane", "roller", "excavator", "streetvendor", "unknown"}
class TypeTraffic(Enum):
    PEDESTRIAN = 'pedestrian'
    MOTOR = 'motor'
    BICYCLE = 'bicycle'
    CAR = 'car'
    BUS = 'bus'
    VAN = 'van'
    TRUCK = 'truck'
    CONTAINER = 'container'
    BAGAC = 'bagac'
    CYCLO = 'cyclo'
    AMBULANCE = 'ambulance'
    FIRETRUCK = 'firetruck'
    WHEELCHAIR = 'wheelchair'
    TRASHCAR = 'trashcar'
    TANKTRUCK = 'tanktruck'
    MIXERTRUCK = 'mixertruck'
    CRANE = 'crane'
    ROLLER = 'roller'
    EXCAVATOR = 'excavator'
    STREETVENDOR = 'streetvendor'
    UNKNOWN = 'unknown'

class ColorTraffic(Enum):
    RED = 'red'
    BLUE = 'blue'
    GREEN = 'green'
    YELLOW = 'yellow'
    BLACK = 'black'
    WHITE = 'white'
    ORANGE = 'orange'

class LicensePlateType(Enum):
    RED = 'BIEN_DO'
    BLUE = 'BIEN_XANH'
    YELLOW = 'BIEN_VANG'
    NG = 'NGOAI_GIAO'

class EventDialog(QDialog):
    ENABLE_HEATMAP_CROWD = False
    def __init__(self, parent=None, event:EventAI =None, show_warning_info = False):
    # def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("Event"))
        # remove title bar
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.thread_pool = ThreadPoolExecutor(max_workers=8)
        # change color window title
        self.event_item = event
        self.show_warning_info = show_warning_info
        self.setup_ui()
        self.setup_stylesheet()


    def setup_ui(self):
        # get current screen size and resize window
        screen = QGuiApplication.primaryScreen()
        desktop_screen_size = screen.availableGeometry()
        # add title bar
        self.create_title_bar()
        self.is_warning_event = self.event_item.warning and self.show_warning_info

        self.event_type = EventAIType.FACE  # default
        self.event_type_name = self.tr("Face")
    
        # GET TYPE
        event_type = self.event_item.event_type
        if event_type == EventAIType.FACE.value:
            self.event_type = EventAIType.FACE
            self.event_type_name = self.tr("Face")
        elif event_type == EventAIType.ZONE_DETECT.value:
            self.event_type = EventAIType.ZONE_DETECT
            self.event_type_name = self.tr("License plate")
        elif event_type == EventAIType.ACCESS_CONTROL.value:
            self.event_type = EventAIType.ACCESS_CONTROL
            self.event_type_name = self.tr("Access control")
        elif event_type == EventAIType.CROWD.value:
            self.event_type = EventAIType.CROWD
            self.event_type_name = self.tr("Crowd")

        self.metadata_human = None
        self.metadata_license_plate = None
        self.metadata_crowd = None

        # GET IMAGE
        self.event_image_origin = self.event_item.image
        self.event_image_crop = self.event_item.crop_image

        # GET TIME
        # get time from UTC
        try:
            utc_datetime = datetime.datetime.fromisoformat(self.event_item.thoiGianXuatHien)
        except Exception as e:
            utc_datetime = datetime.datetime.strptime(
                self.time, "%Y-%m-%dT%H:%M:%S.%fZ")
            logger.error(f'Error when convert time: {e}')
        # convert to local timezone - 7
        local_datetime = utc_datetime.replace(tzinfo=datetime.timezone.utc).astimezone(tz=None)
        # get only dd/MM/YYYY - hh:mm:ss
        event_time = local_datetime.strftime("%d/%m/%Y - %H:%M:%S")
        self.event_time = event_time

        # GET NAME
        self.event_name = self.event_item.soCmt

        # GET CAMERA NAME
        self.event_camera = self.event_item.camera_model.camera_name

        self.warning_type = self.event_item.camera_model.warning_type

        self.times_between_event = self.event_item.camera_model.times_between_event

        self.event_num = self.event_item.camera_model.event_num

        self.time_range = self.event_item.camera_model.time_range

        # KB Hiển thị khi event là crowd
        if self.event_type == EventAIType.CROWD:
            # if contain RECOGNITION then warning type is Counting & Warning Group
            if self.event_item.profiles:
                self.warning_type = self.tr("Counting & Warning Group")
            else:
                self.warning_type = self.tr("Counting")
        else:
            # is NONE -> Not config warning
            # is ALL -> Whenever Vehicle Appears
            # is PROFILE -> Warning Group
            # is Frequency -> Frequency
            if self.event_item.camera_model.warning_type == 'NONE':
                self.warning_type = self.tr("Not config warning")
            elif self.event_item.camera_model.warning_type == 'ALL':
                self.warning_type = self.tr("Whenever Vehicle Appears")
            elif self.event_item.camera_model.warning_type == 'PROFILE':
                self.warning_type = self.tr("Warning Group")
            elif self.event_item.camera_model.warning_type == 'FREQUENCY':
                self.warning_type = self.tr("Frequency")

        # GET STATUS
        self.event_status = self.tr('Appreared')
        self.status_color = Style.PrimaryColor.status_appear

        # create a layout verical list QLabel
        self.group_list_layout = QVBoxLayout()

        self.event_group = ''

        self.event_relationship = self.tr('Registered')

        self.event_quantity_person = '0'
        
        self.license_plate_type_name = ''
        
        self.list_image_crowd: List[CropItemObjectCrowd] = []


        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)

        # horizontal layout
        self.image_result_widget = QWidget()
        self.image_result_layout = QHBoxLayout()
        self.image_result_layout.setAlignment(Qt.AlignCenter)
        self.image_result_layout.setContentsMargins(0, 0, 0, 0)
        self.image_result_layout.setSpacing(2)
        self.image_result_widget.setLayout(self.image_result_layout)

        image_origin = None
        image_face = None
        image_anpr = None
        image_crop_anpr = None
        image_heat_map = None

        self.video_crowd = None

        image_origin = ImageWidget(height=desktop_screen_size.height() * 0.4)
        image_origin.load_from_url(self.event_image_origin)
        if self.event_type == EventAIType.FACE:
            image_face = ImageWidget(height=desktop_screen_size.height() * 0.4)
            image_face.load_from_url(url=self.event_image_crop)

        if self.event_type == EventAIType.ZONE_DETECT or self.event_type == EventAIType.ACCESS_CONTROL:
            image_anpr = ImageWidget(height=desktop_screen_size.height() * 0.2)
            image_anpr.load_from_url(self.event_image_crop)
            image_crop_anpr = ImageWidget(height=desktop_screen_size.height() * 0.2)
        
        if self.event_type == EventAIType.CROWD:
            server_event = main_controller.api_client.server_event
            # video url
            video_url = server_event + self.event_item.video
            # if self.ENABLE_HEATMAP_CROWD:
            image_heat_map = ImageWidget(height=200)
            image_heat_map.load_from_url(self.event_image_crop)
            # self.video_crowd = VideoPlayerWidget(play_url=video_url, height=200, width=200, enable_control_bar=False)
            # self.video_crowd.setFixedSize(480, 270)
        self.layout_image_crop_frequency = QVBoxLayout()
        if self.event_item.warning_type == 'FREQUENCY':
            main_controller.get_waring_items_by_thread(id=self.event_item.id)

        if self.event_type == EventAIType.FACE:
            if self.event_item.camera_model.warning_type == 'FREQUENCY' and self.show_warning_info:
                image_labels = []
                for index, event in enumerate(main_controller.list_warnings_items_for_frequency.results):
                    image_frequency = ImageWidget(height=60, allow_click_to_show_origin=True, thread_pool=self.thread_pool, width_image=68)
                    ip_server = AuthQSettings.get_instance().load_ip_server()
                    port_server = AuthQSettings.get_instance().load_port_websocket()
                    url = event.crop_image
                    image_frequency.load_from_url(url)
                    image_labels.append(image_frequency)
                # Add the QLabel widgets to the layout
                for label in image_labels:
                    self.layout_image_crop_frequency.addWidget(label)
                self.image_result_layout.addWidget(image_origin)
                self.image_result_layout.addWidget(image_face)
                self.image_result_layout.addLayout(self.layout_image_crop_frequency)
            else:
                self.image_result_layout.addWidget(image_origin)
                self.image_result_layout.addWidget(image_face)
        elif self.event_type == EventAIType.ZONE_DETECT or self.event_type == EventAIType.ACCESS_CONTROL:
            if self.event_item.camera_model.warning_type == 'FREQUENCY' and self.show_warning_info:
                image_labels = []
                for index, event in enumerate(main_controller.list_warnings_items_for_frequency.results):
                    image_frequency = ImageWidget(height=60, allow_click_to_show_origin=True, thread_pool=self.thread_pool, width_image=68)
                    ip_server = AuthQSettings.get_instance().load_ip_server()
                    port_server = AuthQSettings.get_instance().load_port_websocket()
                    url = event.crop_image
                    image_frequency.load_from_url(url)
                    image_labels.append(image_frequency)
                # Add the QLabel widgets to the layout
                for label in image_labels:
                    self.layout_image_crop_frequency.addWidget(label)
                self.image_result_layout.addWidget(image_origin)
                lpr_layout = QVBoxLayout()
                lpr_layout.setContentsMargins(0, 0, 0, 0)
                lpr_layout.setSpacing(0)
                lpr_layout.addWidget(image_anpr)
                lpr_layout.addWidget(image_crop_anpr)
                self.image_result_layout.addLayout(lpr_layout)
                self.image_result_layout.addLayout(self.layout_image_crop_frequency)
            else:
                self.image_result_layout.addWidget(image_origin)
                lpr_layout = QVBoxLayout()
                lpr_layout.setContentsMargins(0, 0, 0, 0)
                lpr_layout.setSpacing(0)
                lpr_layout.addWidget(image_anpr)
                lpr_layout.addWidget(image_crop_anpr)
                self.image_result_layout.addLayout(lpr_layout)
        elif self.event_type == EventAIType.CROWD:
            # self.image_result_layout.addWidget(image_origin)
            # if self.ENABLE_HEATMAP_CROWD:
            self.image_result_layout.addWidget(image_heat_map)
            # self.image_result_layout.addWidget(self.video_crowd)
            logger.debug('event crowd: !!!!')
            
        self.left_info_widget = QWidget()
        self.right_info_widget = QWidget()
        self.bottom_info_widget = QWidget()
        self.left_info = QVBoxLayout()
        self.left_info.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.right_info = QVBoxLayout()
        self.right_info.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.left_info_widget.setLayout(self.left_info)
        self.right_info_widget.setLayout(self.right_info)

        self.ai_result_layout = QVBoxLayout()
        left_right_layout = QHBoxLayout()
        left_right_layout.addStretch(15)
        left_right_layout.addWidget(self.left_info_widget, 35)
        left_right_layout.addWidget(self.right_info_widget, 35)
        left_right_layout.addStretch(15)
        left_right_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.ai_result_layout.addLayout(left_right_layout,70)
        if self.event_type == EventAIType.CROWD:
            self.ai_result_layout.addWidget(self.bottom_info_widget,30)
        
        self.left_info.setContentsMargins(20, 5, 5, 5)
        self.left_info.setSpacing(5)
        self.right_info.setContentsMargins(20, 5, 5, 5)
        self.right_info.setSpacing(5)

        # AI Flows
        self.ai_flow_layout = QHBoxLayout()
        self.ai_flow_label = QLabel(self.tr("AI Flows: "))
        self.ai_flow_content = QLabel(self.event_type_name)
        self.ai_flow_layout.addWidget(self.ai_flow_label, 30)
        self.ai_flow_layout.addWidget(self.ai_flow_content, 70)

        # Warning context
        self.warning_context_layout = QHBoxLayout()
        self.warning_context_label = QLabel(self.tr("Warning context: "))
        self.warning_context_content = QLabel(self.warning_type)
        self.warning_context_layout.addWidget(self.warning_context_label, 30)
        self.warning_context_layout.addWidget(self.warning_context_content, 70)

        # edit text name
        self.text_face_name_layout = QHBoxLayout()
        self.text_face_name = QLabel(self.tr("Name: "))
        self.text_face_name_content = QLabel(self.event_name)
        self.text_face_name_layout.addWidget(self.text_face_name, 30)
        self.text_face_name_layout.addWidget(self.text_face_name_content, 70)

        # license plate
        self.text_license_plate_layout = QHBoxLayout()
        self.text_license_plate = QLabel(self.tr("License plate: "))
        self.text_license_plate_content = QLabel(self.event_name)
        self.text_license_plate_layout.addWidget(self.text_license_plate, 30)
        self.text_license_plate_layout.addWidget(self.text_license_plate_content, 70)

        self.quantity_layout = QHBoxLayout()
        self.text_quantity = QLabel(self.tr("Number of humans: "))
        self.text_quantity_content = QLabel(self.event_quantity_person)
        self.quantity_layout.addWidget(self.text_quantity, 30)
        self.quantity_layout.addWidget(self.text_quantity_content, 70)

        self.group_widget = QWidget()
        self.group_layout = QHBoxLayout()
        self.group_layout.setContentsMargins(0, 0, 0, 0)
        self.group_layout.setSpacing(0)
        self.group_label = QLabel(self.tr("Group: "))
        self.group_layout.addWidget(self.group_label, 30)
        self.group_layout.addLayout(self.group_list_layout, 70)
        self.group_widget.setLayout(self.group_layout)

        self.license_plate_type_layout = QHBoxLayout()
        self.text_license_plate_type = QLabel(self.tr("License plate type: "))
        self.text_license_plate_type_content = QLabel()
        self.license_plate_type_layout.addWidget(self.text_license_plate_type, 30)
        self.license_plate_type_layout.addWidget(self.text_license_plate_type_content, 70)

        self.create_group_list_layout(self.event_item)

        # self.event_item.metadata is json string
        # parser
        try:
            self.event_metadata_parser = json.loads(self.event_item.metadata)
            if self.event_type == EventAIType.FACE:
                self.metadata_human = MetadataHuman.from_dict(self.event_metadata_parser)
            elif self.event_type == EventAIType.LICENSE_PLATE or self.event_type == EventAIType.ACCESS_CONTROL:
                if self.event_type == EventAIType.LICENSE_PLATE:
                    self.metadata_license_plate = MetadataTraffic.from_dict(self.event_metadata_parser)
                if self.event_type == EventAIType.ACCESS_CONTROL:
                    self.metadata_license_plate = MetadataAccessControl.from_dict(self.event_metadata_parser)
                # get license plate x,y,w,h and crop
                self.license_plate_x = self.metadata_license_plate.license_plate_x
                self.license_plate_y = self.metadata_license_plate.license_plate_y
                self.license_plate_w = self.metadata_license_plate.license_plate_w
                self.license_plate_h = self.metadata_license_plate.license_plate_h
                if self.license_plate_x and self.license_plate_y and self.license_plate_w and self.license_plate_h:
                    image_crop_anpr.crop_rect = QRect(self.license_plate_x, self.license_plate_y, self.license_plate_w, self.license_plate_h)
                    image_crop_anpr.load_from_url(self.event_image_origin)
                if self.event_type == EventAIType.ACCESS_CONTROL:
                    license_plate_type_color = self.metadata_license_plate.check_in_type
                    if license_plate_type_color == LicensePlateType.RED.value:
                        self.license_plate_type_name = self.tr("Military") # Military
                    elif license_plate_type_color == LicensePlateType.BLUE.value:
                        self.license_plate_type_name = self.tr("Government agencies") # Government agencies
                    elif license_plate_type_color == LicensePlateType.YELLOW.value:
                        self.license_plate_type_name = self.tr("Service") # Service
                    elif license_plate_type_color == LicensePlateType.NG.value:
                        self.license_plate_type_name = self.tr("Diplomatic") # Service
                    self.text_license_plate_type_content.setText(self.license_plate_type_name)
            elif self.event_type == EventAIType.CROWD:
                self.metadata_crowd = MetadataCrowed.from_dict(self.event_metadata_parser)
                self.event_quantity_person = self.metadata_crowd.count
                self.list_image_crowd = self.metadata_crowd.crop_list
                logger.debug(f'load data from metadata: {self.list_image_crowd}')
        except Exception as e:
            logger.error(f'error when parse metadata: {e}')
            self.event_metadata_parser = {}
            
        #Type
        self.type_layout = QHBoxLayout()
        self.type_label = QLabel(self.tr("Type: "))
        self.type_content = QLabel('-')
        self.type_layout.addWidget(self.type_label, 30)
        self.type_layout.addWidget(self.type_content, 70)

        #Brand
        self.brand_layout = QHBoxLayout()
        self.brand_label = QLabel(self.tr("Branch: "))
        self.brand_content = QLabel('-')
        self.brand_layout.addWidget(self.brand_label, 30)
        self.brand_layout.addWidget(self.brand_content, 70)

        #Color
        self.color_layout = QHBoxLayout()
        self.color_label = QLabel(self.tr("Color: "))
        self.color_content = QLabel('-')
        self.color_layout.addWidget(self.color_label, 30)
        self.color_layout.addWidget(self.color_content, 70)

        if self.event_type == EventAIType.FACE:
            if self.is_warning_event:
                self.left_info.addLayout(self.ai_flow_layout, 25)
            self.left_info.addLayout(self.text_face_name_layout, 25)
            self.left_info.addWidget(self.group_widget, 25)
            self.left_info.addWidget(QLabel(), 25)
            self.left_info.addWidget(QLabel(), 25)
        elif self.event_type == EventAIType.ZONE_DETECT or self.event_type == EventAIType.ACCESS_CONTROL:
            if self.is_warning_event:
                self.left_info.addLayout(self.ai_flow_layout, 25)
            self.type_content.setText(self.convert_traffic_type_name(self.metadata_license_plate))
            self.brand_content.setText(self.convert_traffic_brand(self.metadata_license_plate))
            self.left_info.addLayout(self.text_license_plate_layout, 25)
            self.left_info.addLayout(self.type_layout, 25)
            self.left_info.addLayout(self.brand_layout, 25)
            self.left_info.addWidget(self.group_widget, 25)
        elif self.event_type == EventAIType.CROWD:
            self.text_face_name_content.setText(self.tr("CROWD"))
            self.left_info.addLayout(self.text_face_name_layout, 25)
            self.left_info.addLayout(self.quantity_layout, 25)
            self.left_info.addWidget(QLabel(), 25)
            self.left_info.addWidget(QLabel(), 25)
            self.text_quantity_content.setText(str(self.event_quantity_person))

        # status
        self.status_layout = QHBoxLayout()
        self.status_label = QLabel(self.tr("Status: "))
        self.status_content = QLabel(self.event_status)
        self.status_content.setWordWrap(True)
        status_content_layout = QVBoxLayout()
        status_content_layout.setAlignment(Qt.AlignLeft|Qt.AlignCenter)
        status_content_layout.addWidget(self.status_content)
        self.status_layout.addWidget(self.status_label, 30)
        self.status_layout.addLayout(status_content_layout, 70)
        self.status_background_color = Style.PrimaryColor.status_appear
        if self.event_type == EventAIType.ACCESS_CONTROL or self.event_type == EventAIType.FACE:
            # metadata_camera_model = self.event_item.camera_model.metadata
            camera_check_in_apply = self.event_item.is_checkin
            camera_check_out_apply = self.event_item.is_checkout
            if camera_check_in_apply:
                self.status_background_color = Style.PrimaryColor.status_checkin
                self.status_content.setText(self.tr("Checkin"))
            elif camera_check_out_apply:
                self.status_background_color = Style.PrimaryColor.status_checkout
                self.status_content.setText(self.tr("Checkout"))
            else:
                self.status_background_color = Style.PrimaryColor.status_appear
                self.status_content.setText(self.tr("Appear"))


        # camera
        self.camera_name_layout = QHBoxLayout()
        self.camera_label = QLabel("Camera:")
        self.camera_name_content = QLabel(self.event_camera)
        self.camera_name_layout.addWidget(self.camera_label, 30)
        self.camera_name_layout.addWidget(self.camera_name_content, 70)

        # time
        self.time_layout = QHBoxLayout()
        self.time_label = QLabel(self.tr("Time: "))
        self.time_content = QLabel(self.event_time)
        self.time_layout.addWidget(self.time_label, 30)
        self.time_layout.addWidget(self.time_content, 70)

        # Right Info
        if self.is_warning_event:
            self.right_info.addLayout(self.warning_context_layout, 25)
        self.right_info.addLayout(self.status_layout, 25)
        self.right_info.addLayout(self.camera_name_layout, 25)
        self.right_info.addLayout(self.time_layout, 25)
        if self.event_type == EventAIType.ZONE_DETECT or self.event_type == EventAIType.ACCESS_CONTROL:
            self.color_content.setText(self.convert_traffic_color(self.metadata_license_plate))
            self.right_info.addLayout(self.color_layout, 25)
            if self.event_type == EventAIType.ACCESS_CONTROL:
                self.right_info.addLayout(self.license_plate_type_layout, 25)
        else:
            self.right_info.addWidget(QLabel(), 25)


        if self.event_type == EventAIType.CROWD:
            # Bottom object
            image_scroll_area = ImageScrollWidget()
            image_scroll_area.setFixedHeight(150)
            image_scroll_area.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            # add to bottom widgett
            self.bottom_layout = QHBoxLayout()
            self.bottom_layout.setSpacing(0)
            self.bottom_layout.setContentsMargins(0, 0, 0, 0)
            self.bottom_info_widget.setLayout(self.bottom_layout)
            self.bottom_layout.addWidget(image_scroll_area)

            list_image_crowd_reformat: List[CropItemObjectCrowd] = []
            # add image from self.list_image_crowd to image_scroll_area
            for item in self.list_image_crowd:
                # item.image have . at first, need remove it to get full url
                url_not_have_dot = item.image[1:]
                url_image = url_not_have_dot
                list_image_crowd_reformat.append(CropItemObjectCrowd(item.id,url_image))
            image_scroll_area.add_list(list_image_crowd_reformat)

        # button Cancel
        self.select_button_layout = QHBoxLayout()
        
        self.button_cancel = QPushButton(self.tr("Cancel"))
        # border-radius: 5px; border color red, text color red, background color transparent
        
        self.button_cancel.clicked.connect(self.close)
        self.select_button_layout.addWidget(self.button_cancel)
        # align right
        self.select_button_layout.setAlignment(self.button_cancel, Qt.AlignRight)
        self.select_button_layout.setContentsMargins(0, 0, 20, 20)

        self.main_layout.addWidget(self.title_bar_widget, 5)
        # add layout
        self.main_layout.addWidget(self.image_result_widget, 30)
        # create divider
        self.divider = QFrame()
        self.divider.setFixedHeight(1)
        self.main_layout.addWidget(self.divider)
        self.main_layout.addLayout(self.ai_result_layout, 50)
        self.main_layout.addLayout(self.select_button_layout,10)
        self.setModal(False)
        # set layout
        self.setLayout(self.main_layout)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.resize(desktop_screen_size.width() * 0.7, desktop_screen_size.height() * 0.8)

        QApplication.instance().installEventFilter(self)
    # draw round corner for window
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setBrush(QColor(Style.PrimaryColor.white))
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(self.rect(), 10, 10)

    def setup_stylesheet(self):
        self.button_cancel.setStyleSheet(f"border-radius: 5px; border: 2px solid {Style.PrimaryColor.primary}; color: {Style.PrimaryColor.primary}; background-color: white; margin: 5px 5px 5px 5px")
        self.button_cancel.setFixedSize(96, 40)

        text_style = "font-family: Inter; font-size: 14px; font-weight: 400; line-height: 11px; letter-spacing: -0.015em; text-align: left;"
        # self.text_face_name.setStyleSheet(f"color: {Style.PrimaryColor.text_black}; font-weight: bold; {text_style}")
        # self.text_face_name_content.setStyleSheet(f"color: {Style.PrimaryColor.text_black}; {text_style}")

        # self.text_license_plate.setStyleSheet(f"color: {Style.PrimaryColor.text_black}; font-weight: bold; {text_style}")
        # self.text_license_plate_content.setStyleSheet(f"color: {Style.PrimaryColor.text_black}; {text_style}")

        # self.text_quantity.setStyleSheet(f"color: {Style.PrimaryColor.text_black}; font-weight: bold; {text_style}")
        # self.text_quantity_content.setStyleSheet(f"color: {Style.PrimaryColor.text_black}; {text_style}")

        # self.status_label.setStyleSheet(f"color: {Style.PrimaryColor.text_black}; font-weight: bold; {text_style}")
        self.status_content.setStyleSheet(
                f"""
                QLabel {{
                    background-color: {self.status_background_color};
                    color: {Style.PrimaryColor.white};
                    border-radius: 5px;
                    padding-left: 5px;
                    padding-right: 5px;
                    padding-top: 10px;
                    padding-bottom: 10px;
                }}
                """
            )
        
        
        # self.camera_label.setStyleSheet(f"color: {Style.PrimaryColor.text_black}; font-weight: bold; {text_style}")
        # self.camera_name_content.setStyleSheet(f"color: {Style.PrimaryColor.text_black}; {text_style}")

        # self.time_label.setStyleSheet(f"color: {Style.PrimaryColor.text_black}; font-weight: bold; {text_style}")
        # self.time_content.setStyleSheet(f"color: {Style.PrimaryColor.text_black}; {text_style}")
        self.divider.setStyleSheet(f"background-color: {Style.PrimaryColor.text_unselected};")

        self.setStyleSheet("""
            QDialog {
                background-color: transparent
            }
            """)

    def create_title_bar(self):
        # layout
        self.title_bar_widget = QWidget()
        self.title_bar_widget.setObjectName("title_bar")
        # set background Style.PrimaryColor.primary
        self.title_bar_widget.setStyleSheet(f"background-color: {Style.PrimaryColor.primary}; border-top-left-radius: 10px; border-top-right-radius: 10px;")

        self.title_bar_layout = QHBoxLayout()
        # event name
        self.title_name_label = QLabel(self.tr("Event Information"))
        self.title_name_label.setStyleSheet(f"color: Style.PrimaryColor.white; font-weight: bold")
        close_icon = QIcon(Style.PrimaryImage.close_dialog)
        self.close_button = QPushButton(close_icon, "")
        self.close_button.setIconSize(QSize(15, 15))
        self.close_button.setFixedSize(30, 30)
        self.close_button.setStyleSheet("background-color: transparent")
        self.close_button.clicked.connect(self.close)
        # add widget
        self.title_bar_layout.addWidget(self.title_name_label, 90)
        self.title_bar_layout.addWidget(self.close_button, 10)
        self.title_bar_widget.setLayout(self.title_bar_layout)

    def create_group_list_layout(self, event_item: EventAI):
        group_list: List[str] = []
        if event_item.profile_list:
            for item in event_item.profile_list:
                group_list.append(item.name)
        else:
            group_list.append(self.tr("No group"))

        self.group_list_layout.setContentsMargins(5,5,5,5)
        self.group_list_layout.setSpacing(5)

        for group in group_list:
            group_label = QLabel(group)
            # group_label.setObjectName(f'{group}')
            text_style = "font-family: Inter; font-size: 14px; font-weight: 400; line-height: 11px; letter-spacing: -0.015em; text-align: left;"
            if event_item.profile_list:
                group_label.setStyleSheet(
                    f"""
                    QLabel {{
                        background-color: {Style.PrimaryColor.primary};
                        color: {Style.PrimaryColor.white};
                        border-radius: 5px;
                        padding-left: 5px;
                        padding-right: 5px;
                    }}
                    """
                )
            else:
                group_label.setStyleSheet(
                    f"""
                    QLabel {{
                        background-color: {Style.PrimaryColor.text_unselected};
                        color: {Style.PrimaryColor.white};
                        border-radius: 5px;
                        padding-left: 5px;
                        padding-right: 5px;
                    }}
                    """
                )
            group_label.setFixedHeight(30)
            group_layout = QHBoxLayout()
            group_layout.addWidget(group_label, alignment=Qt.AlignLeft)
            self.group_list_layout.addLayout(group_layout)


    # PEDESTRIAN = 'pedestrian'
    # MOTOR = 'motor'
    # BICYCLE = 'bicycle'
    # CAR = 'car'
    # BUS = 'bus'
    # VAN = 'van'
    # TRUCK = 'truck'
    # CONTAINER = 'container'
    # BAGAC = 'bagac'
    # CYCLO = 'cyclo'
    # AMBULANCE = 'ambulance'
    # FIRETRUCK = 'firetruck'
    # WHEELCHAIR = 'wheelchair'
    # TRASHCAR = 'trashcar'
    # TANKTRUCK = 'tanktruck'
    # MIXERTRUCK = 'mixertruck'
    # CRANE = 'crane'
    # ROLLER = 'roller'
    # EXCAVATOR = 'excavator'
    # STREETVENDOR = 'streetvendor'
    # UNKNOWN = 'unknown'

    def convert_traffic_type_name(self, metadata: MetadataTraffic) -> str:
        if metadata.vehicle_name == TypeTraffic.CAR.value:
            return self.tr('Car')
        elif metadata.vehicle_name == TypeTraffic.MOTOR.value:
            return self.tr('Motor')
        elif metadata.vehicle_name == TypeTraffic.BICYCLE.value:
            return self.tr('Bicycle')
        elif metadata.vehicle_name == TypeTraffic.PEDESTRIAN.value:
            return self.tr('Pedestrian')
        elif metadata.vehicle_name == TypeTraffic.TRUCK.value:
            return self.tr('Truck')
        elif metadata.vehicle_name == TypeTraffic.BUS.value:
            return self.tr('Bus')
        elif metadata.vehicle_name == TypeTraffic.VAN.value:
            return self.tr('Van')
        elif metadata.vehicle_name == TypeTraffic.CONTAINER.value:
            return self.tr('Container truck')
        elif metadata.vehicle_name == TypeTraffic.BAGAC.value:
            return self.tr('Delivery tricycles')
        elif metadata.vehicle_name == TypeTraffic.CYCLO.value:
            return self.tr('Cyclo')
        elif metadata.vehicle_name == TypeTraffic.AMBULANCE.value:
            return self.tr('Ambulance')
        elif metadata.vehicle_name == TypeTraffic.FIRETRUCK.value:
            return self.tr('Fire truck')
        elif metadata.vehicle_name == TypeTraffic.WHEELCHAIR.value:
            return self.tr('Wheelchair')
        elif metadata.vehicle_name == TypeTraffic.TRASHCAR.value:
            return self.tr('Trash car')
        elif metadata.vehicle_name == TypeTraffic.TANKTRUCK.value:
            return self.tr('Tank truck')
        elif metadata.vehicle_name == TypeTraffic.MIXERTRUCK.value:
            return self.tr('Mixer truck')
        elif metadata.vehicle_name == TypeTraffic.CRANE.value:
            return self.tr('Crane')
        elif metadata.vehicle_name == TypeTraffic.ROLLER.value:
            return self.tr('Roller')
        elif metadata.vehicle_name == TypeTraffic.EXCAVATOR.value:
            return self.tr('Excavator')
        elif metadata.vehicle_name == TypeTraffic.STREETVENDOR.value:
            return self.tr('Street Vendor')
        else:
            return self.tr('Undefined')

        
    def convert_traffic_color(self, metadata: MetadataTraffic) -> str:
        if metadata.vehicle_color == ColorTraffic.BLACK.value:
            return self.tr('Black')
        elif metadata.vehicle_color == ColorTraffic.WHITE.value:
            return self.tr('White')
        elif metadata.vehicle_color == ColorTraffic.RED.value:
            return self.tr('Red')
        elif metadata.vehicle_color == ColorTraffic.BLUE.value:
            return self.tr('Blue')
        elif metadata.vehicle_color == ColorTraffic.GREEN.value:
            return self.tr('Green')
        elif metadata.vehicle_color == ColorTraffic.YELLOW.value:
            return self.tr('Yellow')
        elif metadata.vehicle_color == ColorTraffic.ORANGE.value:
            return self.tr('Orange')
        else:
            return self.tr('Undefined')

    def convert_traffic_brand(self, metadata: MetadataTraffic) -> str:
        # convert String to Capitalize only first letter
        if metadata.vehicle_brand:
            return metadata.vehicle_brand.capitalize()
        
    def eventFilter(self, source, event):
        if event.type() == QEvent.MouseButtonPress:
            # Kiểm tra xem vị trí của sự kiện chuột có nằm trong vùng của cửa sổ cha hay không

            global_pos = event.globalPos()
            if not self.geometry().contains(global_pos):
                QApplication.instance().removeEventFilter(self)
                self.close()
        return super().eventFilter(source, event)

def show_dialog():
    dialog = EventDialog()
    dialog.exec()


    

class ImageScrollWidget(QWidget):
    def __init__(self):
        super().__init__()

        self.initUI()

    def initUI(self):
        self.layout = QHBoxLayout()
        self.scroll_area = QGraphicsView(self)
        self.x_position = 0  # Initialize the x-coordinate position
        self.scene = QGraphicsScene()
        self.scroll_area.setScene(self.scene)
        # hide scrollbars and stroke
        self.scroll_area.setStyleSheet("border: 0px; background: transparent;")
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        self.layout.addWidget(self.scroll_area)
        self.setLayout(self.layout)

        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

    def add_image(self, pixmap, id):
        logger.debug(f'height: {self.height()}')

        item = CircularImageItem(pixmap, id)
        item.setPos(self.x_position, 0)  # Set the position of the item
        self.scene.addItem(item)
        item_text = QGraphicsTextItem(str(id))
        item_text.setPos(self.x_position, 80)
        self.scene.addItem(item_text)
        self.x_position += 80  # Adjust this value to match the spacing

    def add_list(self, image_list: List[CropItemObjectCrowd]):
        self.x_position = 0  # Initialize the x-coordinate position
        self.thread_pool = ThreadPoolExecutor(max_workers=8)
        for item in image_list:
            self.image_loader = ImageLoader(url=item.image, height=70, id=item.id)
            self.image_loader.custom_finished_data.connect(self._on_image_loaded)
            if self.thread_pool:
                self.thread_pool.submit(self.image_loader.load_image)
            else:
                thread = threading.Thread(target=self.image_loader.load_image)
                thread.start()

    @Slot(QPixmap)
    def _on_image_loaded(self, pixmap, id):
        logger.debug('add_list: loaded')
        self.add_image(pixmap, id)


class CircularImageItem(QGraphicsPixmapItem):
    def __init__(self, pixmap, id=''):
        super().__init__()
        self.pixmap = pixmap
        self.id = id
        self.radius = min(self.pixmap.width(), self.pixmap.height()) / 2

    def boundingRect(self):
        return self.pixmap.rect()

    def paint(self, painter: QPainter, option, widget):
        clip_path = QPainterPath()
        clip_path.addEllipse(self.boundingRect())

        # Save the current painter state
        painter.save()
        painter.setClipPath(clip_path)

        # Draw the clipped image
        painter.drawPixmap(self.boundingRect(), self.pixmap)

        # Restore the painter state
        painter.restore()
