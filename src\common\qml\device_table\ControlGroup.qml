import QtQuick
import QtQuick.Controls
import QtQuick.Layouts

RowLayout {
    property string up_arrow_button: device_controller ? device_controller.get_image_theme_by_key("up_arrow") : ""
    property string down_arrow_button: device_controller ? device_controller.get_image_theme_by_key("down_arrow") : ""
    property string editIcon: device_controller ? device_controller.get_image_theme_by_key("edit_device_item") : ""
    property string deleteIcon: device_controller ? device_controller.get_image_theme_by_key("delete_device_item") : ""

    Connections {
        target: device_controller
        function onThemeChanged() {
            up_arrow_button = device_controller.get_image_theme_by_key("up_arrow")
            down_arrow_button = device_controller.get_image_theme_by_key("down_arrow")
            editIcon = device_controller.get_image_theme_by_key("edit_device_item")
            deleteIcon = device_controller.get_image_theme_by_key("delete_device_item")
        }
    }
    id: rowLayout
    // anchors.fill: parent
    spacing: 10
    // Layout.leftMargin:20
    // Layout.rightMargin:20
    // Layout.horizontalStretchFactor: 1
    property var isExpand

    function getIcon(isExpand) {
        if (isExpand !== undefined) {
            return isExpand.shown? up_arrow_button : down_arrow_button
        }else{
            return down_arrow_button
        }
    }

    ActionButton {
        icon.source: editIcon
        modelType: model.type
        onClicked: {

            console.log("state = ",model.type)
            // root.editData()
            device_controller.action_clicked("Edit",model)
        }
    }
    ActionButton {
        icon.source: deleteIcon
        modelType: model.type
        onClicked: {
            console.log("state = ")
            device_controller.action_clicked("Delete",model)
        }
    }
    ActionButton {
        icon.source: getIcon(isExpand)
        modelType: model.type
        onClicked: {
            isExpand.shown = !isExpand.shown
            device_controller.action_clicked("Up",model)
        }
    }
}