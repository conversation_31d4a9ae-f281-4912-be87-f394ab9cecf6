
import json
import pickle
import time
import enum
import copy
from enum import IntEnum
from PySide6.QtCore import QObject, Property, Signal,Slot,QByteArray, QEnum, QAbstractListModel, Qt, QModelIndex
from src.common.model.camera_model import CameraModel,camera_model_manager,Camera
from src.common.model.model import Model
from src.common.controller.main_controller import main_controller
from src.common.threads.sub_thread import SubThread
from src.utils.utils import Utils
from src.common.model.main_tree_view_model import TreeType
import logging
logger = logging.getLogger(__name__)
class CameraListModel(QAbstractListModel):
    ObjectRole = Qt.UserRole + 1
    def __init__(self, parent=None):
        super().__init__(parent)
        self._items = []

    def rowCount(self, parent=QModelIndex()):
        return len(self._items)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or not (0 <= index.row() < len(self._items)):
            return None

        if role == self.ObjectRole:
            return self._items[index.row()]
        return None

    def roleNames(self):
        return {
            self.ObjectRole: b"model"
        }

    def appendCamera(self, camera):
        self.beginInsertRows(QModelIndex(), len(self._items), len(self._items))
        self._items.append(camera)
        self.endInsertRows()

    def removeCamera(self, camera):
        for idx, item in enumerate(self._items):
            if item.id == camera.id:
                self.beginRemoveRows(QModelIndex(), idx, idx)
                self._items.pop(idx)
                self.endRemoveRows()
                return

    def clear(self):
        self.beginResetModel()
        self._items.clear()
        self.endResetModel()

    def getItems(self):
        return self._items
    
    def replaceCamera(self, index, new_camera):
        if 0 <= index < len(self._items):
            self._items[index] = new_camera
class MapState(QObject):
    class ViewMode(IntEnum):
        NORMAL = 0
        FULLSCREEN = 1
    QEnum(ViewMode)

    class LockMode(IntEnum):
        UNLOCK = 0
        LOCK = 1
    QEnum(LockMode)

    class EditMode(IntEnum):
        READ_ONLY = 0
        EDITABLE = 1
    QEnum(EditMode)

    class notifyKey(IntEnum):
        SelectGridType = 0
        SelectEditMode = 1
        MapSavedSuccessfully = 2
        MapSaveFailed = 3
        LocationAlert = 4

    QEnum(notifyKey)

    viewModeChanged = Signal()
    editModeChanged = Signal()
    lockModeChanged = Signal()
    notifyChanged = Signal(int)
    saveDataSignal = Signal()
    dropEventChanged = Signal(QByteArray)
    themeChanged = Signal()

    def __init__(self):
        super().__init__()
        self._editMode = False
        self._viewMode = False
        self._lockMode = True
        main_controller.theme_change_signal.connect(self.themeChanged)

    @Property(bool,notify=editModeChanged)
    def editMode(self):
        return self._editMode
    
    @editMode.setter
    def editMode(self, value: bool):
        if self._editMode != value:
            self._editMode = value
            if self._editMode:
                self.lockMode = False
            self.editModeChanged.emit()

    @Property(bool,notify=lockModeChanged)
    def lockMode(self):
        return self._lockMode
    
    @lockMode.setter
    def lockMode(self, value: bool):
        if self._lockMode != value:
            self._lockMode = value
            self.lockModeChanged.emit()

    @Property(bool,notify=viewModeChanged)
    def viewMode(self):
        return self._viewMode
    
    @viewMode.setter
    def viewMode(self, value: bool):
        if self._viewMode != value:
            self._viewMode = value
            self.viewModeChanged.emit()

    @Slot(str, result=str)
    def get_color_theme_by_key(self, key=None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Color", key)
    
    @Slot(str, result=str)
    def get_image_theme_by_key(self, key=None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Image", key)
     
class SizeCameraItem(enum.Enum):
    SMALL = 1
    MEDIUM = 2
    LARGE = 3

class FovModeItem(enum.Enum):
    ICON = 'ICON'
    RECTANGLE = 'RECTANGLE'
    CIRCLE = 'CIRCLE'
    POLYGON = 'POLYGON'

class TypeCameraItem(enum.Enum):
    PTZ = 0
    BULLET = 1
    DOME = 2

class CameraColor(enum.Enum):
    ORANGE = "#FF9A24"
    PURPLE = "#675DF0"
    RED = "#FF6969"
    BLUE = "#1191E3"
    PINK = "#EF5E9A"
    CYAN = "#109FA1"
    GREEN = "#31B23A"
    
class FloorModel(Model):
    nameChanged = Signal()
    fileLinkChanged = Signal()
    listCamerasChanged  = Signal()
    themeChanged = Signal()
    updateCameraFloorTreeview = Signal(QObject, list, QObject)
    
    def __init__(self,data: dict = {},controller = None):
        super().__init__(data = data)

        self.controller = controller
        self.id = None
        self.serverIp = None
        self._name = None
        self._fileLink = None
        self._cameras = []
        self.buildingModel = None
        self.synchronize_floor_number = 0
        self._current_new_list_cameras = []
        main_controller.theme_change_signal.connect(self.themeChanged)
        self.load_data()

    def set_building_model(self, buildingModel):
        self.buildingModel = buildingModel

    def load_data(self):
        if self.data is not None:
            self.id = self.data.get("id",None)
            self.serverIp = self.data.get("serverIp",None)
            self._name = self.data.get("name",None)
            self._fileLink = self.data.get("fileLink",None)
            list_cameraIds = self.data.get("cameraIds",None)
            for camera_id in list_cameraIds:
                camera_model = camera_model_manager.get_camera_model(id = camera_id)
                self._cameras.append(camera_model.data)

    def replace_data(self, data):
        if data is not None:
            self.id = data.get("id",None)
            self.serverIp = data.get("serverIp",None)
            self._name = data.get("name",None)
            self._fileLink = data.get("fileLink",None)
            self._cameras = []
            list_cameraIds = data.get("cameraIds",None)
            for camera_id in list_cameraIds:
                camera_model = camera_model_manager.get_camera_model(id = camera_id)
                self._cameras.append(camera_model.data)

    @Property(str,notify=nameChanged)
    def name(self):
        return self._name
    
    @name.setter
    def name(self, value: str):
        if self._name != value:
            self._name = value
            self.nameChanged.emit() 

    @Property(str,notify=fileLinkChanged)
    def fileLink(self):
        return self.get_property("fileLink",None)
    
    @fileLink.setter
    def fileLink(self, value: str):
        if self.get_property("fileLink",None) != value:
            self.set_property("fileLink",value)
            self.fileLinkChanged.emit() 

    def getCameras(self):
        new_list = []
        for item in self._cameras:
            new_list.append(item)
        return new_list
    
    def removeCamera(self,camera_id):
        for item in self._cameras:
            if camera_id == item.get("id"):
                self._cameras.remove(item)
                break

    def addCamera(self, cameraModel:CameraModel):
        self._cameras.append(cameraModel.data) 
        self.listCamerasChanged.emit() 

    def get_camera_models(self):
        return self._cameras

    cameras = Property(list, getCameras, notify=listCamerasChanged)
    
    def to_dict(self):
        list_id = []
        for item in self._cameras:
            list_id.append(item.get("id",None))
        return {
            "id": self.id,
            "serverIp": self.serverIp,
            "name": self._name,
            "fileLink": self._fileLink,
            "cameraIds": list_id,
        }
    
    @Slot(str, result=str)
    def get_color_theme_by_key(self, key=None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Color", key)
    
    @Slot(str, result=str)
    def get_image_theme_by_key(self, key=None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Image", key)
    
    def update_list_cameras(self, new_list_cameras):
        self._cameras = new_list_cameras
        self.listCamerasChanged.emit()

    def process_remove_camera_on_floor(self, cameras):
        list_cam_ids = []
        for camera in cameras:
            list_cam_ids.append(camera["id"] if isinstance(camera,dict) else camera.id)
        new_list_cameras = []
        for current_camera in self._cameras:
            if current_camera.get("id",None) in list_cam_ids:
                continue
            new_list_cameras.append(current_camera)
        self.update_list_cameras(new_list_cameras)
    
    def diff_floor_model(self, floor:dict = {}):
        diff = []
        for field,value in floor.items():
            if self.data[field] != value:
                if field == 'name':
                    diff.append(field)
                    self.name = value
                elif field == 'fileLink':
                    diff.append(field)
                    self.fileLink = value
                elif field == 'level':
                    diff.append(field)
                    self.set_property("level",value)
                elif field == 'cameraIds':
                    diff.append(field)
                    self.set_property("cameraIds",value)
        return diff
class FloorManager(QObject):
    __instance = None
    addFloorList = Signal(tuple)
    createFloorWSSignal = Signal(tuple)
    removeFloorWSSignal = Signal(tuple)
    updateFloorWSSignal = Signal(tuple)
    def __init__(self):
        super().__init__()
        self.data = {}
        self.addFloorList.connect(self.addFloorListSignal)

    @staticmethod
    def get_instance():
        if FloorManager.__instance is None:
            FloorManager.__instance = FloorManager()
        return FloorManager.__instance
    
    def addFloorListSignal(self, data):
        data, controller = data
        for item in data:
            floor_model = FloorModel(data = item,controller = controller)
            floor_model.serverIp = controller.server.data.server_ip
            floor_model.set_property("clientId",Utils.clientId)
            floor_manager.addFloor(floor_model)

    def addFloor(self, floor_model: FloorModel):
        if floor_model.serverIp in self.data:
            self.data[floor_model.serverIp][floor_model.id] = floor_model
        else:
            self.data[floor_model.serverIp] = {}
            self.data[floor_model.serverIp][floor_model.id] = floor_model

    def modify_floor_camera_list(self, floor_model: FloorModel):
        if floor_model.serverIp in self.data:
            self.data[floor_model.serverIp][floor_model.id]._cameras = floor_model._cameras
            self.data[floor_model.serverIp][floor_model.id].listCamerasChanged.emit()


    def removeFloor(self, floor_model: FloorModel):
        if floor_model.serverIp in self.data:
            if floor_model.id in self.data[floor_model.serverIp]:
                del self.data[floor_model.serverIp][floor_model.id]

    def getFloor(self,id = None, cameraModel = None,camera_id = None):
        if id is not None:
            for serverIp,item in self.data.items():
                if id in item:
                    return item[id]
            return None
        if cameraModel is not None:
            if cameraModel.data.server_ip in self.data:
                for floor_id,floorModel in self.data[cameraModel.data.server_ip].items():
                    if cameraModel in floorModel._cameras:
                        return floorModel
            return None
        if camera_id is not None:
            temp = []
            for serverIp,listFloors in self.data.items():
                for floor_id,floorModel in listFloors.items():
                    for cameraModel in floorModel._cameras:
                        if camera_id == cameraModel.get("id",None):
                            temp.append(floorModel)
                            break
            return temp
        
    def to_dict(self):
        dist = {}
        for serverIp, list_floor in self.data.items():
            temp = {}
            for id, floor_model in list_floor.items():
                temp[floor_model.id] = floor_model.to_dict()
            dist[serverIp] = temp
        return dist 
    
    def delete_server(self,serverIp = None):
        if serverIp in self.data:
            del self.data[serverIp]

floor_manager = FloorManager.get_instance()  

class BuildingModel(Model):
    nameChanged = Signal()
    longitudeChanged = Signal()
    latitudeChanged = Signal()
    locationChanged = Signal()
    floorIdsChanged = Signal()

    def __init__(self,data: dict = {}):
        super().__init__(data = data)
        self.id = None
        self.serverIp = None
        self._name = None
        self._longitude = None
        self._latitude = None
        self._location = None
        self._floorIds = []
        self.buildingModel = None
        self.load_data()

    def load_data(self):
        if self.data is not None:
            self.id = self.data.get("id",None)
            self.serverIp = self.data.get("serverIp",None)
            self.getFloors()

    def resgiterOriginBuildingModel(self, buildingModel):
        self.buildingModel = buildingModel
        self.buildingModel.floorIdsChanged.connect(self.syncfloorIdsFromOrigin)

    def syncfloorIdsFromOrigin(self):
        if self.buildingModel is not None:
            self._floorIds.clear()
            for idx,floorModel in enumerate(self.buildingModel.floorIds):
                self._floorIds.append(floorModel)
            self.floorIdsChanged.emit()

    def getFloors(self):
        list_id = self.data.get("floorIds",[])   
        for floor_id in list_id:
            floor_model = floor_manager.getFloor(id = floor_id)
            floor_model.set_building_model(self)
            self.appendfloorIds(floor_model)

    @Property(str,notify=nameChanged)
    def name(self):
        return self.get_property("name","null")
    
    @name.setter
    def name(self, value: str):
        if self.get_property("name","null") != value:
            self.set_property("name",value)
            self.nameChanged.emit() 

    @Property(float,notify=longitudeChanged)
    def longitude(self):
        return self.get_property("longitude",0)
    
    @longitude.setter
    def longitude(self, value: float):
        if self.get_property("longitude",0) != value:
            self.set_property("longitude",value)
            self.longitudeChanged.emit() 

    @Property(float,notify=latitudeChanged)
    def latitude(self):
        return self.get_property("latitude",0)
    
    @latitude.setter
    def latitude(self, value: float):
        if self.get_property("latitude",0) != value:
            self.set_property("latitude",value)
            self.latitudeChanged.emit() 

    @Property(float,notify=locationChanged)
    def location(self):
        return self.get_property("location",None)
    
    @location.setter
    def location(self, value: float):
        if self.get_property("location",0) != value:
            self.set_property("location",value)
            self.locationChanged.emit() 

    def getfloorIds(self):
        return self._floorIds

    def appendfloorIds(self, item):
        if isinstance(item, FloorModel):
            self._floorIds.append(item)
            floor_manager.addFloor(item)
            self.floorIdsChanged.emit()  # Thông báo danh sách đã thay đổi

    def removeFloor(self, item = None,controller = None):
        if item is None:
            for floor_model in self._floorIds:
                if controller is not None:
                    subThread = SubThread(parent=self,target=controller.delete_floor,args=(floor_model.id,))
                    subThread.start()
                floor_manager.removeFloor(floor_model=floor_model)
            self._floorIds = []  
            self.floorIdsChanged.emit() 
        if isinstance(item, FloorModel):
            self._floorIds.remove(item)
            floor_manager.removeFloor(item)
            self.floorIdsChanged.emit()  # Thông báo danh sách đã thay đổi

    def clearfloorIds(self):
        self._floorIds.clear()
        self.floorIdsChanged.emit()

    floorIds = Property(list, getfloorIds, notify=floorIdsChanged)

    def diffBuildingModel(self, building:dict = {}):
        diff = []
        floorIdAdded = None
        for field,value in building.items():
            if self.data[field] != value:
                logger.debug(f"Field {field} changed from {self.data[field]} to {value}")
                if field == 'name':
                    diff.append(field)
                    self.name = value
                elif field == 'longitude':
                    diff.append(field)
                    self.longitude = value
                elif field == 'latitude':
                    diff.append(field)
                    self.latitude = value
                elif field == 'floorDTOs':
                    diff.append(field)
                    self.data[field] = value
                elif field == 'floorIds':
                    diff.append(field)
                    # case này là khởi tạo 1 floor
                    for x in value:
                        if x not in self.data[field]:
                            floorIdAdded = x
                            break
                        
                    self.data[field] = value
                else:
                    diff.append(field)
                    if field != 'clientId':
                        self.data[field] = value          
        if floorIdAdded is not None:
            floorDTOs = self.get_property("floorDTOs",None) if self.get_property("floorDTOs",None) is not None else []
            for floorDTO in floorDTOs:
                id = floorDTO.get("id",None)
                if id == floorIdAdded:
                    clientId = floorDTO.get("clientId")
                    if clientId != Utils.clientId:
                        floor_manager.createFloorWSSignal.emit((self.serverIp,floorDTO,self))
                    break
        return diff
    
    def isDataChanged(self, building:dict = {}):
        diff = []
        for field,value in building.items():
            if self.data[field] != value:
                logger.debug(f"Field {field} changed from {self.data[field]} to {value}")
                if field == 'name':
                    diff.append(field)
                elif field == 'longitude':
                    diff.append(field)
                elif field == 'latitude':
                    diff.append(field)
                else:
                    if field != "serverIp" or filter != "clientId":
                        diff.append(field)
        return len(diff) != 0
    
    def to_dict(self):
        list_id = []
        for item in self.floorIds:
            list_id.append(item.id)
        return {
            "id": self.id,
            # "serverIp": self.serverIp,
            "name": self.get_property("name",None),
            "latitude": self.get_property("latitude",None),
            "longitude": self.get_property("longitude",None),
            "location": self.get_property("location",None),
            "floorIds": list_id,
        }
class BuildingManager(QObject):
    addBuildingList = Signal(tuple)
    createBuildingWSSignal = Signal(tuple)
    removeBuildingWSSignal = Signal(tuple)
    updateBuildingWSSignal = Signal(tuple)
    __instance = None
    def __init__(self):
        super().__init__()
        self.data = {}
        self.addBuildingList.connect(self.addBuildingListSignal)


    @staticmethod
    def get_instance():
        if BuildingManager.__instance is None:
            BuildingManager.__instance = BuildingManager()
        return BuildingManager.__instance
    
    def addBuildingListSignal(self,data):
        data,controller = data
        for item in data:
            building_model = BuildingModel(data=item)
            building_model.serverIp = controller.server.data.server_ip
            building_model.set_property("clientId",Utils.clientId)
            building_manager.addBuilding(building_model)

    def addBuilding(self, building_model: BuildingModel):
        if building_model.serverIp in self.data:
            self.data[building_model.serverIp][building_model.id] = building_model
        else:
            self.data[building_model.serverIp] = {}
            self.data[building_model.serverIp][building_model.id] = building_model

    def getBuilding(self,id = None, floorModel = None):
        if id is not None:
            for serverIp,item in self.data.items():
                if id in item:
                    return item[id]
        elif floorModel is not None:
            for serverIp,item in self.data.items():
                for buidingId, buidingModel in item.items():
                    if floorModel in buidingModel._floorIds:
                        return buidingModel
        return None
            
    def removeBuilding(self, building_model: BuildingModel):
        if building_model.serverIp in self.data:
            if building_model.id in self.data[building_model.serverIp]:
                building_model.removeFloor()
                del self.data[building_model.serverIp][building_model.id]

    def removeFloor(self, floor_model: FloorModel, controller = None):
        for serverIp, list_building in self.data.items():
            for id, building_model in list_building.items():
                if floor_model in building_model.floorIds:
                    building_model.removeFloor(item = floor_model,controller = controller)
                    return

    def to_dict(self):
        dist = {}
        for serverIp, list_building in self.data.items():
            temp = {}
            for id, building_model in list_building.items():
                temp[building_model.id] = building_model.to_dict()
            dist[serverIp] = temp
        return dist 
    
    def getBuildingList(self,serverIp = None):
        if serverIp in self.data:
            return self.data[serverIp]
        else:
            return {}
        
    def delete_server(self,serverIp = None):
        if serverIp in self.data:
            del self.data[serverIp]

building_manager = BuildingManager.get_instance() 

class MapModel(QObject):
    idChanged = Signal()
    cameraIdsChanged = Signal()
    buildingIdsChanged = Signal()
    newCameraChanged = Signal(QObject)
    removeCameraChanged = Signal(QObject)
    removeListCameraChanged = Signal(str,list)
    addCameraChanged = Signal(QObject)
    addBuildingChanged = Signal(QObject)
    removeBuildingChanged = Signal(QObject)
    clearCameraIdsChanged = Signal(str)
    notifyChanged = Signal(int)
    accessTokenChanged = Signal(str)
    
    def __init__(self,data: dict = None,controller = None):
        super().__init__()
        self.data = data
        self._id = None
        self.serverIp = None
        self._name = None
        self._buildingIds = []
        self._cameraIds = CameraListModel(parent=self)
        self._previousCameraLocation = {}
        self.cloned_model = None
        self.controller = controller
        self.listCameraIdsRemoved = []
        self._accessToken = None
        self.load_data()

    def load_data(self):
        if self.data is not None:
            self._id = self.data.get("id",None)
            if self.controller is not None:
                self.serverIp = self.controller.server.data.server_ip
            self._name = self.data.get("name",None)
            self.getBuildings()
            self.getCameras()
            
    def getBuildings(self):
        list_buildings = building_manager.getBuildingList(serverIp=self.serverIp)
        for id, buildingModel in list_buildings.items():
            self.appendBuildingIds(buildingModel)

    def getCameras(self): 
        list_cameras = camera_model_manager.get_camera_list(server_ip=self.serverIp)
        for id, cameraModel in list_cameras.items():
            if cameraModel.get_property("coordinateLat") is not None:
                if cameraModel.get_property("floorId") is not None:
                    # camera nằm trong Floor rồi thì không add vào mapModel nữa
                    pass
                else:
                    self._cameraIds.appendCamera(cameraModel)

    @Property(str,notify=accessTokenChanged)
    def accessToken(self):
        if self.controller is not None:
            return self.controller.api_client.access_token
        return None

    @Property(str,notify=idChanged)
    def id(self):
        return self._id
    
    @id.setter
    def id(self, value: str):
        if self._id != value:
            self._id = value
            self.idChanged.emit() 

    def getCameraIds(self):
        return self._cameraIds

    def appendCameraIds(self, item):
        if isinstance(item, CameraModel):
            for camera in self._cameraIds.getItems():
                if item.id == camera.id:
                    camera.latitude = item.latitude
                    camera.longitude = item.longitude
                    return
            self._cameraIds.appendCamera(item)
            self.addCameraChanged.emit(item)

    def removeCamera(self, item):
        if isinstance(item, CameraModel):
            self._cameraIds.removeCamera(item)
            self.removeCameraChanged.emit(item)

    def clearCameraIds(self):
        self._cameraIds.clear()
        
    @Property(QObject, constant=True)
    def cameraIds(self):
        return self._cameraIds

    def getbuildingIds(self):
        return self._buildingIds

    def appendBuildingIds(self, item):
        if isinstance(item, BuildingModel):
            self._buildingIds.append(item)
            self.buildingIdsChanged.emit()  # Thông báo danh sách đã thay đổi
            
    
    def removeBuilding(self, item):
        if isinstance(item, BuildingModel):
            self._buildingIds.remove(item)
            self.removeBuildingChanged.emit(item)  # Thông báo danh sách đã thay đổi
    
    def clearbuildingIds(self):
        for item in self._buildingIds:
            item.latitude = None
            item.longitude = None

    buildingIds = Property(list, getbuildingIds, notify=buildingIdsChanged)

    def addBuilding(self,buildingModel:BuildingModel):
        self.appendBuildingIds(buildingModel)

    def camera_available(self, camera_id = None):
        for cameraModel in self._cameraIds.getItems():
            if camera_id == cameraModel.id:
                return True
        return False

    @Slot(QByteArray,float,float,result=QObject)
    def handleDrop(self, data, latitude, longitude):
        if(data.isEmpty()):
            return
        try:
            data = pickle.loads(data.data())
        except:
            data = bytes(data.data()).decode('utf-8')
            data = json.loads(data)
        id = data.get('id',None)
        tree_type = data.get('tree_type',None)
        if tree_type == TreeType.Camera:
            model:CameraModel = camera_model_manager.get_camera_model(id = id)
            # camModel = CameraModel(data=Camera(id=model.id,name=model.name, address=model.data.address,coordinateLat=model.latitude,coordinateLong=model.longitude,server_ip=model.data.server_ip,state=model.data.state))
            cameraData = {
                "id": model.id,
                "name": model.name,
                "address": model.get_property("address",None),
                "server_ip": model.get_property("server_ip",None),
                "coordinateLat": model.get_property("coordinateLat",None),
                "coordinateLong": model.get_property("coordinateLong",None),
                "state": model.get_property("state",None)
            }
            camModel = CameraModel(data=cameraData)
            if model.get_property('coordinateLat',None) is not None:
                self._previousCameraLocation[id] = (model.latitude, model.longitude)
                camModel.latitude = latitude
                camModel.longitude = longitude
                self.appendCameraIds(camModel)
                return
            else:
                camModel.latitude = latitude
                camModel.longitude = longitude
                self.appendCameraIds(camModel)
                return

        elif tree_type == TreeType.BuildingItem:
            model:BuildingModel = building_manager.getBuilding(id = id)
            buildingModel = BuildingModel(model.to_dict())
            buildingModel.resgiterOriginBuildingModel(model)
            for item in self._buildingIds:
                if item.id == buildingModel.id:
                    item.latitude = latitude
                    item.longitude = longitude
                    return
            buildingModel.latitude = latitude
            buildingModel.longitude = longitude
            self.appendBuildingIds(buildingModel)
            return model
        
    @Slot(str,bool)
    def handleConfirmLocation(self, id, isConfirm):
        if not isConfirm:
            latitude, longitude = self._previousCameraLocation[id]
            model:CameraModel = camera_model_manager.get_camera_model(id = id)
            # camModel = CameraModel(data=Camera(id=model.id,name=model.name, address=model.data.address,coordinateLat=model.latitude,coordinateLong=model.longitude,server_ip=model.data.server_ip))
            cameraData = {
                "id": model.id,
                "name": model.name,
                "address": model.get_property("address",None),
                "server_ip": model.get_property("server_ip",None),
                "coordinateLat": model.get_property("coordinateLat",None),
                "coordinateLong": model.get_property("coordinateLong",None),
                "state": model.get_property("state",None)
            }
            camModel = CameraModel(data=cameraData)
            camModel.latitude = latitude
            camModel.longitude = longitude
            self.appendCameraIds(camModel)
        
        self._previousCameraLocation.pop(id)

    @Slot(str, result=bool)
    def isPositionChanged(self, id):
        return id in self._previousCameraLocation

    @Slot(QObject)    
    def removeCameraFromMap(self,cameraModel:CameraModel) -> None:
        if isinstance(cameraModel, CameraModel):
            cameraModel.latitude = None
            cameraModel.longitude = None
            self.removeCamera(cameraModel)

    @Slot(QObject)    
    def removeBuildingFromMap(self,buildingModel:QObject) -> None:
        if isinstance(buildingModel, BuildingModel):
            buildingModel.latitude = None
            buildingModel.longitude = None

    def to_dict(self):
        list_Building_id = []
        list_cameras_id = []
        for item in self.buildingIds:
            list_Building_id.append(item.id)
        for item in self._cameraIds.getItems():
            list_cameras_id.append(item.id)
        return {
            "id": self._id,
            "serverIp": self.serverIp,
            "name": self._name,
            "buildingIds": list_Building_id,
            "cameraIds": list_cameras_id,
        }

    def resgiterOriginmapModel(self, mapModel):
        self.mapModel = mapModel
        self.mapModel.addCameraChanged.connect(self.syncAddCameraChanged)
        self.mapModel.removeBuildingChanged.connect(self.syncRemoveBuildingChanged)

    def syncAddCameraChanged(self):
        pass

    def syncRemoveBuildingChanged(self,item: BuildingModel):
        if self.mapModel is not None:
            for buildingModel in self._buildingIds:
                if buildingModel.id == item.id:
                    self._buildingIds.remove(buildingModel)
                    self.buildingIdsChanged.emit()

    def clone(self):
        from copy import deepcopy
        temp = self.to_dict()
        cloned_data = deepcopy(temp)
        cloned_model = MapModel(data = cloned_data,controller=self.controller)
        for idx,building in enumerate(self._buildingIds):
            copyBuildingModel = BuildingModel(data=building.to_dict())
            copyBuildingModel.resgiterOriginBuildingModel(building)
            cloned_model._buildingIds[idx] = copyBuildingModel
        for idx,camera in enumerate(self._cameraIds.getItems()):
            cameraData = {
                "id": camera.get_property("id",None),
                "name": camera.get_property("name",None),
                "address": camera.get_property("address",None),
                "server_ip": camera.get_property("server_ip",None),
                "state": camera.get_property("state",None)
            }
            cameraModel = CameraModel(data=cameraData)
            cameraModel.latitude = camera.latitude
            cameraModel.longitude = camera.longitude
            cloned_model._cameraIds.replaceCamera(idx,cameraModel)
        cloned_model.resgiterOriginmapModel(self)
        return cloned_model

    def processSaveDigitalMap(self,newMap, processType):
        logger.info(f'processSaveDigitalMap = {processType}')
        startCount = 0
        endCount = 0
        continuous = False 
        if processType == 0:
            # Cập nhật dữ liệu lat/log cho Building và camera trong Building
            for building in newMap.getbuildingIds():
                building_model:BuildingModel = building_manager.getBuilding(id = building.id)
                if building_model is None:
                    pass
                else:
                    if not building_model.isDataChanged(building.data):
                        continue
                    startCount += 1
                    def make_thread(building_model, building,endCount):
                        def callback(data):
                            if data is not None:
                                building_model.latitude = data[0]["latitude"]
                                building_model.longitude = data[0]["longitude"]
                                if (endCount == startCount) and continuous:
                                    self.processSaveDigitalMap(newMap,1)
                            else:
                                main_controller.show_message_dialog(None, "SAVE_MAP_ERROR")
                        def process():
                            listCameras = []
                            for floorModel in building_model._floorIds:
                                for camera in floorModel._cameras:
                                    temp = copy.deepcopy(camera)
                                    temp["coordinateLat"] = building.latitude
                                    temp["coordinateLong"] = building.longitude
                                    if not newMap.camera_available(camera_id = temp["id"]):
                                        listCameras.append(temp)
                            result = self.controller.update_list_cameras_by_put(data=listCameras) 
                            if result is not None:
                                for item in result:
                                    cameraModel:CameraModel = camera_model_manager.get_camera_model(id = item.get("id"))
                                    if cameraModel is not None:
                                        cameraModel.diff_camera_model(item)
                            else:
                                return None
                            data = {"id": building_model.id,"name": building.name, "latitude": building.latitude,"longitude": building.longitude,"location": building.location, "clientId": Utils.clientId}
                            result = self.controller.update_building_by_put([data])  
                            return result
                        subThread = SubThread(parent=self,target=process,callback=callback)
                        subThread.start() 
                    endCount += 1
                    make_thread(building_model,building,endCount)
            if startCount == 0:
                self.processSaveDigitalMap(newMap,1)
            continuous = True
        elif processType == 1:
            # Xử lý lat/long cho camera bị xóa khỏi bản đồ số
            originMap = self.to_dict()
            newMapDict = newMap.to_dict()
            list_id = []
            for id in originMap["cameraIds"]:
                if id not in newMapDict["cameraIds"]:
                    list_id.append(id)
            list_cameras = []
            for cameraModel in self._cameraIds.getItems():
                if cameraModel.id in list_id:
                    data = copy.deepcopy(cameraModel.data)
                    data['coordinateLat'] = None
                    data['coordinateLong'] = None
                    list_cameras.append(data)

            def callback(data):
                if data is not None:
                    # đã xử lý việc cập nhật camera qua websocket rồi nên comment lại
                    # dist = []
                    for item in data:
                        cameraModel:CameraModel = camera_model_manager.get_camera_model(id = item.get("id"))
                        if cameraModel is not None:
                            cameraModel.diff_camera_model(item)
                            self.removeCamera(cameraModel)
                    self.processSaveDigitalMap(newMap,2)
                else:
                    main_controller.show_message_dialog(None, "SAVE_MAP_ERROR")
            def process():
                if len(list_cameras)>0:
                    result = self.controller.update_list_cameras_by_put(list_cameras)   
                    return result 
                    
            if len(list_cameras) == 0:
                self.processSaveDigitalMap(newMap,2)
            else:
                subThread = SubThread(parent=self,target=process,callback = callback)
                subThread.start() 
            continuous = True
        elif processType == 2:
            # Đồng bộ xóa camera khỏi floor
            for camera in newMap._cameraIds.getItems():
                cameraModel:CameraModel = camera_model_manager.get_camera_model(id=camera.id)
                if not cameraModel.isLatLogChanged(camera.data):
                    continue
                listFloors = floor_manager.getFloor(camera_id=cameraModel.id)
                for floorModel in listFloors:
                    startCount += 1
                    def make_thread(floorModel, cameraModel,endCount):
                        def callback(data):
                            if data is not None:
                                floorModel = floor_manager.getFloor(id=data.get("id",None))
                                if floorModel is not None:
                                    floorModel.set_property("cameraIds",data.get("cameraIds",[]))
                                    floorModel.removeCamera(camera_id=cameraModel.id)
                                    floorModel.listCamerasChanged.emit()
                                    if (endCount == startCount) and continuous:
                                        self.processSaveDigitalMap(newMap,3)
                            else:
                                main_controller.show_message_dialog(None, "SAVE_MAP_ERROR")
                        def process():
                            floor = floorModel.controller.remove_camera_on_floor(data = {"id": floorModel.id, "ids": [cameraModel.id], })
                            return floor
                        subThread = SubThread(parent=self,target=process,callback=callback)
                        subThread.start() 
                    endCount += 1
                    make_thread(floorModel,cameraModel,endCount)
            if startCount == 0:
                self.processSaveDigitalMap(newMap,3)
            continuous = True
        elif processType == 3:
            # Đồng bộ dữ liệu lat/long của tất cả camera có trên bản đồ số
            for camera in newMap._cameraIds.getItems():
                cameraModel:CameraModel = camera_model_manager.get_camera_model(id=camera.id)
                if not cameraModel.isLatLogChanged(camera.data):
                    continue
                startCount += 1
                def make_thread(cameraModel, camera,endCount):
                    def callback(data):
                        if data is not None:
                            for item in data:
                                cameraModel:CameraModel = camera_model_manager.get_camera_model(id = item.get("id"))
                                if cameraModel is not None:
                                    cameraModel.diff_camera_model(item)
                                    self.appendCameraIds(cameraModel)
                            if (endCount == startCount) and continuous:
                                self.processSaveDigitalMap(newMap,4)
                    def process():
                        temp = CameraModel(data=copy.deepcopy(cameraModel.data))
                        temp.set_property("coordinateLat",camera.latitude)
                        temp.set_property("coordinateLong",camera.longitude)
                        result = self.controller.update_list_cameras_by_put([temp.data])
                        if result is not None:
                            return result
                    subThread = SubThread(parent=self,target=process,callback=callback)
                    subThread.start() 
                endCount += 1    
                make_thread(cameraModel,camera,endCount)  
            if startCount == 0:
                self.processSaveDigitalMap(newMap,4)
            continuous = True
        elif processType == 4:
            main_controller.show_message_dialog(None, "SAVE_MAP")
            logger.info(f'processSaveDigitalMap = {time.time() -main_controller.startTime}')

    def merge(self,newMap):
        main_controller.startTime = time.time()
        self.processSaveDigitalMap(newMap,0)

    def process_remove_camera_on_map(self, cameraId: str):
        for cameraModel in self._cameraIds.getItems():
            if cameraId == cameraModel.id:
                self.removeCamera(cameraModel)

class MapManager(QObject):
    addMapList = Signal(tuple)
    __instance = None
    def __init__(self):
        super().__init__()
        self.data = {}
        self.addMapList.connect(self.addMapListSignal)

    @staticmethod
    def get_instance():
        if MapManager.__instance is None:
            MapManager.__instance = MapManager()
        return MapManager.__instance
    
    def addMapListSignal(self,data):
        data,controller = data
        for item in data:
            map_model = MapModel(data = item, controller = controller)
            map_manager.addMap(map_model)

    def addMap(self, map_model: MapModel):
        self.data[map_model.serverIp] = map_model

    def addBuilding(self, building_model: BuildingModel):
        if building_model.serverIp in self.data:
            map_model:MapModel = self.data[building_model.serverIp]
            map_model.appendBuildingIds(building_model)
            building_manager.addBuilding(building_model)

    def removeBuilding(self, building_model: BuildingModel):
        if building_model.serverIp in self.data:
            map_model:MapModel = self.data[building_model.serverIp]
            if building_model in map_model._buildingIds:
                map_model.removeBuilding(building_model)
                building_manager.removeBuilding(building_model)

    def updateCamera(self,originCamera: CameraModel, newCamera:dict):
        if originCamera.get_property("floorId") != newCamera.get("floorId"):
            if originCamera.get_property("coordinateLat") != newCamera.get("coordinateLat") or originCamera.get_property("coordinateLong") != newCamera.get("coordinateLong"):
                if newCamera.get("floorId") is None:
                    # case xóa camera khỏi Floor
                    floorModel:FloorModel = floor_manager.getFloor(id = originCamera.get_property("floorId"))
                    if floorModel is not None:
                        floorModel.removeCamera(newCamera.get("id",None))
                        floorModel.listCamerasChanged.emit()
                    mapModel = map_manager.get_map_model(serverIp=originCamera.get_property("server_ip"))
                    if mapModel is not None:
                        if newCamera.get("coordinateLat") is None:
                            # case xóa camera khỏi MapModel
                            mapModel.removeCamera(originCamera)
                        else:
                            # case thêm camera vào MapModel
                            mapModel.appendCameraIds(originCamera)
                else:
                    if originCamera.get_property("floorId") is None:
                        # case thêm camera vào floor
                        floorModel:FloorModel = floor_manager.getFloor(id = newCamera.get("floorId",None)) 
                        if floorModel is not None:
                            originCamera.syncFloorData(newCamera)
                            floorModel.addCamera(originCamera)
                        mapModel = map_manager.get_map_model(serverIp=originCamera.get_property("server_ip"))
                        if mapModel is not None:
                            mapModel.removeCamera(originCamera)
                    else:
                        # case xóa camera từ floor cũ
                        floorModel:FloorModel = floor_manager.getFloor(id = originCamera.get_property("floorId")) 
                        if floorModel is not None:
                            floorModel.removeCamera(newCamera.get("id",None))
                            floorModel.listCamerasChanged.emit()
                        # case thêm camera vào floor mới
                        floorModel:FloorModel = floor_manager.getFloor(id = newCamera.get("floorId",None)) 
                        if floorModel is not None:
                            originCamera.syncFloorData(newCamera)
                            floorModel.addCamera(originCamera)
            else:
                if newCamera.get("floorId") is None:
                    # case xóa camera khỏi Floor
                    floorModel:FloorModel = floor_manager.getFloor(id = originCamera.get_property("floorId"))
                    if floorModel is not None:
                        floorModel.removeCamera(newCamera.get("id",None))
                        floorModel.listCamerasChanged.emit()
                else:
                    if originCamera.get_property("floorId") is None:
                        # case thêm camera vào floor
                        floorModel:FloorModel = floor_manager.getFloor(id = newCamera.get("floorId",None)) 
                        if floorModel is not None:
                            originCamera.syncFloorData(newCamera)
                            floorModel.addCamera(originCamera)
                    else:
                        # case xóa camera khỏi Floor cux
                        floorModel:FloorModel = floor_manager.getFloor(id = originCamera.get_property("floorId"))
                        if floorModel is not None:
                            floorModel.removeCamera(newCamera.get("id",None))
                            floorModel.listCamerasChanged.emit()
                        floorModel:FloorModel = floor_manager.getFloor(id = newCamera.get("floorId",None)) 
                        if floorModel is not None:
                            originCamera.syncFloorData(newCamera)
                            floorModel.addCamera(originCamera)
            main_controller.show_message_dialog(None, "MAP_DATA_CHANGED")
        else:
            if originCamera.get_property("coordinateLat") != newCamera.get("coordinateLat") or originCamera.get_property("coordinateLong") != newCamera.get("coordinateLong"):
                if originCamera.get_property("floorId") is None:
                    mapModel = map_manager.get_map_model(serverIp=originCamera.get_property("server_ip"))
                    if mapModel is not None:
                        if newCamera.get("coordinateLat") is None:
                            # case xóa camera khỏi MapModel
                            mapModel.removeCamera(originCamera)
                        else:
                            # case thêm camera vào MapModel
                            mapModel.appendCameraIds(originCamera)
                else:
                    logger.info(f'updateCamera99')
            else:
                if originCamera.get_property("floorId") is None:
                    logger.info(f'updateCamera12')
                else:
                    floorModel:FloorModel = floor_manager.getFloor(id = newCamera.get("floorId",None)) 
                    if floorModel is not None:
                        originCamera.syncFloorData(newCamera)
                        floorModel.listCamerasChanged.emit()
            main_controller.show_message_dialog(None, "MAP_DATA_CHANGED")        
               
    def get_map_model(self, serverIp = None, id = None,camera_id = None):
        if serverIp in self.data:
            return self.data[serverIp]
        for i, map_model in self.data.items():
            if id == map_model.id:
                return map_model
        if camera_id is not None:
            for serverIp,mapModel in self.data.items():
                for cameraModel in mapModel.cameraIds.getItems():
                    if cameraModel.id == camera_id:
                        return mapModel
        return None
    
    def to_dict(self):
        dist = {}
        for k, v in self.data.items():
            dist[k] = v.to_dict()
        return dist 
    
    def delete_server(self,serverIp = None):
        if serverIp in self.data:
            del self.data[serverIp]

map_manager = MapManager.get_instance()



class Map2DController(QObject):
    floorModelChanged = Signal()
    listCameraChanged = Signal()
    mapStateChanged = Signal()
    saveCameraListSignal = Signal()
    saveCameraStatusSignal = Signal(bool)
    themeChangeSignal = Signal()

    def __init__(self, floor_model: FloorModel, map_state: MapState = None):
        super().__init__()
        self._floorModel = floor_model
        logger.info(f'Map2DController = {self._floorModel.buildingModel}')
        self._tempListCameras = self.cloneCameraList(self._floorModel._cameras)
        self._mapState = map_state
        self.saveCameraListSignal.connect(self.saveCameraData)
        main_controller.theme_change_signal.connect(self.themeChangeSignal)

    @Property(QObject,notify=floorModelChanged)
    def floorModel(self):
        return self._floorModel
    
    @floorModel.setter
    def floorModel(self, value: QObject):
        if self._floorModel != value:
            self._floorModel = value
            self.floorModelChanged.emit() 

    @Property(QObject,notify=mapStateChanged)
    def mapState(self):
        return self._mapState
    
    @mapState.setter
    def mapState(self, value: QObject):
        if self._mapState != value:
            self._mapState = value
            self.mapStateChanged.emit() 

    @Property(list,notify=listCameraChanged)
    def listCameras(self):
        return self._tempListCameras
    
    @listCameras.setter
    def listCameras(self, value: list):
        if self._tempListCameras != value:
            self._tempListCameras = value
            self.listCameraChanged.emit()

    @Slot(int)
    def temporarilyDeleteCamera(self, index):
        self._tempListCameras.pop(index)
        self.listCameraChanged.emit()

    @Slot(QByteArray, float, float, float)
    def temporarilyAddCamera(self, data: QByteArray, relative_x, relative_y, ratio):
        if(data.isEmpty()):
            return
        try:
            data = pickle.loads(data.data())
        except:
            data = bytes(data.data()).decode('utf-8')
            data = json.loads(data)

        id = data.get('id',None)
        camera_model:CameraModel = camera_model_manager.get_camera_model(id = id)
        if len(floor_manager.getFloor(camera_id = id)) > 0:
            main_controller.show_message_dialog(None, "CAMERA_ALREADY_HAVE_LOCATION")
        if camera_model is not None:
            for camera in self._tempListCameras:
                if camera['id'] == camera_model.id:
                    main_controller.show_message_dialog(None, "CAMERA_EXIST_IN_MAP")
                    return
            fov_data = {
                'position': f"{relative_x},{relative_y}",
                'arc_start_angle': -45,
                'arc_range_angle': 90,
                'radius': 80 / ratio,
                'icon_type': 0
            }
            temp = CameraModel(data=copy.deepcopy(camera_model.data))
            temp.set_property("coordinateLat",self._floorModel.buildingModel.latitude)
            temp.set_property("coordinateLong",self._floorModel.buildingModel.longitude)
            temp.set_property("fovData",json.dumps(fov_data))
            temp.set_property("color",CameraColor.BLUE.value)
            temp.set_property("fovEnable",True)
            temp.set_property("fovMode",FovModeItem.ICON.value)
            temp.set_property("nameEnable",True)
            temp.set_property("size",SizeCameraItem.MEDIUM.value)
            self._tempListCameras.append(temp.data)

            self.listCameraChanged.emit()

    @Slot(int, dict)
    def modifyTempCameraList(self, index, data):
        self._tempListCameras[index] = data

    @Slot(str, result=str)
    def get_color_theme_by_key(self, key = None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Color", key)
    
    def processSaveFloorMap(self,new_list_cameras = [], processType = 0):
        # Code theo cơ chế này để đảm bảo các data được xử lý tuần tự trong callback và vẫn xử lý giao tiếp API trong luồng phụ
        logger.info(f'processSaveFloorMap = {processType}')
        continuous = False 
        if processType == 0:
            # xử lý xóa camera từ FloorModel này
            listCameras = []
            if self._floorModel.get_property("cameraIds") is not None:
                for id in self._floorModel.get_property("cameraIds"):
                    check = False
                    for camera in new_list_cameras:
                        if id == camera.get("id"):
                            check = True
                    if not check:
                        listCameras.append(id)
            if len(listCameras) > 0:
                def callback(data):
                    if data is not None:
                        pass
                        self.processSaveFloorMap(new_list_cameras, 1)
                def process():
                    floor = self._floorModel.controller.remove_camera_on_floor(data = {"id": self._floorModel.id, "ids": listCameras, })
                    if floor is not None:
                        self._floorModel.set_property("cameraIds",floor.get("cameraIds",[]))
                        listCameraModels = []
                        for id in listCameras:
                            cameraModel = camera_model_manager.get_camera_model(id = id)
                            if cameraModel is not None:
                                temp = copy.deepcopy(cameraModel.data)
                                temp["coordinateLat"] = None
                                temp["coordinateLong"] = None
                                temp["floorId"] = None
                                listCameraModels.append(temp)

                        if len(listCameraModels) > 0:
                            data = self._floorModel.controller.update_list_cameras_by_put(listCameraModels)
                            if data is not None:
                                for item in data:
                                    cameraModel:CameraModel = camera_model_manager.get_camera_model(id = item.get("id"))
                                    if cameraModel is not None:
                                        cameraModel.diff_camera_model(item)
                                        self._floorModel.removeCamera(camera_id=cameraModel.id) 
                                self._floorModel.listCamerasChanged.emit()
                        return floor
                subThread = SubThread(parent=self,target=process,callback=callback)
                subThread.start()
            else:
                self.processSaveFloorMap(new_list_cameras, 1)

        elif processType == 1:
            startCount = 0
            endCount = 0
            # xóa camera từ floorModel cũ chứa camera này
            for camera in new_list_cameras:
                cameraModel:CameraModel = camera_model_manager.get_camera_model(id = camera['id'])
                
                if cameraModel is not None:
                    floorId = cameraModel.get_property("floorId",None)
                    if floorId != self._floorModel.id and floorId is not None:
                        startCount += 1
                        def make_thread(floorId, camera,endCount):
                            def callback(data):
                                if data is not None:
                                    floorModel = floor_manager.getFloor(id=data.get("id",None))
                                    if floorModel is not None:
                                        floorModel.set_property("cameraIds",data.get("cameraIds",[]))
                                        floorModel.removeCamera(camera_id=camera['id'])
                                        floorModel.listCamerasChanged.emit()
                                        if (endCount == startCount) and continuous:
                                            self.processSaveFloorMap(new_list_cameras,2)
                                else:
                                    return False
                            def process():
                                floor = self._floorModel.controller.remove_camera_on_floor(data = {"id": floorId, "ids": [camera['id']], })
                                if floor is not None:
                                    return floor
                                else:
                                    return None
                            subThread = SubThread(parent=self,target=process,callback=callback)
                            subThread.start() 
                        endCount += 1
                        make_thread(floorId,camera,endCount)
            if startCount == 0:
                self.processSaveFloorMap(new_list_cameras,2)
            continuous = True
        elif processType == 2:
            # xóa camera khỏi MapModel
            mapModel:MapModel = map_manager.get_map_model(serverIp = self._floorModel.serverIp)
            if mapModel is not None:
                for camera in new_list_cameras:
                    mapModel.process_remove_camera_on_map(camera["id"])
            self.processSaveFloorMap(new_list_cameras,3)
        elif processType == 3:
            listCameraIds = []
            latitude = None
            longitude = None
            buildingModel = building_manager.getBuilding(floorModel=self._floorModel)
            if buildingModel is not None:
                latitude = buildingModel.latitude
                longitude = buildingModel.longitude
            for camera in new_list_cameras:
                camera["floorId"] = self._floorModel.id
                camera["coordinateLat"] = latitude
                camera["coordinateLong"] = longitude
                listCameraIds.append(camera.get("id",None))
            def callback(data):
                if data is not None:
                    self.processSaveFloorMap(new_list_cameras,4)
                else:
                    main_controller.show_message_dialog(None, "SAVE_MAP_ERROR")
            def process():
                floor = self._floorModel.controller.add_camera_on_floor(data = {"id": self._floorModel.id, "ids": listCameraIds, })
                if floor is not None:
                    self._floorModel.set_property("cameraIds",floor.get("cameraIds",[]))
                    data = self._floorModel.controller.update_list_cameras_by_put(new_list_cameras)
                    if data is not None:
                        for item in data:
                            cameraModel:CameraModel = camera_model_manager.get_camera_model(id = item.get("id"))
                            if cameraModel is not None:
                                diff = cameraModel.diff_camera_model(item)
                                check = False
                                for camera in self._floorModel._cameras:
                                    if camera.get("id", None) == cameraModel.id:
                                        check = True
                                if not check:
                                    self._floorModel._cameras.append(cameraModel.data)
                        self._floorModel.listCamerasChanged.emit()
                        self.floorModelChanged.emit()
                    return floor
            if len(new_list_cameras) == 0:
                self.processSaveFloorMap(new_list_cameras,4)
            else:
                subThread = SubThread(parent=self,target=process,callback=callback)
                subThread.start() 
        elif processType == 4:
            main_controller.show_message_dialog(None, "SAVE_MAP")
            logger.info(f'processSaveFloorMap = {time.time() -main_controller.startTime}')

    @Slot()
    def saveCameraData(self):
        new_list_cameras = [camera for camera in self._tempListCameras]
        main_controller.startTime = time.time()
        self.processSaveFloorMap(new_list_cameras,0)

    def cloneCameraList(self, listCameras):
        new_list_cam = []
        for idx,camera in enumerate(listCameras):
            dict_camera = copy.deepcopy(camera)
            new_list_cam.append(dict_camera)

        return new_list_cam
    
    def clearlistCameras(self):
        self._floorModel._cameras.clear()
