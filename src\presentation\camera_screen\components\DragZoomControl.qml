import QtQuick
import QtQuick.Controls
import "../constants/ZIndexConstants.js" as ZIndex

Item {
    id: dragZoomControl
    anchors.fill: parent
    visible: false
    z: ZIndex.gridItemPopup

    property bool isDarkTheme: true
    property point startPoint: Qt.point(0, 0)
    property point endPoint: Qt.point(0, 0)
    property bool isDrawing: false
    property bool isActive: false
    property bool isOverButtonArea: false

    // Tín hiệu khi người dùng vẽ hình chữ nhật để zoom
    signal dragZoom(point startPoint, point endPoint)

    // Tín hiệu khi người dùng click để tắt
    signal dragZoomClosed()

    // Canvas để vẽ hình chữ nhật khi kéo thả
    <PERSON>vas {
        id: dragCanvas
        anchors.fill: parent

        onPaint: {
            var ctx = getContext("2d");
            ctx.clearRect(0, 0, width, height);

            // Chỉ vẽ khi đang trong trạng thái vẽ
            if (isDrawing) {
                // L<PERSON><PERSON> trạng thái
                ctx.save();

                // Vẽ hình chữ nhật
                ctx.beginPath();

                // Tính toán tọa độ và kích thước hình chữ nhật
                var rectX = Math.min(startPoint.x, endPoint.x);
                var rectY = Math.min(startPoint.y, endPoint.y);
                var rectWidth = Math.abs(endPoint.x - startPoint.x);
                var rectHeight = Math.abs(endPoint.y - startPoint.y);

                // Vẽ hình chữ nhật với viền đứt
                ctx.setLineDash([5, 3]);
                ctx.strokeStyle = isDarkTheme ? "white" : "black";
                ctx.lineWidth = 2;
                ctx.strokeRect(rectX, rectY, rectWidth, rectHeight);

                // Vẽ hình chữ nhật bán trong suốt
                ctx.fillStyle = "rgba(255, 255, 255, 0.2)";
                ctx.fillRect(rectX, rectY, rectWidth, rectHeight);

                // Khôi phục trạng thái
                ctx.restore();
            }
        }
    }

    // Vùng nhận sự kiện chuột
    MouseArea {
        id: dragZoomMouseArea
        anchors.fill: parent
        hoverEnabled: true
        propagateComposedEvents: true

        function isInFlowButtonArea(mouseX, mouseY) {
            var parentItem = parent;
            var controlButtonsRow = null;
            for (var i = 0; i < parentItem.children.length; i++) {
                if (parentItem.children[i].objectName === "controlButtonsRow") {
                    controlButtonsRow = parentItem.children[i];
                    break;
                }
            }
            if (controlButtonsRow && controlButtonsRow.visible) {
                var mousePos = mapToItem(parentItem, mouseX, mouseY);
                var buttonRect = Qt.rect(
                    controlButtonsRow.x,
                    controlButtonsRow.y,
                    controlButtonsRow.width,
                    controlButtonsRow.height
                );
                return (
                    mousePos.x >= buttonRect.x &&
                    mousePos.x <= buttonRect.x + buttonRect.width &&
                    mousePos.y >= buttonRect.y &&
                    mousePos.y <= buttonRect.y + buttonRect.height
                );
            }
            return false;
        }

        onPressed: function(mouse) {
            if (isInFlowButtonArea(mouse.x, mouse.y)) {
                mouse.accepted = false;
                return;
            }
            if (mouse.button === Qt.LeftButton) {
                isDrawing = true;
                startPoint = Qt.point(mouse.x, mouse.y);
                endPoint = startPoint;
                dragCanvas.requestPaint();
                mouse.accepted = true;
            } else {
                mouse.accepted = false;
            }
        }

        onPositionChanged: function(mouse) {
            if (isInFlowButtonArea(mouse.x, mouse.y)) {
                mouse.accepted = false;
                return;
            }
            dragZoomControl.isOverButtonArea = false;
            dragZoomMouseArea.cursorShape = Qt.CrossCursor;
            if (isDrawing) {
                endPoint = Qt.point(mouse.x, mouse.y);
                dragCanvas.requestPaint();
                mouse.accepted = true;
            } else {
                mouse.accepted = false;
            }
        }

        onReleased: function(mouse) {
            if (isInFlowButtonArea(mouse.x, mouse.y)) {
                mouse.accepted = false;
                return;
            }
            if (isDrawing && mouse.button === Qt.LeftButton) {
                isDrawing = false;
                var rectWidth = Math.abs(endPoint.x - startPoint.x);
                var rectHeight = Math.abs(endPoint.y - startPoint.y);
                if (rectWidth > 10 && rectHeight > 10) {
                    var savedStartPoint = Qt.point(startPoint.x, startPoint.y);
                    var savedEndPoint = Qt.point(endPoint.x, endPoint.y);
                    startPoint = Qt.point(0, 0);
                    endPoint = Qt.point(0, 0);
                    dragCanvas.requestPaint();
                    dragZoom(savedStartPoint, savedEndPoint);
                } else {
                    startPoint = Qt.point(0, 0);
                    endPoint = Qt.point(0, 0);
                    dragCanvas.requestPaint();
                }
                mouse.accepted = true;
            } else {
                mouse.accepted = false;
            }
        }

        onClicked: function(mouse) {
            if (isInFlowButtonArea(mouse.x, mouse.y)) {
                mouse.accepted = false;
                return;
            }
            startPoint = Qt.point(0, 0);
            endPoint = Qt.point(0, 0);
            dragCanvas.requestPaint();
            mouse.accepted = true;
        }
    }

    function show() {
        isDrawing = false;
        startPoint = Qt.point(0, 0);
        endPoint = Qt.point(0, 0);
        dragCanvas.requestPaint();
        dragZoomControl.visible = true;
    }

    function hide() {
        isDrawing = false;
        startPoint = Qt.point(0, 0);
        endPoint = Qt.point(0, 0);
        dragCanvas.requestPaint();
        dragZoomControl.visible = false;
        dragZoomClosed();
    }
}
