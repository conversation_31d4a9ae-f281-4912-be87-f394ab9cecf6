import logging
from functools import partial
from queue import Queue

from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QStandardItemModel, QStandardItem, QColor, QBrush, QIcon, QPixmap, QGuiApplication
from PySide6.QtWidgets import QWidget, QVBoxLayout, QCheckBox, QAbstractItemView, QSizePolicy, \
    QPushButton, QHBoxLayout, QLabel, QDialog

from src.common.controller.controller_manager import Controller
from src.common.controller.main_controller import main_controller
from src.common.model.user_model import user_model_manager, UserModel
from src.common.onvif_api.worker_thread import WorkerThread
from src.common.widget.custom_checkbox_dialog import CustomCheckBoxDialogs
from src.common.widget.custom_qtable_view import TableWithCustomHeader
from src.common.widget.dialogs.warning_dialog import WarningDialog
from src.common.widget.dialogs.dialogs_permission_screen import UserInformationDialog
from src.common.widget.pagination.page_indicator.page_indicator import Pagination
from src.styles.style import Style, Theme
from src.utils.theme_setting import theme_setting

logger = logging.getLogger(__name__)

class UsersTableView(QWidget):
    def __init__(self, parent=None, widget_width=None, widget_height=None, controller: Controller = None):
        super().__init__(parent)
        self.is_programmatically_changing_combobox = True
        self.controller = controller
        main_controller.list_parent['UsersTableView'] = self
        self.widget_width = widget_width
        self.widget_height = widget_height
        self.table_result_users = None
        self.page_indicator = None
        self.model_table_users = None
        self.column_init_table = 9
        self.is_loading = False
        self.filter_user_name = False
        self.filter_queue = Queue()
        self.filter_user_thread = WorkerThread(parent=self, target=self.create_user_data, args=(True,),
                                               callback=self.callback_filter_user)

        self.create_model()
        self.load_ui()
        self.set_dynamic_stylesheet()

    def load_ui(self):
        # create layout
        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        list_horizontal_header = ["", self.tr("NO"), self.tr("USERNAME"), self.tr("FULLNAME"), self.tr("EMAIL"), self.tr("PHONE NUMBER"), self.tr("GROUP"), self.tr("STATUS"), self.tr("ACTIONS")]
        widget_checkbox = QWidget()
        layout_checkbox = QVBoxLayout()
        layout_checkbox.setContentsMargins(0, 0, 0, 0)
        layout_checkbox.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.checkbox_header = QCheckBox()
        # self.checkbox_header.setStyleSheet(f"""
        #             QCheckBox::indicator:checked {{
        #                 border: none;
        #                 image: url({Style.PrimaryImage.checkbox_checked});
        #                 background-color: transparent;
        #                 width: 16px;
        #                 height: 16px;
        #             }}
        #             QCheckBox::indicator:unchecked {{
        #                 border: none;
        #                 image: url({Style.PrimaryImage.checkbox_unchecked});
        #                 background-color: {Style.PrimaryColor.on_background};
        #                 width: 16px;
        #                 height: 16px;
        #             }}
        #             """)
        self.checkbox_header.stateChanged.connect(self.check_all)
        self.checkbox_header.setTristate(False)
        layout_checkbox.addWidget(self.checkbox_header)
        widget_checkbox.setLayout(layout_checkbox)
        list_widget_for_header = {0: widget_checkbox}

        # theme changed
        self.table_result_users = TableWithCustomHeader(horizontal_label_list=list_horizontal_header,
                                                        list_widget_to_header=list_widget_for_header,
                                                        model_for_table=self.model_table_users,
                                                        use_stylesheet_header=True)

        self.table_result_users.table.clicked.connect(
            partial(self.on_clicked_table, model_table=self.model_table_users,
                    table=self.table_result_users.table, checkbox_all=self.checkbox_header))
        self.set_up_dimension_row()
        self.table_result_users.table.verticalHeader().setVisible(False)
        self.table_result_users.table.verticalHeader().setDefaultSectionSize(0.08 * self.widget_height)
        self.table_result_users.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table_result_users.table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.table_result_users.table.setSelectionMode(QAbstractItemView.SelectionMode.NoSelection)
        self.table_result_users.table.setFocusPolicy(Qt.NoFocus)
        self.table_result_users.table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.table_result_users.table.setShowGrid(False)
        # horizontal_scroll_bar = self.table_result_users.table.horizontalScrollBar()
        # horizontal_scroll_bar.setStyleSheet(f'''
        #                         QScrollBar::horizontal {{
        #                             background-color: transparent;
        #                             height: 8px;
        #                             width: 48px;
        #                             margin: 0px 0px 0px 0px;
        #                         }}
        #                         QScrollBar::handle:horizontal {{
        #                             background-color: #656475;
        #                             border-radius: 4px;
        #                             min-width: 8px;
        #                             width: 48px;
        #                         }}
        #                         QScrollBar::add-line:horizontal {{
        #                             background: none;
        #                         }}
        #                         QScrollBar::sub-line:horizontal {{
        #                             background: none;
        #                         }}
        #                         QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
        #                             background: none;
        #                         }}
        #                         QScrollBar::left-arrow:horizontal, QScrollBar::right-arrow:horizontal {{
        #                             width: 0px;
        #                             height: 0px;
        #                             background: none;
        #                         }}
        #                 ''')

        # Page Indicator:
        self.widget_indicator = QWidget()
        self.widget_indicator.setObjectName('widget_indicator')
        # self.widget_indicator.setStyleSheet(
        #     f"""
        #         QWidget {{
        #         background-color: {Style.PrimaryColor.background};
        #         color: {Style.PrimaryColor.text_unselected};
        #     }}
        # """)
        layout_indicator = QHBoxLayout()
        layout_indicator.setContentsMargins(0, 0, 0, 0)
        layout_indicator.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.page_indicator = Pagination(rows_per_page=10)
        self.page_indicator.signal_update_table.connect(self.temp_update_table)
        self.label_total_pages = QLabel(self.tr('Total: 50'))
        self.label_total_pages.setStyleSheet('padding: 16px 0px')
        self.label_item_per_page = QLabel(self.tr('Show records/page: ')+f'10')
        self.label_item_per_page.setStyleSheet('padding: 16px 0px')
        layout_indicator.addWidget(self.label_total_pages)
        layout_indicator.addWidget(self.page_indicator)
        layout_indicator.addWidget(self.label_item_per_page)
        self.widget_indicator.setLayout(layout_indicator)

        widget_table = QWidget()
        layout_table = QVBoxLayout(widget_table)
        layout_table.setContentsMargins(8, 8, 8, 0)
        layout_table.addWidget(self.table_result_users)

        self.main_layout.addWidget(widget_table)
        self.main_layout.addWidget(self.widget_indicator)
        self.setLayout(self.main_layout)

    def set_up_dimension_row(self):
        self.table_result_users.table.setColumnWidth(0, 0.025 * self.widget_width)  # checkbox
        self.table_result_users.table.setColumnWidth(1, 0.05 * self.widget_width)  # STT
        self.table_result_users.table.setColumnWidth(2, 0.1 * self.widget_width)  # username
        self.table_result_users.table.setColumnWidth(3, 0.16 * self.widget_width)  # name
        self.table_result_users.table.setColumnWidth(4, 0.16 * self.widget_width)  # email

        self.table_result_users.table.setColumnWidth(5, 0.08 * self.widget_width)  # phone number
        self.table_result_users.table.setColumnWidth(6, 0.2 * self.widget_width)  # group
        self.table_result_users.table.setColumnWidth(7, 0.075 * self.widget_width)  # status
        self.table_result_users.table.setColumnWidth(8, 0.15 * self.widget_width)  # action

    def put_filter_queue(self, msg):
        self.filter_queue.put(msg)
        # Tạm thời comment vì chưa rõ kịch bản đang tìm kiếm
        # self.create_camera_data()
        self.create_user_data()

    def create_user_data(self, is_thread=True):
        data = user_model_manager.get_user_list(server_ip=self.controller.server.data.server_ip)
        user_list = []
        for user_model in data.values():
            user_list.append(user_model)
        if is_thread:
            # Search by username, fullName, email, phone number, group name
            while not self.filter_queue.empty():
                msg = self.filter_queue.get()
                if not self.is_loading:
                    self.is_loading = True
                    self.controller.device_data_filtered = []
                    user_dist = []
                    if self.filter_user_name:
                        for user in user_list:
                            if msg in user.data.username or msg in user.data.fullName or msg in user.data.email or msg in user.data.phone or (msg in user.data.rolesName if user.data.rolesName is not None else False):
                                user_dist.append(user)
                    else:
                        user_dist = user_list
                    # print(f"HanhLT: user_dist = {user_dist}")
                    self.page_indicator.set_total_rows_and_total_pages(len(user_dist))
                    self.controller.user_data_filter = user_dist
                self.is_loading = False
                self.filter_queue.task_done()
        else:
            self.page_indicator.set_total_rows_and_total_pages(len(self.controller.user_data_filter))
            self.controller.user_data_filter = user_list
        self.callback_filter_user()

    def temp_update_table(self, data):
        current_page, item_per_page = data
        self.controller.current_user_table_page = current_page
        if self.checkbox_header.checkState() == Qt.CheckState.Checked:
            self.checkbox_header.setCheckState(Qt.CheckState.Unchecked)
            self.checkbox_header.clearFocus()
        self.update_model()

    def callback_filter_user(self):
        self.create_model()

    def create_model(self):
        if self.page_indicator is not None:
            self.page_indicator.set_total_rows_and_total_pages(len(self.controller.user_data_filter))
        self.update_model()

    def update_model(self):
        if self.model_table_users is not None:
            self.model_table_users.clear()
        else:
            self.model_table_users = QStandardItemModel()
        if self.table_result_users is not None:
            self.model_table_users.setHorizontalHeaderLabels(self.table_result_users.horizontal_label_list)
            self.table_result_users.table.setModel(self.model_table_users)
        else:
            return
        self.set_up_dimension_row()
        self.model_table_users.setRowCount(self.controller.total_user_items)
        self.label_total_pages.setText(self.tr(f'Total: ')+f'{len(self.controller.user_data_filter)}')
        # Calculate the start and end rows based on pagination
        start_row = (self.page_indicator.current_page - 1) * self.page_indicator.rows_per_page
        end_row = min(start_row + self.page_indicator.rows_per_page, len(self.controller.user_data_filter))

        for index_in_page, row in enumerate(range(start_row, end_row)):
            user_data_row: UserModel = self.controller.user_data_filter[row]

            # Now 'index_in_page' starts from 0 and increments for each item in the current page
            for column in range(self.column_init_table):
                if column == 0:
                    model_index = self.model_table_users.index(index_in_page, column)
                    widget = CustomCheckBoxDialogs(parent=self.table_result_users, index=model_index)
                    self.table_result_users.table.setIndexWidget(model_index, widget)
                if column == 1:
                    item_stt = CustomLabelItemUserTable(f"{row + 1}")  # Keep original row number for display (row+1)
                    # item_stt.setForeground(QBrush(QColor(Style.PrimaryColor.white_2)))
                    # item_stt.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.model_table_users.setItem(index_in_page, 1, item_stt)
                if column == 3:
                    index = self.model_table_users.index(index_in_page, column)
                    label_fullname = CustomLabelUserTable(content=user_data_row.data.fullName,
                                                          key='fullName', user_model=user_data_row, controller=self.controller, index_in_page=index_in_page)
                    self.table_result_users.table.current_hoverred_row.connect(label_fullname.on_hover_row_changed)
                    self.table_result_users.table.setIndexWidget(index, label_fullname)
                    self.table_result_users.table.setColumnWidth(column, 0.16 * self.widget_width)
                if column == 4:
                    index = self.model_table_users.index(index_in_page, column)
                    label_email = CustomLabelUserTable(content=user_data_row.data.email,
                                                          key='email', user_model=user_data_row, index_in_page=index_in_page)
                    self.table_result_users.table.current_hoverred_row.connect(label_email.on_hover_row_changed)
                    self.table_result_users.table.setIndexWidget(index, label_email)
                    self.table_result_users.table.setColumnWidth(column, 0.16 * self.widget_width)
                if column == 5:
                    index = self.model_table_users.index(index_in_page, column)
                    label_phone = CustomLabelUserTable(content=user_data_row.data.phone,
                                                          key='phone', user_model=user_data_row, index_in_page=index_in_page)
                    self.table_result_users.table.current_hoverred_row.connect(label_phone.on_hover_row_changed)
                    self.table_result_users.table.setIndexWidget(index, label_phone)
                    self.table_result_users.table.setColumnWidth(column, 0.08 * self.widget_width)

                if column == 6:
                    index = self.model_table_users.index(index_in_page, column)
                    widget_description = WidgetGroupName(list_group_name=user_data_row.data.rolesName, label_width=0.14 * self.widget_width,
                                                         key='rolesName', user_model=user_data_row, index_in_page=index_in_page)
                    self.table_result_users.table.current_hoverred_row.connect(widget_description.on_hover_row_changed)
                    self.table_result_users.table.setIndexWidget(index, widget_description)
                    self.table_result_users.table.setColumnWidth(column, 0.2 * self.widget_width)

                if column == 7:
                    model_index = self.model_table_users.index(index_in_page, column)
                    widget_status = WidgetStatus(controller=self.controller, index=model_index,
                                                 status=user_data_row.data.status, user_model=user_data_row)
                    self.table_result_users.table.setIndexWidget(model_index, widget_status)
                    self.table_result_users.table.setColumnWidth(column, 0.075 * self.widget_width)
                if column == 8:
                    model_index = self.model_table_users.index(index_in_page, column)
                    widget_button_action = WidgetButtonActions(controller=self.controller, index=model_index)
                    self.table_result_users.table.setIndexWidget(model_index, widget_button_action)
                    self.table_result_users.table.setColumnWidth(column, 0.15 * self.widget_width)

            if user_data_row.data.username is not None and user_data_row.data.username != '':
                item_username = CustomLabelItemUserTable(f"{user_data_row.data.username}")
            else:
                item_username = CustomLabelItemUserTable(f"-")

            # if user_data_row.data.fullName is not None and user_data_row.data.fullName != '':
            #     item_full_name = QStandardItem(f"{user_data_row.data.fullName}")
            #     item_full_name.setData(f"{user_data_row.data.fullName}", Config.ROLE_UNDERLINE)
            # else:
            #     item_full_name = QStandardItem(f"-")
            #     item_full_name.setData(f"-", Config.ROLE_UNDERLINE)
            #
            # if user_data_row.data.email is not None and user_data_row.data.email != '':
            #     item_email = QStandardItem(f"{user_data_row.data.email}")
            # else:
            #     item_email = QStandardItem(f"-")
            #
            # if user_data_row.data.phone is not None and user_data_row.data.phone != '':
            #     item_phone = QStandardItem(f"{user_data_row.data.phone}")
            # else:
            #     item_phone = QStandardItem(f"-")

            # if user_data_row.data.rolesName is not None and user_data_row.data.rolesName != '':
            #     item_user_group = QStandardItem(f"{user_data_row.data.rolesName}")
            # else:
            #     item_user_group = QStandardItem(f"-")

            # item_full_name.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            # item_full_name.setForeground(QBrush(QColor(Style.PrimaryColor.white_2)))

            # item_username.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            # item_username.setForeground(QBrush(QColor(Style.PrimaryColor.white_2)))
            # item_email.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            # item_email.setForeground(QBrush(QColor(Style.PrimaryColor.white_2)))
            # item_phone.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            # item_phone.setForeground(QBrush(QColor(Style.PrimaryColor.white_2)))
            # item_user_group.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            # item_user_group.setForeground(QBrush(QColor(Style.PrimaryColor.white_2)))

            self.model_table_users.setItem(index_in_page, 2, item_username)
            # self.model_table_users.setItem(index_in_page, 3, item_full_name)
            # self.model_table_users.setItem(index_in_page, 4, item_email)
            # self.model_table_users.setItem(index_in_page, 5, item_phone)
            # self.model_table_users.setItem(index_in_page, 6, item_user_group)

    def update_model_theme_color(self):
        pass

    def check_all(self, checked):
        for row in range(self.model_table_users.rowCount()):
            index = self.model_table_users.index(row, 0)
            item = self.table_result_users.table.indexWidget(index)
            if item is not None:
                item.checkbox.setCheckState(Qt.CheckState.Checked if checked else Qt.CheckState.Unchecked)

    def on_clicked_table(self, index=None, model_table=None, table=None, checkbox_all=None):
        index_find = model_table.index(index.row(), 0)
        item = table.indexWidget(index_find)
        all_checked = True
        all_unchecked = True
        if item is not None:
            if item.checkbox.checkState() == Qt.CheckState.Checked:
                item.checkbox.setCheckState(Qt.CheckState.Unchecked)
                for row in range(model_table.rowCount()):
                    index = model_table.index(row, 0)
                    item = table.indexWidget(index)
                    if item is not None:
                        if item.checkbox.checkState() != Qt.CheckState.Checked:
                            all_unchecked = False
                            break
                if checkbox_all.checkState() == Qt.CheckState.Checked:
                    if not all_unchecked:
                        self.is_programmatically_changing_combobox = False
                        checkbox_all.setCheckState(Qt.CheckState.Unchecked)
                        self.is_programmatically_changing_combobox = True
            else:
                item.checkbox.setCheckState(Qt.CheckState.Checked)
                for row in range(model_table.rowCount()):
                    index = model_table.index(row, 0)
                    item = table.indexWidget(index)
                    if item is not None:
                        if item.checkbox.checkState() != Qt.CheckState.Checked:
                            all_checked = False
                if all_checked:
                    checkbox_all.setCheckState(Qt.CheckState.Checked)

    def refresh_table(self):
        self.create_user_data(is_thread=False)

    def retranslate_table_user(self):
        self.model_table_users.clear()
        self.table_result_users.horizontal_label_list = ["", self.tr("NO"), self.tr("USERNAME"), self.tr("FULLNAME"), self.tr("EMAIL"), self.tr("PHONE NUMBER"), self.tr("GROUP"), self.tr("STATUS"), self.tr("ACTIONS")]
        self.create_model()

    def set_dynamic_stylesheet(self):
        
        self.checkbox_header.setStyleSheet(f"""
            QCheckBox::indicator:checked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                background-color: transparent;
                width: 16px;
                height: 16px;
            }}
            QCheckBox::indicator:unchecked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                background-color: transparent;
                width: 16px;
                height: 16px;
            }}
            """)
        
        horizontal_scroll_bar = self.table_result_users.table.horizontalScrollBar()
        horizontal_scroll_bar.setStyleSheet(f'''
            QScrollBar::horizontal {{
                background-color: transparent;
                height: 8px;
                width: 48px;
                margin: 0px 0px 0px 0px;
            }}
            QScrollBar::handle:horizontal {{
                background-color: #656475;
                border-radius: 4px;
                min-width: 8px;
                width: 48px;
            }}
            QScrollBar::add-line:horizontal {{
                background: none;
            }}
            QScrollBar::sub-line:horizontal {{
                background: none;
            }}
            QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
                background: none;
            }}
            QScrollBar::left-arrow:horizontal, QScrollBar::right-arrow:horizontal {{
                width: 0px;
                height: 0px;
                background: none;
            }}
        ''')
        self.table_result_users.set_dynamic_stylesheet()
        self.widget_indicator.setStyleSheet(
            f"""
            QWidget {{
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
            }}
        """)
        
        self.page_indicator.set_dynamic_stylesheet()

    def resize_ui_table_user(self, widget_width, widget_height):
        self.widget_width = widget_width
        self.widget_height = widget_height
        self.table_result_users.table.verticalHeader().setDefaultSectionSize(0.08 * self.widget_height)
        self.set_up_dimension_row()

class WidgetButtonActions(QWidget):
    def __init__(self, parent=None, index=None, controller: Controller = None):
        super().__init__(parent)
        self.setMouseTracking(True)
        self.controller = controller
        self.index = index
        key_value = '{row} - {current_page}'.format(
            row=self.index.row(), current_page=self.controller.current_user_table_page)

        self.btn_view_info = QPushButton()
        self.btn_view_info.setObjectName('btn_view_info')
        self.btn_view_info.setFixedSize(22, 22)
        self.btn_view_info.setIconSize(QSize(20, 20))
        self.btn_view_info.setToolTip('View')
        self.btn_view_info.clicked.connect(lambda: self.btn_view_info_clicked(index))

        self.btn_edit = QPushButton()
        self.btn_edit.setObjectName('btn_edit')
        self.btn_edit.setFixedSize(22, 22)
        self.btn_edit.setIconSize(QSize(20, 20))
        self.btn_edit.setToolTip('Edit')
        self.btn_edit.clicked.connect(lambda: self.btn_edit_clicked(index))

        self.btn_trash = QPushButton()
        self.btn_trash.setObjectName('btn_trash')
        self.btn_trash.setFixedSize(22, 22)
        self.btn_trash.setIconSize(QSize(20, 20))
        self.btn_trash.setToolTip('Remove')
        self.btn_trash.clicked.connect(lambda: self.btn_trash_clicked(index))

        self.event_clicked = None

        layout = QHBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.setSpacing(8)
        self.setLayout(layout)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        layout.addWidget(self.btn_view_info)
        layout.addWidget(self.btn_edit)
        layout.addWidget(self.btn_trash)
        self.set_dynamic_stylesheet()
        main_controller.theme_change_signal.connect(self.set_dynamic_stylesheet)

    def setHideDisabled(self, value):
        self.btn_edit.setDisabled(value)

    def setTrashDisabled(self, value):
        self.btn_trash.setDisabled(value)

    def btn_view_info_clicked(self, index):
        data = self.controller.user_data_filter[(self.controller.current_user_table_page - 1) * self.controller.total_user_items + index.row()]
        user_info = UserInformationDialog(parent=main_controller.list_parent['UsersTableView'], user_model=data, just_view_infor=True, controller=self.controller)
        user_info.exec()

    def btn_edit_clicked(self, index):
        data = self.controller.user_data_filter[
            (self.controller.current_user_table_page - 1) * self.controller.total_user_items + index.row()]

        user_info = UserInformationDialog(parent=main_controller.list_parent['UsersTableView'], user_model=data,
                                          just_view_infor=False, controller=self.controller)
        user_info.exec()

    def btn_trash_clicked(self, index):
        user_model = self.controller.user_data_filter[
            (self.controller.current_user_table_page - 1) * self.controller.total_user_items + index.row()]

        dialog = WarningDialog()
        result = dialog.exec()
        if result == QDialog.Accepted:
            self.controller.delete_single_user(user_id=user_model.data.id)
        elif result == QDialog.Rejected:
            pass

    def set_dynamic_stylesheet(self):
        self.btn_view_info.setStyleSheet(f"""
            QPushButton {{
                border: None;
                qproperty-icon: url({main_controller.get_theme_attribute("Image", "table_eye")});
            }}
            QPushButton:hover {{
                background-color: {main_controller.get_theme_attribute("Color", "table_action_background_hoverred")};
                border-radius: 4px;
                margin: 0px;
            }}
            QPushButton:pressed {{
                background-color: {main_controller.get_theme_attribute("Color", "table_action_background_hoverred")};
                border-radius: 4px;
                margin: 0.5px;
            }}
        """
        )
        
        self.btn_edit.setStyleSheet(f"""
            QPushButton {{
                border: None;
                qproperty-icon: url({main_controller.get_theme_attribute("Image", "table_edit")});
            }}
            QPushButton:hover {{
                background-color: {main_controller.get_theme_attribute("Color", "table_action_background_hoverred")};
                border-radius: 4px;
                margin: 0px;
            }}
            QPushButton:pressed {{
                background-color: {main_controller.get_theme_attribute("Color", "table_action_background_hoverred")};
                border-radius: 4px;
                margin: 0.5px;
            }}
        """
        )
        
        self.btn_trash.setStyleSheet(f"""
            QPushButton {{
                border: None;
                qproperty-icon: url({main_controller.get_theme_attribute("Image", "table_trash")});
            }}
            QPushButton:hover {{
                background-color: {main_controller.get_theme_attribute("Color", "table_action_background_hoverred")};
                border-radius: 4px;
                margin: 0px;
            }}
            QPushButton:pressed {{
                background-color: {main_controller.get_theme_attribute("Color", "table_action_background_hoverred")};
                border-radius: 4px;
                margin: 0.5px;
            }}
        """
        )

class WidgetStatus(QWidget):
    def __init__(self, parent=None, key=None, controller: Controller = None, status=None, user_model: UserModel = None, index = None):
        super().__init__(parent)
        self.status = status
        self.key = key
        self.user_model = user_model
        if self.user_model is not None:
            self.user_model.change_user_model_signal.connect(self.update_content)
        self.setMouseTracking(True)
        self.label_status = QLabel()
        self.load_ui()

    def load_ui(self):
        if self.status == 0:
            self.label_status.setText(self.tr('In-Active'))
        else:
            self.label_status.setText(self.tr('Active'))

        layout_label = QVBoxLayout()
        layout_label.setContentsMargins(0, 0, 0, 0)
        layout_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout_label.addWidget(self.label_status)

        self.widget_label = QWidget()
        self.widget_label.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.widget_label.setLayout(layout_label)
        self.widget_label.setMinimumWidth(64)
        if self.status == 0:
            self.widget_label.setStyleSheet(f'''
                        QWidget{{
                            border-radius: 4px;
                            color: white;
                            background-color: {Style.PrimaryColor.primary};
                            padding: 8px 4px
                        }}
                    ''')
        else:
            self.widget_label.setStyleSheet(f'''
                        QWidget{{
                            border-radius: 4px;
                            color: white;
                            background-color: #4C9008;
                            padding: 8px 4px
                        }}
                    ''')

        layout = QHBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.widget_label)
        self.setLayout(layout)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

    def update_content(self, data):
        key, value, model = data
        if key == 'status' and key == self.key:
            self.status = value
            if self.status == 0:
                self.label_status.setText(self.tr('In-Active'))
                self.widget_label.setStyleSheet(f'''
                                        QWidget{{
                                            border-radius: 4px;
                                            color: white;
                                            background-color: {Style.PrimaryColor.primary};
                                            padding: 4px 8px
                                        }}
                                    ''')
            else:
                self.label_status.setText(self.tr('Active'))
                self.widget_label.setStyleSheet(f'''
                                        QWidget{{
                                            border-radius: 4px;
                                            color: white;
                                            background-color: green;
                                            padding: 8px 4px
                                        }}
                                    ''')

class WidgetGroupName(QWidget):
    def __init__(self, parent=None, user_model: UserModel = None, key=None, controller: Controller = None, list_group_name=None, label_width=None, index_in_page=None):
        super().__init__(parent)
        self.list_group_name = list_group_name
        self.label_width = label_width
        self.index_in_page = index_in_page
        self.key = key
        self.user_model = user_model
        if self.user_model is not None:
            self.user_model.change_user_model_signal.connect(self.update_content)
        self.setMouseTracking(False)
        main_controller.theme_change_signal.connect(self.set_dynamic_stylesheet)
        self.load_ui()
        self.set_dynamic_stylesheet()

    def load_ui(self):
        if self.list_group_name is not None and self.list_group_name != '':
            message = self.list_group_name
        else:
            message = "-"
        self.label_list_group_name = QLabel(message)
        self.label_list_group_name.setToolTip(self.list_group_name)
        self.label_list_group_name.toolTip()
        self.label_list_group_name.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_list_group_name.setWordWrap(True)
        # self.label_list_group_name.setStyleSheet(f'color: {Style.PrimaryColor.white_2}')
        if self.label_width is not None:
            self.label_list_group_name.setFixedWidth(self.label_width)

        layout_label = QVBoxLayout()
        layout_label.setContentsMargins(8, 4, 8, 4)
        layout_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout_label.addWidget(self.label_list_group_name)
        widget_label = QWidget()
        widget_label.setLayout(layout_label)
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(widget_label)

        # self.setStyleSheet(f"""
        #             QToolTip {{
        #                 background-color: {Style.PrimaryColor.background};
        #                 color: {Style.PrimaryColor.white_2};
        #                 border: 1px solid black;
        #                 padding: 5px;
        #                 font-size: 14px;
        #                 font-family: Arial, sans-serif;
        #             }}
        #         """)

        self.setLayout(layout)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

    def resizeEvent(self, event):
        self.label_list_group_name.setWordWrap(True)

    def on_hover_row_changed(self, row):
        self.label_list_group_name.setStyleSheet(f'color: {main_controller.get_theme_attribute("Color", "text_color_all_app")}')

    def update_content(self, data):
        key, value, model = data
        if key == 'rolesName' and key == self.key:
            self.list_group_name = value
            self.label_list_group_name.setText(value)

    def set_dynamic_stylesheet(self):
        self.label_list_group_name.setStyleSheet(f'color: {main_controller.get_theme_attribute("Color", "text_color_all_app")}')
        self.setStyleSheet(f"""
            QToolTip {{
                background-color: {Style.PrimaryColor.background};
                color: {Style.PrimaryColor.white_2};
                border: 1px solid black;
                padding: 5px;
                font-size: 14px;
                font-family: Arial, sans-serif;
            }}
        """)

class CustomLabelItemUserTable(QStandardItem):
    def __init__(self, text=None):
        super().__init__(text)
        main_controller.theme_change_signal.connect(self.set_dynamic_stylesheet)
        self.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.set_dynamic_stylesheet()

    def set_dynamic_stylesheet(self):
        self.setForeground(QBrush(QColor(main_controller.get_theme_attribute("Color", "text_color_all_app"))))

class CustomLabelUserTable(QWidget):
    def __init__(self, parent=None, user_model: UserModel = None, controller: Controller = None, content=None, key=None, index_in_page=None):
        super().__init__(parent)
        self.content = content
        self.key = key
        self.user_model = user_model
        self.controller = controller
        self.index_in_page = index_in_page
        if self.user_model is not None:
            self.user_model.change_user_model_signal.connect(self.update_content)
        main_controller.theme_change_signal.connect(self.set_dynamic_stylesheet)
        self.setMouseTracking(True)
        self.load_ui()
        self.set_dynamic_stylesheet()

    def load_ui(self):
        if self.content is not None and self.content != '':
            message = self.content
        else:
            message = "-"
        self.label_content = QLabel(message)
        self.label_content.setToolTip(self.content)
        self.label_content.toolTip()
        self.label_content.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_content.setWordWrap(True)
        # self.label_content.setStyleSheet(f'color: {Style.PrimaryColor.white_2}')
        # if self.label_width is not None:
        #     self.label_list_group_name.setFixedWidth(self.label_width)

        layout_label = QVBoxLayout()
        layout_label.setContentsMargins(8, 4, 8, 4)
        layout_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        layout_label.addWidget(self.label_content)
        widget_label = QWidget()
        widget_label.setLayout(layout_label)
        widget_label.mousePressEvent = self.on_click_label
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(widget_label)

        # self.setStyleSheet(f"""
        #             QToolTip {{
        #                 background-color: {Style.PrimaryColor.background};
        #                 color: {Style.PrimaryColor.white_2};
        #                 border: 1px solid black;
        #                 padding: 5px;
        #                 font-size: 14px;
        #                 font-family: Arial, sans-serif;
        #             }}
        #             QLabel {{color: {Style.PrimaryColor.white_2}; }}
        #         """)

        self.setLayout(layout)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

    def update_content(self, data):
        key, value, model = data
        if key == 'fullName' and key == self.key:
            self.content = value
            self.label_content.setText(value)
        elif key == 'email' and key == self.key:
            self.content = value
            self.label_content.setText(value)
        elif key == 'phone' and key == self.key:
            self.content = value
            self.label_content.setText(value)

    def on_click_label(self, event):
        if self.key == "fullName":
            user_info = UserInformationDialog(parent=main_controller.list_parent['UsersTableView'], user_model=self.user_model,
                                              just_view_infor=False, controller=self.controller)
            user_info.exec()
        else:
            pass

    def on_hover_row_changed(self, row):
        # if self.index_in_page == row and main_controller.current_theme == Theme.LIGHT:
        #     self.label_content.setStyleSheet(f"QLabel {{color: {Style.PrimaryColor.white_2}; }}")
        # else:
        self.label_content.setStyleSheet(f"QLabel {{color: {self.label_content_color}; }}")

    def enterEvent(self, event):
        if self.key == 'fullName':
            self.label_content.setStyleSheet(f"QLabel {{ text-decoration: underline; color: {self.label_content_color_hoverred}; }}")

    def leaveEvent(self, event):
        if self.key == 'fullName':
            self.label_content.setStyleSheet(f"QLabel {{color: {self.label_content_color}; }}")

    def set_dynamic_stylesheet(self):
        self.label_content_color_hoverred = main_controller.get_theme_attribute("Color", "primary")
        self.label_content_color = main_controller.get_theme_attribute("Color", "text_color_all_app")
        self.label_content.setStyleSheet(f"QLabel {{color: {self.label_content_color}; }}")

        self.setStyleSheet(f"""
            QToolTip {{
                background-color: {Style.PrimaryColor.background};
                color: {Style.PrimaryColor.white_2};
                border: 1px solid black;
                padding: 5px;
                font-size: 14px;
                font-family: Arial, sans-serif;
            }}
            QLabel {{color: {Style.PrimaryColor.white_2}; }}
        """)
