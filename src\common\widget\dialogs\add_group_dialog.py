
import re
import copy
import logging
from functools import partial
from urllib.parse import urlparse
from .base_dialog import NewBaseDialog

from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QGuiApplication
from PySide6.QtSvgWidgets import QSvgWidget
from PySide6.QtWidgets import QScrollArea
from PySide6.QtWidgets import QWidget, QGridLayout, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QCheckBox, QDialog, QStackedWidget, QProgressDialog, QSizePolicy

from dataclasses import replace
from src.styles.style import Style
from src.utils.config import Config
from src.utils.theme_setting import theme_setting
from src.presentation.device_management_screen.widget.list_custom_widgets import SearchComboBox, InputText
from src.common.controller.main_controller import main_controller,connect_slot
from src.common.widget.search_widget.search_bar import SearchBar
from src.common.model.group_model import Group, GroupModel, group_model_manager
from src.common.model.camera_model import camera_model_manager, CameraModel
from src.common.widget.pagination.page_indicator.page_indicator import Pagination
from src.common.widget.elided_label import ElidedLabel
from src.common.model.door_model import DoorModel,door_model_manager
logger = logging.getLogger(__name__)

class RowItem(QWidget):
    def __init__(self, parent=None,idx = None,model = None):
        super().__init__(parent)
        self.idx = idx
        self.model = model
        self.list_groups = {}
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0,5,0,5)
        self.main_widget = QWidget()
        self.main_widget.setObjectName('RowItem')
        camera_layout = QGridLayout(self.main_widget)
        # camera_layout.setContentsMargins(0,0,0,0)
        self.checkbox = self.create_checkbox()
        self.label_idx = QLabel('-')
        self.label_device_name= ElidedLabel('-')
        self.label_device_name.setMaximumWidth(150)
        self.label_ip_address = ElidedLabel('-')
        self.label_ip_address.setMaximumWidth(150)
        self.label_mac_address = ElidedLabel('-')
        self.label_mac_address.setMaximumWidth(150)
        self.label_group = ElidedLabel('-')
        self.label_group.setMaximumWidth(150)
        camera_layout.addWidget(self.checkbox,0,0)
        camera_layout.addWidget(self.label_idx,0,1)
        camera_layout.addWidget(self.label_device_name,0,2,Qt.AlignmentFlag.AlignLeft)
        camera_layout.addWidget(self.label_ip_address,0,3,Qt.AlignmentFlag.AlignLeft)
        camera_layout.addWidget(self.label_mac_address,0,4,Qt.AlignmentFlag.AlignLeft)
        camera_layout.addWidget(self.label_group,0,5,Qt.AlignmentFlag.AlignLeft)
        camera_layout.setColumnStretch(0, 1)
        camera_layout.setColumnStretch(1, 1)
        camera_layout.setColumnStretch(2, 3)
        camera_layout.setColumnStretch(3, 3)
        camera_layout.setColumnStretch(4, 3)
        camera_layout.setColumnStretch(5, 3)
        self.main_layout.addWidget(self.main_widget)
        self.update_data()
        self.setStyleSheet(f"""           
            QWidget#RowItem {{
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                border-radius: 8px;
                border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
            }}
            QLabel {{
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                font: normal;
                font-size: {Style.Size.caption}px;
            }}

            """)
    def update_data(self):
        group_list = group_model_manager.get_group_list(self.model.get_property('server_ip'))
        self.label_idx.setText(str(self.idx))
        if isinstance(self.model,CameraModel):
            self.label_device_name.setText(self.model.get_property('name'))
            self.label_device_name.setToolTip(self.model.get_property('name'))
            if self.model.get_property("ipAddress") is not None:
                self.label_ip_address.setText(self.model.get_property("ipAddress"))
                self.label_ip_address.setToolTip(self.model.get_property("ipAddress"))
            if len(self.model.get_property("cameraGroupIds")) > 0:
                text = ''    
                for id in self.model.get_property("cameraGroupIds"):
                    if id in group_list:
                        if id == self.model.get_property("cameraGroupIds")[-1]:
                            text = text + \
                                group_list[id].get_property('name')
                        else:
                            text = text + \
                                group_list[id].get_property('name') + ', '
                            
                self.label_group.setText(text)
                self.label_group.setToolTip(text)
        elif  isinstance(self.model,GroupModel):   
            self.label_device_name.setText(self.model.get_property('name'))
            self.label_device_name.setToolTip(self.model.get_property('name'))
            self.label_ip_address.setText(self.model.get_property("ip"))
            self.label_ip_address.setToolTip(self.model.get_property("ip"))
            self.label_mac_address.setText(self.model.get_property("mac"))
            self.label_mac_address.setToolTip(self.model.get_property("mac"))
            if self.model.get_property("parentGroupId") is not None and len(self.model.get_property("parentGroupId")) > 0:
                text = ''    
                for id in self.model.get_property("parentGroupId"):
                    if id in group_list:
                        if id == self.model.get_property("parentGroupId")[-1]:
                            text = text + \
                                group_list[id].get_property('name')
                        else:
                            text = text + \
                                group_list[id].get_property('name') + ', '
                            
                self.label_group.setText(text)
                self.label_group.setToolTip(text)

    def create_checkbox(self):
        checkbox = QCheckBox(self, objectName='checkbox')
        checkbox.setStyleSheet(
            f"""
            QCheckBox {{
                background-color: transparent;
                border: none;
            }}
            QCheckBox::indicator:checked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                width: 16px;
                height: 16px;
            }}
            QCheckBox::indicator:unchecked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                width: 16px;
                height: 16px;
            }}
            """)
        checkbox.stateChanged.connect(self.checkbox_clicked)
        return checkbox
    
    def btn_edit_clicked(self):
        pass

    def checkbox_clicked(self,state):
        # logger.debug(f'state = {state}')
        if state == 2:
            main_controller.group_selected_signal.emit((True,self.model))
        else:
            main_controller.group_selected_signal.emit((False,self.model))

    def mousePressEvent(self,event):
        if self.checkbox.checkState() == Qt.Checked:
            self.checkbox.setCheckState(Qt.CheckState.Unchecked)
        else:
            self.checkbox.setCheckState(Qt.CheckState.Checked)

    def enterEvent(self, event):
        self.setStyleSheet(f"""           
            QWidget#RowItem {{
                background-color: {main_controller.get_theme_attribute("Color", "table_row_hoverred")};
                color: {main_controller.get_theme_attribute("Color", "primary")};
                border-radius: 8px;
                border: 1px solid {main_controller.get_theme_attribute("Color", "primary")};
            }}
            QLabel {{
                background-color: {main_controller.get_theme_attribute("Color", "table_row_hoverred")};
                color: {main_controller.get_theme_attribute("Color", "primary")};
                font: normal;
                font-size: {Style.Size.caption}px;
            }}
            """)
        
    def leaveEvent(self, event):
        self.setStyleSheet(f"""           
            QWidget#RowItem {{
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                border-radius: 8px;
                border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
            }}
            QLabel {{
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                font: normal;
                font-size: {Style.Size.caption}px;
            }}
            """)
class GroupTable(QWidget):
    search_device_signal = Signal(dict)
    def __init__(self, parent=None,group_model:GroupModel = None, data = None):
        super().__init__(parent)
        self.data = data
        self.data_filtered = self.data
        self.group_model = group_model
        self.list_groups = {}
        self.search_device_signal.connect(self.search_device_slot)
        main_layout = QVBoxLayout(self)
        header_widget = self.create_header_widget()
        self.scroll_area = self.create_scroll_area()
        self.scroll_area.setStyleSheet("border: none;")
        self.page_indicator = self.create_pagination()
        self.table = None
        main_layout.addWidget(header_widget)
        main_layout.addWidget(self.scroll_area)
        main_layout.addWidget(self.page_indicator)
        self.update_list_groups()
        self.update_table()
    #     self.connect_slot()
    # def connect_slot(self):
    #     connect_slot(
    #         (main_controller.group_selected_signal, self.group_selected_signal)
    #     )

    def update_list_groups(self):
        if self.group_model is not None:
            group_list = group_model_manager.get_group_list(server_ip=main_controller.current_controller.server.data.server_ip)
            camera_list = camera_model_manager.get_camera_list(server_ip=main_controller.current_controller.server.data.server_ip)
            if self.group_model.get_property("childGroupIds") is not None:
                for id in self.group_model.get_property("childGroupIds"):
                    if id in group_list:
                        self.list_groups[id] = group_list[id]
            if self.group_model.get_property("cameraIds") is not None:
                for id in self.group_model.get_property("cameraIds"):
                    if id in camera_list:
                        self.list_groups[id] = camera_list[id]
        
    def create_scroll_area(self):
        widget = QScrollArea()
        widget.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        widget.setWidgetResizable(True)
        widget.setStyleSheet(
            f'''   
                QScrollArea {{
                    border: none;  /* Ẩn border của QScrollArea */
                }}
                QScrollBar:vertical {{
                    background-color: {Style.PrimaryColor.background};
                    width: 2px;
                    margin: 0px 0px 0px 0px;
                }}
                QScrollBar::handle:vertical {{
                    background-color: {Style.PrimaryColor.on_background};
                    border-radius: 5px;
                    min-height: 20px;
                }}
                QScrollBar::add-line:vertical {{
                    background: none;
                }}
                QScrollBar::sub-line:vertical {{
                    background: none;
                }}
                QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                    background: none;
                }}
                QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {{
                    width: 0px;
                    height: 0px;
                    background: none;
                }}
            ''')
        return widget
    
    def create_header_widget(self):
        widget = QWidget()
        widget.setObjectName('HeaderItem')
        layout = QGridLayout(widget)
        self.checkbox = self.create_checkbox()
        layout.addWidget(self.checkbox,0,0)
        layout.addWidget(QLabel(self.tr('No')),0,1)
        layout.addWidget(QLabel(self.tr('Device name')),0,2,Qt.AlignmentFlag.AlignLeft)
        layout.addWidget(QLabel(self.tr('IP address')),0,3,Qt.AlignmentFlag.AlignLeft)
        layout.addWidget(QLabel(self.tr('Mac address')),0,4,Qt.AlignmentFlag.AlignLeft)
        layout.addWidget(QLabel(self.tr('Group')),0,5,Qt.AlignmentFlag.AlignLeft)
        layout.setColumnStretch(0, 1)
        layout.setColumnStretch(1, 1)
        layout.setColumnStretch(2, 3)
        layout.setColumnStretch(3, 3)
        layout.setColumnStretch(4, 3)
        layout.setColumnStretch(5, 3)
        self.setStyleSheet(f"""           
            QWidget#HeaderItem {{
                background-color: {main_controller.get_theme_attribute("Color", "main_border")};
                color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                border-radius: 8px;
                border: none;
            }}
            QLabel {{
                background-color: {main_controller.get_theme_attribute("Color", "main_border")};
                color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                font: bold;
                font-size: {Style.Size.caption}px;
            }}
            """)
        widget.mousePressEvent = self.item_clicked
        return widget
    
    def clear_scroll_area(self):
        # Lấy widget chứa tất cả các widget con bên trong scroll area
            # Kiểm tra xem widget có tồn tại không
        if self.table is not None:
            # Lấy layout của widget đó
            layout = self.table.layout()
            
            # Xóa tất cả các widget con trong layout
            if layout is not None:
                while layout.count():
                    item = layout.takeAt(0)
                    widget = item.widget()
                    if widget is not None:
                        widget.deleteLater()

    def update_table(self):
        widget = self.scroll_area.widget()
        if widget is not None:
            widget.deleteLater()
        self.table = QWidget()
        layout = QVBoxLayout(self.table)
        layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        layout.setContentsMargins(0,0,0,0)
        layout.setSpacing(0)
        min_index = (self.page_indicator.current_page - 1) * self.page_indicator.rows_per_page
        max_index = min_index + self.page_indicator.rows_per_page
        for idx,model in enumerate(self.data_filtered):
            if idx >= min_index and idx < max_index :
                widget = RowItem(idx =idx + 1,model = model)
                if model.get_property('id') in self.list_groups:
                    widget.checkbox.setCheckState(Qt.CheckState.Checked)
                layout.addWidget(widget)
        self.scroll_area.setWidget(self.table)

    def create_pagination(self):
        page_indicator = Pagination(rows_per_page=7)
        page_indicator.signal_update_table.connect(self.signal_update_table)
        if page_indicator is not None:
            page_indicator.set_total_rows_and_total_pages(len(self.data_filtered))
        return page_indicator
    
    def signal_update_table(self, data):
        # logger.debug(f'data = {data}')
        self.update_table()

    def create_checkbox(self):
        checkbox = QCheckBox(self, objectName='checkbox')
        checkbox.setStyleSheet(
            f"""
            QCheckBox {{
                background-color: transparent;
                border: none;
            }}
            QCheckBox::indicator:checked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                width: 16px;
                height: 16px;
            }}
            QCheckBox::indicator:unchecked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                width: 16px;
                height: 16px;
            }}
            """)
        checkbox.stateChanged.connect(self.checkbox_clicked)
        return checkbox
    
    def checkbox_clicked(self,state):
        if state == 2:
            # self.list_groups = {}
            for model in self.data_filtered:
                self.list_groups[model.get_property('id')] = model
            for i in range(self.table.layout().count()):
                widget = self.table.layout().itemAt(i).widget()
                # logger.debug(f'camera_widget_clicked = {widget}')
                if widget is not None and hasattr(widget,'checkbox_clicked'):
                    if widget.checkbox.checkState() == Qt.CheckState.Unchecked:
                        widget.checkbox.setCheckState(Qt.CheckState.Checked)
            main_controller.group_selected_signal.emit((True,None))
        else:
            
            for i in range(self.table.layout().count()):
                widget = self.table.layout().itemAt(i).widget()
                # logger.debug(f'camera_widget_clicked = {widget}')
                if widget is not None and hasattr(widget,'checkbox_clicked'):
                    if widget.checkbox.checkState() == Qt.CheckState.Checked:
                        widget.checkbox.setCheckState(Qt.CheckState.Unchecked)
                        # del self.list_groups[widget.model.data.id]
            for model in self.data_filtered:
                if model.get_property('id') in self.list_groups:
                    del self.list_groups[model.get_property('id')]
            # self.list_groups = {}
            main_controller.group_selected_signal.emit((False,None))
    
    def item_clicked(self,event):
        if self.checkbox.checkState() == Qt.Checked:
            self.checkbox.setCheckState(Qt.CheckState.Unchecked)
        else:
            self.checkbox.setCheckState(Qt.CheckState.Checked)

    def search_device_slot(self,data):
        text = data['text'].lower()
        device_type = data['device_type']
        self.data_filtered = []
        text_filtered = []
        for model in self.data:
            if text in model.get_property('name').lower():
                text_filtered.append(model)
                continue
            if isinstance(model,CameraModel):
                if model.get_property("ipAddress") is not None and text in model.get_property("ipAddress").lower():
                    text_filtered.append(model)
            elif isinstance(model,GroupModel):
                if model.get_property("ip") is not None and text in model.get_property("ip").lower():
                    text_filtered.append(model)

        if device_type == 0:
            for model in text_filtered:
                self.data_filtered.append(model)
        elif device_type == 1:
            for model in text_filtered:
                if isinstance(model,CameraModel):
                    self.data_filtered.append(model)
        elif device_type == 2:
            for model in text_filtered:
                if isinstance(model,GroupModel):
                    self.data_filtered.append(model)
        elif device_type == 3:
            for model in text_filtered:
                if isinstance(model,DoorModel):
                    self.data_filtered.append(model)

        if self.page_indicator is not None:
            self.page_indicator.set_total_rows_and_total_pages(len(self.data_filtered))
        self.update_table()



    def search_items(self, text):
        self.data_filtered = []
        for model in self.data:
            if text in model.get_property('name'):
                self.data_filtered.append(model)
                continue
            if model.get_property("ipAddress") is not None and text in model.get_property("ipAddress"):
                self.data_filtered.append(model)
        if self.page_indicator is not None:
            self.page_indicator.set_total_rows_and_total_pages(len(self.data_filtered))
        self.update_table()

class AddGroupDialog(NewBaseDialog):

    def __init__(self, parent=None,title = 'ADD GROUP', group_model:GroupModel = None, is_add_dialog=False):
        self.title = title
        self.group_model = group_model
        self.is_add_dialog = is_add_dialog
        self.data = None
        self.table = None
        self.number_group = 0
        screen = QGuiApplication.primaryScreen()
        self.desktop_screen_size = screen.availableGeometry()

        self.style_sheet_active_button = f'''
            QPushButton{{
                background-color: {Style.PrimaryColor.button_second_background};
                padding: 4px 16px 4px 16px;
                color: {Style.PrimaryColor.text_on_primary};
                border: None;
                
                border-top-left-radius: 4px;  /* Set top-left border radius */
                border-top-right-radius: 4px;
            }}
        '''
        self.style_sheet_inactive_button = f'''
            QPushButton{{
                background-color: transparent;
                padding: 4px 16px 4px 16px;
                
                color: {Style.PrimaryColor.text_not_select};
                border: None;
            }}
        '''
        widget_main = self.load_ui()
        # title = self.tr("Add Group")
        super().__init__(parent, title=self.tr(self.title), content_widget=widget_main, width_dialog=Config.WIDTH_DIALOG_MEDIUM, min_height_dialog=705)
        self.setObjectName("AddCameraDialog")
        self.update_style()
        self.update_data()
        self.connect_slot()

    def connect_slot(self):
        connect_slot(
            (main_controller.group_selected_signal, self.group_selected_signal),
            (self.save_update_signal,self.save_clicked)
        )

    def update_data(self):
        if self.group_model is not None:
            self.group_name.line_edit.setText(self.group_model.get_property('name'))
            self.number_group = 0 
            if self.group_model.get_property("childGroupIds") is not None:
                self.number_group += len(self.group_model.get_property("childGroupIds"))
            if self.group_model.get_property("cameraIds") is not None:
                self.number_group += len(self.group_model.get_property("cameraIds"))
            self.label_number_group.setText(str(self.number_group))

    def load_ui(self):
        widget_main = QWidget()
        widget_main.setFixedWidth(Config.WIDTH_DIALOG_MEDIUM)
        self.layout_dialog = QVBoxLayout()
        self.layout_dialog.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_dialog.setContentsMargins(0, 0, 0, 0)
        self.layout_dialog.setSpacing(0)
        # self.layout_dialog.addWidget(self.content_widget)
        self.group_info_widget = self.create_group_info_widget()
        # self.scan_widget = self.create_scan_widget()
        self.layout_dialog.addWidget(self.group_info_widget)
        self.group_table_widget = self.create_group_table_widget()
        self.layout_dialog.addWidget(self.group_table_widget)

        widget_main.setLayout(self.layout_dialog)
        return widget_main
    
    def create_group_info_widget(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        h_layout = QHBoxLayout()
        self.group_name = InputText(title=self.tr('Group Name'))
        h_layout.addWidget(self.group_name)

        h_control_layout = QHBoxLayout()
        h_right_widget = QWidget()
        h_right_widget.setStyleSheet("border: none;")
        h_right_layout = QHBoxLayout(h_right_widget)
        
        h_right_widget_inside = QWidget()
        h_right_widget_inside.setFixedHeight(32)
        h_right_widget_inside.setStyleSheet(f"""
            QWidget{{
                background-color: {main_controller.get_theme_attribute("Color", "primary", 0.15)};
                border-radius: 16px;
                border: 1px solid {main_controller.get_theme_attribute("Color", "primary")};
                color: {main_controller.get_theme_attribute("Color", "primary")};
            }}
        """)
        h_right_layout_inside = QHBoxLayout(h_right_widget_inside)
        # h_right_layout.setContentsMargins(10,5,5,5)
        label_selected = QLabel(self.tr("Selected:"))
        label_selected.setStyleSheet("border: none;background-color: transparent;")
        self.label_number_group = QLabel()
        self.label_number_group.setStyleSheet("border: none;background-color: transparent;")
        h_right_layout_inside.setAlignment(Qt.AlignmentFlag.AlignRight)
        h_right_layout_inside.addWidget(label_selected)
        h_right_layout_inside.addWidget(self.label_number_group)
        h_right_layout.addStretch(1)
        h_right_layout.addWidget(h_right_widget_inside)
        
        h_left_layout = QHBoxLayout()
        self.search_group = SearchBar(hide_title=True)
        self.search_group.search_bar.setPlaceholderText(self.tr("Search"))
        self.search_group.search_bar.setFixedHeight(36)
        self.search_group.search_items_signal.connect(self.search_items)

        data = [
            self.tr('All'),
            self.tr('Camera'),
            self.tr('AIBox'),
            self.tr('Door'),
        ]
        self.search_device_type = SearchComboBox(title=self.tr('Device Type'), data=data,
                                        combobox_clicked=self.search_devicetype_clicked)
        h_left_layout.addWidget(self.search_group)
        h_left_layout.addWidget(self.search_device_type)
        h_left_layout.setStretch(0,1)
        h_left_layout.setStretch(1,1)

        h_control_layout.addLayout(h_left_layout)
        h_control_layout.addWidget(h_right_widget)
        h_control_layout.setStretch(0,1)
        h_control_layout.setStretch(1,1)
        
        h_control_layout.setContentsMargins(4,0,4,0)
        layout.addLayout(h_layout)
        layout.addLayout(h_control_layout)
        return widget
    
    
    def create_group_table_widget(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        widget_no_data = QWidget()
        layout_no_data = QHBoxLayout(widget_no_data)
        layout_no_data.setAlignment(Qt.AlignmentFlag.AlignCenter)
        svg_widget = QSvgWidget()
        svg_widget.load(main_controller.get_theme_attribute("Image", "no_data_image"))
        self.label_know_address_no_data = QLabel(self.tr('No Data'))
        self.label_know_address_no_data.setStyleSheet(f'font-size: {Style.Size.body_large}px; font-weight: bold; color: {main_controller.get_theme_attribute("Color", "dialog_text")}')
        layout_no_data.addWidget(svg_widget)
        layout_no_data.addWidget(self.label_know_address_no_data)

        widget_searching = QWidget()
        layout_searching = QVBoxLayout(widget_searching)
        layout_searching.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label_searching = QLabel(self.tr("Searching"))
        layout_searching.addWidget(label_searching)
        
        group_list = group_model_manager.get_group_list(server_ip=main_controller.current_controller.server.data.server_ip)
        camera_list = camera_model_manager.get_camera_list(server_ip=main_controller.current_controller.server.data.server_ip)
        self.data = []
        for id, group_model in group_list.items():
            if group_model.get_property("type") == "AI_BOX" and group_model != self.group_model:
                self.data.append(group_model)
        for id, camera_model in camera_list.items():
            self.data.append(camera_model)
        # self.group_table = GroupTable(group_model=self.group_model, data = self.data)
        self.group_table = GroupTable(group_model=self.group_model, data = [] if self.is_add_dialog else self.data)
        self.widget_result_search = QStackedWidget()
        self.widget_result_search.setContentsMargins(0, 0, 0, 0)
        self.widget_result_search.setObjectName("stacked_result")
        self.widget_result_search.addWidget(widget_no_data)  # 0
        self.widget_result_search.addWidget(widget_searching)  # 1
        self.widget_result_search.addWidget(self.group_table)  # 2
        self.widget_result_search.setCurrentIndex(2)
        layout.addWidget(self.widget_result_search)

        return widget
    
    def update_style(self):
        self.setStyleSheet(
            f'''
                QWidget {{
                    background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                    border-left: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                    border-right: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                }}

                QLabel {{
                    color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                    border: none;
                }}
            
                QLabel:disabled {{
                    color: gray;
                    font-style: italic;
                }}
                
                QLineEdit:disabled {{
                    color: gray;
                    background-color: lightgray;
                }}
                
                QCheckBox:disabled {{
                    color: gray;
                }}
                QComboBox:disabled {{
                    color: gray;
                    background-color: lightgray;
                }}
                QLabel#warning_label {{
                    color: {Style.PrimaryColor.primary};
                    font-style: italic;
                }}
                QStackedWidget#stacked_result {{
                    border-radius: 8px; 
                    border: none;
                }}
                QCheckBox::indicator:checked {{
                    border: none;
                    image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                    background-color: transparent;
                    width: 16px;
                    height: 16px;
                }}
                QCheckBox::indicator:unchecked {{
                    border: none;
                    image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                    background-color: {Style.PrimaryColor.on_background};
                    width: 16px;
                    height: 16px;
                }}
                QComboBox {{
                   background-color: transparent; 
                   border-radius: 8px;
                   border: 1px solid {Style.PrimaryColor.border_line_edit};
                   color: {Style.PrimaryColor.border_line_edit}
                }}
                '''
        )

    def group_selected_signal(self,data):
        is_check, model = data
        if is_check:
            if model is None: 
                # case người dùng Checked vào checkbox all
                if self.data is not None:
                    self.number_group = len(self.group_table.list_groups)
                    self.label_number_group.setText(str(self.number_group))
            else:
                # case người dùng Checked vào checkbox trên row của table
                self.group_table.list_groups[model.get_property('id')] = model
                self.number_group = len(self.group_table.list_groups)
                self.label_number_group.setText(str(self.number_group))
        else:
            if model is None: 
                # case người dùng UnChecked vào checkbox all
                self.number_group = len(self.group_table.list_groups)
                self.label_number_group.setText(str(self.number_group))
            else:
                # case người dùng UnChecked vào checkbox trên row của table
                # self.number_group -= 1
                if model.get_property('id') in self.group_table.list_groups:
                    del self.group_table.list_groups[model.get_property('id')]
                self.number_group = len(self.group_table.list_groups)
                self.label_number_group.setText(str(self.number_group))
    
    def search_items(self, text):
        data = {"text": text,"device_type": self.search_device_type.combobox.currentIndex()}
        self.group_table.search_device_signal.emit(data)

    def search_devicetype_clicked(self, index):
        data = {"text": self.search_group.search_bar.text(),"device_type": index}
        self.group_table.search_device_signal.emit(data)


    def save_clicked(self):
        if self.group_name.line_edit.text() != '':
            list_ai_box = []
            list_camera = []
            for id,model in self.group_table.list_groups.items():
                if isinstance(model,GroupModel):
                    list_ai_box.append(id)
                else:
                    list_camera.append(id)
            if self.group_model is None:
                group = Group(name=self.group_name.line_edit.text())
                group.childGroupIds = list_ai_box
                group.cameraIds = list_camera
                main_controller.current_controller.create_camera_group(parent=main_controller.list_parent['DeviceScreen'], data=group)
            else:
                group = GroupModel(data=copy.deepcopy(self.group_model.data))
                group.set_property("name", self.group_name.line_edit.text())
                group.set_property("childGroupIds", list_ai_box)
                group.set_property("cameraIds", list_camera)
                main_controller.current_controller.update_camera_group_by_put(data=group.data)
            self.close()
        else:
            self.group_name.label_warning.setText(self.tr("Group name is required"))
            self.group_name.label_warning.show()
        # if self.result_scan_table.table is not None:
        #     data = []
        #     for i in range(self.result_scan_table.table.layout().count()):
        #         widget = self.result_scan_table.table.layout().itemAt(i).widget()
        #         # logger.debug(f'camera_widget_clicked = {widget}')
        #         if widget is not None and isinstance(widget,RowItem):
        #             # logger.debug(f'sssssssssssss = {widget.camera_info.name, widget.checkbox.checkState()}')
        #             if widget.checkbox.checkState() == Qt.CheckState.Checked:
        #                 data.append({"id":widget.camera_info.id,"name": widget.camera_info.name})
        #     logger.debug(f'sssssssssssss = {data}')
        #     if len(data) > 0:
        #         main_controller.current_controller.create_camera_in_group(parent = self,data = data)
